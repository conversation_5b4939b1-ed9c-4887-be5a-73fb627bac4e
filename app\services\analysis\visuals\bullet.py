from app.core import logger
from app.core.enums import ChartType, GeneralKeys as GS, WorkSheet as WS,  GeneralKeys as GS

class BulletChart:
    @staticmethod
    def check_bullet_chart(worksheet):
        try:
            style_rules = worksheet.findall(WS.STYLE_RULE.value)
            has_refline = any(rule.attrib.get("element") == GS.REFLINE.value for rule in style_rules)

            panes = worksheet.findall(WS.PANES_PANE.value)
            if len(panes) != 1:
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            refline_in_pane = any(pane.find(WS.REFERENCE_LINE.value) is not None for pane in panes)

            if refline_in_pane and has_refline:
                return {
                    GS.STATUS.value: True,
                    GS.CHART_TYPE.value: ChartType.BULLET_CHART.value
                }

            return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

        except Exception as e:
            logger.error(f"Unexpected error in check_bullet_chart: {e}")
            return {
                GS.STATUS.value: False,
                GS.CHART_TYPE.value: None,
                GS.ERROR.value: str(e)
            }
