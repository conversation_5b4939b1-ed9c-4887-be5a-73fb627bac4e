"""folder_id added in uploads_files_report_details table

Revision ID: 141da23008f3
Revises: 01b39de940b7
Create Date: 2025-06-23 18:04:42.184772

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '141da23008f3'
down_revision: Union[str, None] = '01b39de940b7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add the 'folder_id' column in the 'upload_files_report_details' table
    op.add_column('upload_files_report_details', sa.Column('folder_id', sa.UUID(as_uuid=True), nullable=False))
    
    # Create a foreign key relationship between 'upload_files_report_details' and 'folders' based on 'folder_id'
    op.create_foreign_key(None, 'upload_files_report_details', 'folders', ['folder_id'], ['id'])


def downgrade() -> None:
    op.drop_constraint(None, 'upload_files_report_details', type_='foreignkey')

    # Drop the 'folder_id' column in the 'upload_files_report_details' table
    op.drop_column('upload_files_report_details', 'folder_id')
