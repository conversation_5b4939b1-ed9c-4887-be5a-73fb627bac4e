{"config": "{\"version\":\"5.57\",\"themeCollection\":{\"baseTheme\":{\"name\":\"CY24SU08\",\"version\":\"5.58\",\"type\":2}},\"activeSectionIndex\":0,\"defaultDrillFilterOtherVisuals\":true,\"linguisticSchemaSyncVersion\":2,\"settings\":{\"useNewFilterPaneExperience\":true,\"allowChangeFilterTypes\":true,\"useStylableVisualContainerHeader\":true,\"queryLimitOption\":6,\"exportDataMode\":1,\"useDefaultAggregateDisplayName\":true,\"useEnhancedTooltips\":true,\"filterPaneHiddenInEditMode\":false},\"objects\":{\"section\":[{\"properties\":{\"verticalAlignment\":{\"expr\":{\"Literal\":{\"Value\":\"'Top'\"}}}}}],\"outspacePane\":[{\"properties\":{\"expanded\":{\"expr\":{\"Literal\":{\"Value\":\"false\"}}}}}]}}", "layoutOptimization": 0, "resourcePackages": [{"resourcePackage": {"disabled": false, "items": [{"name": "CY24SU08", "path": "BaseThemes/CY24SU08.json", "type": 202}], "name": "SharedResources", "type": 2}}], "sections": [{"config": "{}", "displayName": "Customer Rank", "displayOption": 1, "filters": "[]", "height": 720.0, "name": "a8b4344a0e1247518a96", "ordinal": 1, "visualContainers": [{"config": "{ \"name\": \"6c1e02c734c5401c88ce\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 0.0, \"y\": 0.0, \"z\": 0, \"width\": 1280, \"height\": 720, \"tabOrder\": 1001 } } ], \"singleVisual\": { \"visualType\": \"clusteredBarChart\", \"projections\": {\"Y\": [{\"queryRef\": \"Sum(Orders.Sales)\", \"active\": true}], \"Category\": [{\"queryRef\": \"Orders.Customer Name\", \"active\": true}], \"Tooltips\": [{\"queryRef\": \"Sum(Orders.Profit)\", \"active\": true}]}, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Customer Name\"}, \"Name\": \"Orders.Customer Name\", \"NativeReferenceName\": \"Customer Name\"}, {\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Sales\"}}, \"Function\": 0}, \"Name\": \"Sum(Orders.Sales)\", \"NativeReferenceName\": \"Sum of Sales\"}, {\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Profit\"}}, \"Function\": 0}, \"Name\": \"Sum(Orders.Profit)\", \"NativeReferenceName\": \"Sum of Profit\"}]}, \"drillFilterOtherVisuals\": true, \"hasDefaultSort\": true, \"objects\": {\"lineStyles\": [{\"properties\": {\"showMarker\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}}}], \"legend\": [{\"properties\": {\"position\": {\"expr\": {\"Literal\": {\"Value\": \"'Right'\"}}}}}], \"dataPoint\": [{\"properties\": {\"fill\": {\"solid\": {\"color\": {\"expr\": {\"FillRule\": {\"Input\": {\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Entity\": \"Orders\"}}, \"Property\": \"Sales\"}}, \"Function\": 0}}, \"FillRule\": {\"linearGradient3\": {\"min\": {\"color\": {\"Literal\": {\"Value\": \"'#AE123A'\"}}}, \"mid\": {\"color\": {\"Literal\": {\"Value\": \"'#D9D9D9'\"}}}, \"max\": {\"color\": {\"Literal\": {\"Value\": \"'#49525E'\"}}}, \"nullColoringStrategy\": {\"strategy\": {\"Literal\": {\"Value\": \"'asZero'\"}}}}}}}}}}}, \"selector\": {\"data\": [{\"dataViewWildcard\": {\"matchingOption\": 1}}]}}], \"labels\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}}}], \"valueAxis\": [{\"properties\": {\"switchAxisPosition\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}, \"labelPrecision\": {\"expr\": {\"Literal\": {\"Value\": \"1L\"}}}, \"showAxisTitle\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}}}], \"categoryAxis\": [{\"properties\": {\"switchAxisPosition\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}, \"labelPrecision\": {\"expr\": {\"Literal\": {\"Value\": \"1L\"}}}, \"showAxisTitle\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}}}]}, \"vcObjects\": { \"title\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"text\": {\"expr\": {\"Literal\": {\"Value\": \"'Customer Ranking'\"}}}, \"fontFamily\": {\"expr\": {\"Literal\": {\"Value\": \"'Calibri'\"}}}}}], \"background\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}], \"border\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}] } } }", "filters": "[]", "height": 720, "width": 1280, "x": 0.0, "y": 0.0, "z": 0}], "width": 1280.0}, {"config": "{}", "displayName": "Customer <PERSON>", "displayOption": 1, "filters": "[]", "height": 720.0, "name": "dc7e2a0d15bc4cf6ad9d", "ordinal": 2, "visualContainers": [{"config": "{\"name\":\"ead8c75fa0754e6eb4be\",\"layouts\":[{\"id\":0,\"position\":{\"x\":0.0,\"y\":0.0,\"z\":0,\"width\":1280,\"height\":720,\"tabOrder\":0}}],\"singleVisual\":{\"visualType\":\"scatterChart\",\"projections\":{\"X\":[{\"queryRef\": \"Sum(Orders.Sales)\", \"active\": true}],\"Category\":[{\"queryRef\": \"Orders.Customer Name\", \"active\": true}],\"Series\":[{\"queryRef\": \"Sum(Orders.Profit)\", \"active\": true}],\"Y\":[{\"queryRef\": \"Sum(Orders.Profit)\", \"active\": true}]},\"prototypeQuery\":{\"Version\":2,\"From\":[{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}],\"Select\":[{\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Sales\"}}, \"Function\": 0}, \"Name\": \"Sum(Orders.Sales)\", \"NativeReferenceName\": \"Sum of Sales\"}, {\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Profit\"}}, \"Function\": 0}, \"Name\": \"Sum(Orders.Profit)\", \"NativeReferenceName\": \"Sum of Profit\"}, {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Customer Name\"}, \"Name\": \"Orders.Customer Name\", \"NativeReferenceName\": \"Customer Name\"}]},\"drillFilterOtherVisuals\":true,\"objects\":{\"categoryLabels\":[{\"properties\":{\"show\":{\"expr\":{\"Literal\":{\"Value\":\"true\"}}}}}]}}}", "filters": "[]", "height": 720, "width": 1280, "x": 0.0, "y": 0.0, "z": 0}], "width": 1280.0}, {"config": "{}", "displayName": "Days to Ship", "displayOption": 1, "filters": "[]", "height": 720.0, "name": "61d50043f5e94e5c82e5", "ordinal": 3, "visualContainers": [{"config": "{\"name\":\"642effe3931e49eea815\",\"layouts\":[{\"id\":0,\"position\":{\"x\":0.0,\"y\":0.0,\"z\":0,\"width\":1280,\"height\":720,\"tabOrder\":0}}],\"singleVisual\":{\"visualType\":\"textbox\",\"drillFilterOtherVisuals\":true,\"objects\": {\"general\": [{\"properties\": {\"paragraphs\": [{\"textRuns\": [{\"value\": \"This Visual is not supported\", \"textStyle\": {\"fontFamily\": \"Calibri\", \"fontSize\": \"14pt\", \"textDecoration\": \"underline\", \"fontWeight\": \"bold\", \"fontStyle\": \"italic\"}}], \"horizontalTextAlignment\": \"center\"}]}}]},\"vcObjects\": {\"border\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}}}], \"background\": [{\"properties\": {\"color\": {\"solid\": {\"color\": {\"expr\": {\"Literal\": {\"Value\": \"'#ffffff'\"}}}}}}}], \"title\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}]}}}", "filters": "[]", "height": 720, "width": 1280, "x": 0.0, "y": 0.0, "z": 0}], "width": 1280.0}, {"config": "{}", "displayName": "Monthly Sales", "displayOption": 1, "filters": "[]", "height": 720.0, "name": "5d52e506075f4d79a9b6", "ordinal": 4, "visualContainers": [{"config": "{\"name\":\"7ca91ccaba954ff99a61\",\"layouts\":[{\"id\":0,\"position\":{\"x\":0.0,\"y\":0.0,\"z\":0,\"width\":1280,\"height\":720,\"tabOrder\":0}}],\"singleVisual\":{\"visualType\":\"areaChart\",\"projections\":{\"Category\":[{\"queryRef\": \"Orders.Order Date.Variation.Date Hierarchy.Year\", \"active\": true}, {\"queryRef\": \"Orders.Order Date.Variation.Date Hierarchy.Quarter\", \"active\": true}, {\"queryRef\": \"Orders.Order Date.Variation.Date Hierarchy.Month\", \"active\": true}],\"Y\":[{\"queryRef\": \"Sum(Orders.Sales)\", \"active\": true}],\"Series\":[{\"queryRef\": \"Orders.Order Profitable?\", \"active\": true}]},\"prototypeQuery\":{\"Version\":2,\"From\":[{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}],\"Select\":[{\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Sales\"}}, \"Function\": 0}, \"Name\": \"Sum(Orders.Sales)\", \"NativeReferenceName\": \"Sum of Sales\"}, {\"HierarchyLevel\": {\"Expression\": {\"Hierarchy\": {\"Expression\": {\"PropertyVariationSource\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Name\": \"Variation\", \"Property\": \"Order Date\"}}, \"Hierarchy\": \"Date Hierarchy\"}}, \"Level\": \"Year\"}, \"Name\": \"Orders.Order Date.Variation.Date Hierarchy.Year\", \"NativeReferenceName\": \"Order Date Year\"}, {\"HierarchyLevel\": {\"Expression\": {\"Hierarchy\": {\"Expression\": {\"PropertyVariationSource\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Name\": \"Variation\", \"Property\": \"Order Date\"}}, \"Hierarchy\": \"Date Hierarchy\"}}, \"Level\": \"Quarter\"}, \"Name\": \"Orders.Order Date.Variation.Date Hierarchy.Quarter\", \"NativeReferenceName\": \"Order Date Quarter\"}, {\"HierarchyLevel\": {\"Expression\": {\"Hierarchy\": {\"Expression\": {\"PropertyVariationSource\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Name\": \"Variation\", \"Property\": \"Order Date\"}}, \"Hierarchy\": \"Date Hierarchy\"}}, \"Level\": \"Month\"}, \"Name\": \"Orders.Order Date.Variation.Date Hierarchy.Month\", \"NativeReferenceName\": \"Order Date Month\"}, {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Order Profitable?\"}, \"Name\": \"Orders.Order Profitable?\", \"NativeReferenceName\": \"Order Profitable?\"}],\"OrderBy\":[]},\"drillFilterOtherVisuals\":true,\"objects\":{\"labels\":[{\"properties\":{\"show\":{\"expr\":{\"Literal\":{\"Value\":\"true\"}}}}}],\"seriesLabels\":[{\"properties\":{\"show\":{\"expr\":{\"Literal\":{\"Value\":\"true\"}}}}}]}}}", "filters": "[]", "height": 720, "width": 1280, "x": 0.0, "y": 0.0, "z": 0}], "width": 1280.0}, {"config": "{}", "displayName": "Monthly Segment", "displayOption": 1, "filters": "[]", "height": 720.0, "name": "908f04f9a0a74780a00e", "ordinal": 5, "visualContainers": [{"config": "{\"name\":\"5b5df5328afc4e119984\",\"layouts\":[{\"id\":0,\"position\":{\"x\":0.0,\"y\":0.0,\"z\":0,\"width\":1280,\"height\":720,\"tabOrder\":0}}],\"singleVisual\":{\"visualType\":\"textbox\",\"drillFilterOtherVisuals\":true,\"objects\": {\"general\": [{\"properties\": {\"paragraphs\": [{\"textRuns\": [{\"value\": \"This Visual is not supported\", \"textStyle\": {\"fontFamily\": \"Calibri\", \"fontSize\": \"14pt\", \"textDecoration\": \"underline\", \"fontWeight\": \"bold\", \"fontStyle\": \"italic\"}}], \"horizontalTextAlignment\": \"center\"}]}}]},\"vcObjects\": {\"border\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}}}], \"background\": [{\"properties\": {\"color\": {\"solid\": {\"color\": {\"expr\": {\"Literal\": {\"Value\": \"'#ffffff'\"}}}}}}}], \"title\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}]}}}", "filters": "[]", "height": 720, "width": 1280, "x": 0.0, "y": 0.0, "z": 0}], "width": 1280.0}, {"config": "{}", "displayName": "Product Detail Sheet", "displayOption": 1, "filters": "[]", "height": 720.0, "name": "5d2e15b4d7c444b88c87", "ordinal": 6, "visualContainers": [{"config": "{ \"name\": \"2bb9ed4ae660e7a710b4\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 0.0, \"y\": 0.0, \"z\": 0, \"width\": 1280, \"height\": 720, \"tabOrder\": 6000 } } ], \"singleVisual\": { \"visualType\": \"tableEx\", \"projections\": { \"Values\": [{\"queryRef\": \"Orders.Order ID\"}, {\"queryRef\": \"Orders.Customer Name\"}, {\"queryRef\": \"Orders.Customer ID\"}, {\"queryRef\": \"Orders.Region\"}, {\"queryRef\": \"Orders.Category\"}, {\"queryRef\": \"Orders.Segment\"}, {\"queryRef\": \"Orders.State/Province\"}, {\"queryRef\": \"Orders.Order Date.Variation.Date Hierarchy.Year\"}, {\"queryRef\": \"Orders.Order Date.Variation.Date Hierarchy.Quarter\"}, {\"queryRef\": \"Orders.Order Date.Variation.Date Hierarchy.Month\"}, {\"queryRef\": \"Orders.Ship Mode\"}] }, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Order ID\"}, \"Name\": \"Orders.Order ID\", \"NativeReferenceName\": \"Order ID\"}, {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Customer Name\"}, \"Name\": \"Orders.Customer Name\", \"NativeReferenceName\": \"Customer Name\"}, {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Customer ID\"}, \"Name\": \"Orders.Customer ID\", \"NativeReferenceName\": \"Customer ID\"}, {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Region\"}, \"Name\": \"Orders.Region\", \"NativeReferenceName\": \"Region\"}, {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Category\"}, \"Name\": \"Orders.Category\", \"NativeReferenceName\": \"Category\"}, {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Segment\"}, \"Name\": \"Orders.Segment\", \"NativeReferenceName\": \"Segment\"}, {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"State/Province\"}, \"Name\": \"Orders.State/Province\", \"NativeReferenceName\": \"State/Province\"}, {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Order Date\"}, \"Name\": \"Orders.Order Date.Variation.Date Hierarchy.Year\", \"NativeReferenceName\": \"Order Date Year\"}, {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Order Date\"}, \"Name\": \"Orders.Order Date.Variation.Date Hierarchy.Quarter\", \"NativeReferenceName\": \"Order Date\"}, {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Order Date\"}, \"Name\": \"Orders.Order Date.Variation.Date Hierarchy.Month\", \"NativeReferenceName\": \"Order Date Month\"}, {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Ship Mode\"}, \"Name\": \"Orders.Ship Mode\", \"NativeReferenceName\": \"Ship Mode\"}] }, \"drillFilterOtherVisuals\": true, \"vcObjects\": {\"title\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"text\": {\"expr\": {\"Literal\": {\"Value\": \"'Product Detail Sheet'\"}}}, \"fontFamily\": {\"expr\": {\"Literal\": {\"Value\": \"'Calibri'\"}}}}}], \"background\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}], \"border\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}]} } }", "filters": "[]", "height": 720, "width": 1280, "x": 0.0, "y": 0.0, "z": 0}], "width": 1280.0}, {"config": "{}", "displayName": "Product view", "displayOption": 1, "filters": "[]", "height": 720.0, "name": "bed677cea3e142cb9306", "ordinal": 7, "visualContainers": [{"config": "{\"name\":\"51f77871584e4e8a872b\",\"layouts\":[{\"id\":0,\"position\":{\"x\":0.0,\"y\":0.0,\"z\":0,\"width\":1280,\"height\":720,\"tabOrder\":0}}],\"singleVisual\":{\"visualType\":\"pivotTable\",\"projections\":{\"Rows\": [{\"queryRef\": \"Orders.Category\", \"active\": true}, {\"queryRef\": \"Orders.Order Date.Variation.Date Hierarchy.Year\", \"active\": true}], \"Columns\": [{\"queryRef\": \"Orders.Order Date.Variation.Date Hierarchy.Month\", \"active\": true}], \"Values\": [{\"queryRef\": \"Sum(Orders.Sales)\", \"active\": true}]},\"prototypeQuery\":{\"Version\":2,\"From\":[{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}],\"Select\":[{\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Category\"}, \"Name\": \"Orders.Category\", \"NativeReferenceName\": \"Category\"}, {\"HierarchyLevel\": {\"Expression\": {\"Hierarchy\": {\"Expression\": {\"PropertyVariationSource\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Name\": \"Variation\", \"Property\": \"Order Date\"}}, \"Hierarchy\": \"Date Hierarchy\"}}, \"Level\": \"Year\"}, \"Name\": \"Orders.Order Date.Variation.Date Hierarchy.Year\", \"NativeReferenceName\": \"Order Date Year\"}, {\"HierarchyLevel\": {\"Expression\": {\"Hierarchy\": {\"Expression\": {\"PropertyVariationSource\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Name\": \"Variation\", \"Property\": \"Order Date\"}}, \"Hierarchy\": \"Date Hierarchy\"}}, \"Level\": \"Month\"}, \"Name\": \"Orders.Order Date.Variation.Date Hierarchy.Month\", \"NativeReferenceName\": \"Order Date Month\"}, {\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Sales\"}}, \"Function\": 0}, \"Name\": \"Sum(Orders.Sales)\", \"NativeReferenceName\": \"Sum of Sales\"}]},\"drillFilterOtherVisuals\":true,\"objects\":{\"values\":[ { \"properties\": { \"backColor\": { \"solid\": { \"color\": { \"expr\": { \"FillRule\": { \"Input\": {\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Entity\": \"Orders\"}}, \"Property\": \"Sales\"}}, \"Function\": 0}}, \"FillRule\": { \"linearGradient3\": { \"min\": { \"color\": { \"Literal\": { \"Value\": \"'#B9DDF1'\" } } }, \"mid\": { \"color\": { \"Literal\": { \"Value\": \"'#6798C1'\" } } }, \"max\": { \"color\": { \"Literal\": { \"Value\": \"'#2A5783'\" } } }, \"nullColoringStrategy\": { \"strategy\": { \"Literal\": { \"Value\": \"'asZero'\" } } } } } } } } } } }, \"selector\": { \"data\": [ { \"dataViewWildcard\": { \"matchingOption\": 1 } } ], \"metadata\": \"Sum(Orders.Sales)\" } }, { \"properties\": { \"bandedRowHeaders\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } }, \"fontSize\": { \"expr\": { \"Literal\": { \"Value\": \"13D\" } } } } } ],\"columnHeaders\":[{\"properties\":{\"wordWrap\":{\"expr\":{\"Literal\":{\"Value\":\"true\"}}},\"fontSize\":{\"expr\":{\"Literal\":{\"Value\":\"10D\"}}},\"fontFamily\":{\"expr\":{\"Literal\":{\"Value\":\"'Calibri'\"}}}}}],\"subTotals\":[{\"properties\":{\"columnSubtotals\":{\"expr\":{\"Literal\":{\"Value\":\"true\"}}},\"rowSubtotals\":{\"expr\":{\"Literal\":{\"Value\":\"false\"}}}}}],\"rowHeaders\":[{\"properties\":{\"fontSize\":{\"expr\":{\"Literal\":{\"Value\":\"13D\"}}},\"fontFamily\":{\"expr\":{\"Literal\":{\"Value\":\"'Calibri'\"}}}}}],\"grid\":[{\"properties\":{\"outlineColor\":{\"solid\":{\"color\":{\"expr\":{\"Literal\":{\"Value\":\"'#CCCCCC'\"}}}}}}}],\"columnTotal\":[{\"properties\":{\"fontFamily\":{\"expr\":{\"Literal\":{\"Value\":\"'Calibri'\"}}}}}],\"rowTotal\":[{\"properties\":{\"fontFamily\":{\"expr\":{\"Literal\":{\"Value\":\"'Calibri'\"}}}}}]}, \"vcObjects\": { \"title\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"text\": {\"expr\": {\"Literal\": {\"Value\": \"'Sales by Product Category'\"}}}, \"fontFamily\": {\"expr\": {\"Literal\": {\"Value\": \"'Calibri'\"}}}}}], \"background\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}], \"border\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}] }}}", "filters": "[]", "height": 720, "width": 1280, "x": 0.0, "y": 0.0, "z": 0}], "width": 1280.0}, {"config": "{}", "displayName": "ProductDetails", "displayOption": 1, "filters": "[]", "height": 720.0, "name": "9f82ae261950462abf27", "ordinal": 8, "visualContainers": [{"config": "{\"name\":\"5c564b8b99de4a0d95a3\",\"layouts\":[{\"id\":0,\"position\":{\"x\":0.0,\"y\":0.0,\"z\":0,\"width\":1280,\"height\":720,\"tabOrder\":0}}],\"singleVisual\":{\"visualType\":\"textbox\",\"drillFilterOtherVisuals\":true,\"objects\": {\"general\": [{\"properties\": {\"paragraphs\": [{\"textRuns\": [{\"value\": \"This Visual is not supported\", \"textStyle\": {\"fontFamily\": \"Calibri\", \"fontSize\": \"14pt\", \"textDecoration\": \"underline\", \"fontWeight\": \"bold\", \"fontStyle\": \"italic\"}}], \"horizontalTextAlignment\": \"center\"}]}}]},\"vcObjects\": {\"border\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}}}], \"background\": [{\"properties\": {\"color\": {\"solid\": {\"color\": {\"expr\": {\"Literal\": {\"Value\": \"'#ffffff'\"}}}}}}}], \"title\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}]}}}", "filters": "[]", "height": 720, "width": 1280, "x": 0.0, "y": 0.0, "z": 0}], "width": 1280.0}, {"config": "{}", "displayName": "Quarterly Sales", "displayOption": 1, "filters": "[]", "height": 720.0, "name": "03b617b36d304932adaa", "ordinal": 9, "visualContainers": [{"config": "{\"name\":\"66ce309ddc024eaead54\",\"layouts\":[{\"id\":0,\"position\":{\"x\":0.0,\"y\":0.0,\"z\":0,\"width\":1280,\"height\":720,\"tabOrder\":0}}],\"singleVisual\":{\"visualType\":\"areaChart\",\"projections\":{\"Category\":[{\"queryRef\": \"Orders.Order Date.Variation.Date Hierarchy.Year\", \"active\": true}, {\"queryRef\": \"Orders.Order Date.Variation.Date Hierarchy.Quarter\", \"active\": true}],\"Y\":[{\"queryRef\": \"Sum(Orders.Sales)\", \"active\": true}],\"Series\":[{\"queryRef\": \"Orders.Order Profitable?\", \"active\": true}]},\"prototypeQuery\":{\"Version\":2,\"From\":[{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}],\"Select\":[{\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Sales\"}}, \"Function\": 0}, \"Name\": \"Sum(Orders.Sales)\", \"NativeReferenceName\": \"Sum of Sales\"}, {\"HierarchyLevel\": {\"Expression\": {\"Hierarchy\": {\"Expression\": {\"PropertyVariationSource\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Name\": \"Variation\", \"Property\": \"Order Date\"}}, \"Hierarchy\": \"Date Hierarchy\"}}, \"Level\": \"Year\"}, \"Name\": \"Orders.Order Date.Variation.Date Hierarchy.Year\", \"NativeReferenceName\": \"Order Date Year\"}, {\"HierarchyLevel\": {\"Expression\": {\"Hierarchy\": {\"Expression\": {\"PropertyVariationSource\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Name\": \"Variation\", \"Property\": \"Order Date\"}}, \"Hierarchy\": \"Date Hierarchy\"}}, \"Level\": \"Quarter\"}, \"Name\": \"Orders.Order Date.Variation.Date Hierarchy.Quarter\", \"NativeReferenceName\": \"Order Date Quarter\"}, {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Order Profitable?\"}, \"Name\": \"Orders.Order Profitable?\", \"NativeReferenceName\": \"Order Profitable?\"}],\"OrderBy\":[]},\"drillFilterOtherVisuals\":true,\"objects\":{\"labels\":[{\"properties\":{\"show\":{\"expr\":{\"Literal\":{\"Value\":\"true\"}}}}}],\"seriesLabels\":[{\"properties\":{\"show\":{\"expr\":{\"Literal\":{\"Value\":\"true\"}}}}}]}}}", "filters": "[]", "height": 720, "width": 1280, "x": 0.0, "y": 0.0, "z": 0}], "width": 1280.0}, {"config": "{}", "displayName": "Sale Map", "displayOption": 1, "filters": "[]", "height": 720.0, "name": "172cfc36446c4c1a913f", "ordinal": 10, "visualContainers": [{"config": "{ \"name\": \"d09111e421ff44fc844a\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 0.0, \"y\": 0.0, \"z\": 0, \"width\": 1280, \"height\": 720, \"tabOrder\": 1 } } ], \"singleVisual\": { \"visualType\": \"filledMap\", \"projections\": {\"Category\": [{\"queryRef\": \"Orders.Country/Region\", \"active\": true}, {\"queryRef\": \"Orders.State/Province\", \"active\": true}], \"Tooltips\": [{\"queryRef\": \"Sum(Orders.Sales)\", \"active\": true}]}, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Country/Region\"}, \"Name\": \"Orders.Country/Region\", \"NativeReferenceName\": \"Country/Region\"}, {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"State/Province\"}, \"Name\": \"Orders.State/Province\", \"NativeReferenceName\": \"State/Province\"}, {\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Sales\"}}, \"Function\": 0}, \"Name\": \"Sum(Orders.Sales)\", \"NativeReferenceName\": \"Sum of Sales\"}] }, \"drillFilterOtherVisuals\": true, \"objects\": {\"mapStyles\": [{\"properties\": {\"mapTheme\": {\"expr\": {\"Literal\": {\"Value\": \"'canvasLight'\"}}}, \"showLabels\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}}}], \"dataPoint\": [{\"properties\": {\"fill\": {\"solid\": {\"color\": {\"expr\": {\"FillRule\": {\"Input\": {\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Entity\": \"Orders\"}}, \"Property\": \"Sales\"}}, \"Function\": 0}}, \"FillRule\": {\"linearGradient3\": {\"min\": {\"color\": {\"Literal\": {\"Value\": \"'#9E3D22'\"}}}, \"mid\": {\"color\": {\"Literal\": {\"Value\": \"'#D9D5C9'\"}}}, \"max\": {\"color\": {\"Literal\": {\"Value\": \"'#2B5C88'\"}}}, \"nullColoringStrategy\": {\"strategy\": {\"Literal\": {\"Value\": \"'asZero'\"}}}}}}}}}}}, \"selector\": {\"data\": [{\"dataViewWildcard\": {\"matchingOption\": 1}}]}}], \"mapControls\": [{\"properties\": {\"autoZoom\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"showZoomButtons\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}, \"showLassoButton\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}]}, \"vcObjects\": { \"title\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"text\": {\"expr\": {\"Literal\": {\"Value\": \"'Sales by Geography'\"}}}, \"fontFamily\": {\"expr\": {\"Literal\": {\"Value\": \"'Calibri'\"}}}}}], \"background\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}], \"border\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}] } } }", "filters": "[]", "height": 720, "width": 1280, "x": 0.0, "y": 0.0, "z": 0}], "width": 1280.0}, {"config": "{}", "displayName": "Segment Sales", "displayOption": 1, "filters": "[]", "height": 720.0, "name": "6464cb34226542e48efe", "ordinal": 11, "visualContainers": [{"config": "{ \"name\": \"5c382697062c4a4faebe\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 0.0, \"y\": 0.0, \"z\": 0, \"width\":1280, \"height\": 720, \"tabOrder\": 0 } } ], \"singleVisual\": { \"visualType\": \"lineChart\", \"projections\": {\"Y\": [{\"queryRef\": \"Sum(Orders.Sales)\", \"active\": true}], \"Category\": [{\"queryRef\": \"Orders.Ship Date.Variation.Date Hierarchy.Year\", \"active\": true}, {\"queryRef\": \"Orders.Ship Date.Variation.Date Hierarchy.Quarter\", \"active\": true}], \"Series\": [{\"queryRef\": \"Orders.Segment\", \"active\": true}]}, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Sales\"}}, \"Function\": 0}, \"Name\": \"Sum(Orders.Sales)\", \"NativeReferenceName\": \"Sum of Sales\"}, {\"HierarchyLevel\": {\"Expression\": {\"Hierarchy\": {\"Expression\": {\"PropertyVariationSource\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Name\": \"Variation\", \"Property\": \"Ship Date\"}}, \"Hierarchy\": \"Date Hierarchy\"}}, \"Level\": \"Year\"}, \"Name\": \"Orders.Ship Date.Variation.Date Hierarchy.Year\", \"NativeReferenceName\": \"Ship Date Year\"}, {\"HierarchyLevel\": {\"Expression\": {\"Hierarchy\": {\"Expression\": {\"PropertyVariationSource\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Name\": \"Variation\", \"Property\": \"Ship Date\"}}, \"Hierarchy\": \"Date Hierarchy\"}}, \"Level\": \"Quarter\"}, \"Name\": \"Orders.Ship Date.Variation.Date Hierarchy.Quarter\", \"NativeReferenceName\": \"Ship Date Quarter\"}, {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Segment\"}, \"Name\": \"Orders.Segment\", \"NativeReferenceName\": \"Segment\"}] }, \"drillFilterOtherVisuals\": true, \"objects\": {\"lineStyles\": [{\"properties\": {\"showMarker\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}}}], \"legend\": [{\"properties\": {\"position\": {\"expr\": {\"Literal\": {\"Value\": \"'Right'\"}}}}}], \"labels\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}}}], \"categoryAxis\": [{\"properties\": {\"switchAxisPosition\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}, \"labelPrecision\": {\"expr\": {\"Literal\": {\"Value\": \"1L\"}}}, \"showAxisTitle\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}}}], \"valueAxis\": [{\"properties\": {\"switchAxisPosition\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}, \"labelPrecision\": {\"expr\": {\"Literal\": {\"Value\": \"1L\"}}}, \"showAxisTitle\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}}}]}, \"vcObjects\": { \"background\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}], \"title\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"text\": {\"expr\": {\"Literal\": {\"Value\": \"'Segment Sales'\"}}}, \"fontFamily\": {\"expr\": {\"Literal\": {\"Value\": \"'Calibri'\"}}}}}], \"border\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}] } } }", "filters": "[]", "height": 720, "width": 1280, "x": 0.0, "y": 0.0, "z": 0}], "width": 1280.0}, {"config": "{}", "displayName": "Shipping Trend", "displayOption": 1, "filters": "[]", "height": 720.0, "name": "80ead4f0b3c3459cb613", "ordinal": 12, "visualContainers": [{"config": "{\"name\":\"e6007f7e7b0a4c9a8876\",\"layouts\":[{\"id\":0,\"position\":{\"x\":0.0,\"y\":0.0,\"z\":0,\"width\":1280,\"height\":720,\"tabOrder\":0}}],\"singleVisual\":{\"visualType\":\"areaChart\",\"projections\":{\"Category\":[{\"queryRef\": \"Orders.Order Date.Variation.Date Hierarchy.Year\", \"active\": true}, {\"queryRef\": \"Orders.Order Date.Variation.Date Hierarchy.Quarter\", \"active\": true}, {\"queryRef\": \"Orders.Order Date.Variation.Date Hierarchy.Month\", \"active\": true}],\"Y\":[{\"queryRef\": \"Sum(Orders.Sales)\", \"active\": true}],\"Series\":[]},\"prototypeQuery\":{\"Version\":2,\"From\":[{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}],\"Select\":[{\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Sales\"}}, \"Function\": 0}, \"Name\": \"Sum(Orders.Sales)\", \"NativeReferenceName\": \"Sum of Sales\"}, {\"HierarchyLevel\": {\"Expression\": {\"Hierarchy\": {\"Expression\": {\"PropertyVariationSource\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Name\": \"Variation\", \"Property\": \"Order Date\"}}, \"Hierarchy\": \"Date Hierarchy\"}}, \"Level\": \"Year\"}, \"Name\": \"Orders.Order Date.Variation.Date Hierarchy.Year\", \"NativeReferenceName\": \"Order Date Year\"}, {\"HierarchyLevel\": {\"Expression\": {\"Hierarchy\": {\"Expression\": {\"PropertyVariationSource\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Name\": \"Variation\", \"Property\": \"Order Date\"}}, \"Hierarchy\": \"Date Hierarchy\"}}, \"Level\": \"Quarter\"}, \"Name\": \"Orders.Order Date.Variation.Date Hierarchy.Quarter\", \"NativeReferenceName\": \"Order Date Quarter\"}, {\"HierarchyLevel\": {\"Expression\": {\"Hierarchy\": {\"Expression\": {\"PropertyVariationSource\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Name\": \"Variation\", \"Property\": \"Order Date\"}}, \"Hierarchy\": \"Date Hierarchy\"}}, \"Level\": \"Month\"}, \"Name\": \"Orders.Order Date.Variation.Date Hierarchy.Month\", \"NativeReferenceName\": \"Order Date Month\"}],\"OrderBy\":[]},\"drillFilterOtherVisuals\":true,\"objects\":{\"labels\":[{\"properties\":{\"show\":{\"expr\":{\"Literal\":{\"Value\":\"true\"}}}}}],\"seriesLabels\":[{\"properties\":{\"show\":{\"expr\":{\"Literal\":{\"Value\":\"true\"}}}}}]}}}", "filters": "[]", "height": 720, "width": 1280, "x": 0.0, "y": 0.0, "z": 0}], "width": 1280.0}, {"config": "{}", "displayName": "Total Sales", "displayOption": 1, "filters": "[]", "height": 720.0, "name": "4b0092ba6d89409fb4c6", "ordinal": 13, "visualContainers": [{"config": "{ \"name\": \"f0a3f30ddb7b4a6bbb96\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 0.0, \"y\":0.0, \"z\": 0.0, \"width\": 1280, \"height\": 50, \"tabOrder\": 1 } } ], \"singleVisual\": { \"visualType\": \"textbox\", \"drillFilterOtherVisuals\": true, \"objects\": { \"general\": [ { \"properties\": { \"paragraphs\": [ { \"textRuns\": [ { \"value\": \"Executive Overview - Profitability\", \"textStyle\": {\"fontSize\": \"18pt\", \"fontFamily\": \"Calibri\"} } ], \"horizontalTextAlignment\": \"1\" } ] } } ] }, \"vcObjects\": { \"border\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}], \"background\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}] } } }", "filters": "[]", "x": 0.0, "y": 0.0, "width": 1280, "height": 50, "z": 0.0}, {"config": "{ \"name\": \"b41399909c1a401f81ff\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 0.0, \"y\": 50.0, \"z\": 0.0, \"width\": 205.0, \"height\": 80, \"tabOrder\": 1 } } ], \"singleVisual\": { \"visualType\": \"card\", \"projections\": {\"Values\": [{\"queryRef\": \"Sum(Orders.Sales)\", \"active\": true}]}, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Sales\"}}, \"Function\": 0}, \"Name\": \"Sum(Orders.Sales)\", \"NativeReferenceName\": \"Sum of Sales\"}] }, \"drillFilterOtherVisuals\": true, \"objects\": {\"categoryLabels\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}], \"labels\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"bold\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"fontSize\": {\"expr\": {\"Literal\": {\"Value\": \"12D\"}}}}}]}, \"vcObjects\": { \"title\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"text\": {\"expr\": {\"Literal\": {\"Value\": \"'Sales'\"}}}, \"fontFamily\": {\"expr\": {\"Literal\": {\"Value\": \"'Calibri'\"}}}, \"alignment\": {\"expr\": {\"Literal\": {\"Value\": \"'center'\"}}}}}] } } }", "filters": "[]", "x": 0.0, "y": 50.0, "width": 205.0, "height": 80, "z": 0.0}, {"config": "{ \"name\": \"0fab9bd9c803493094e5\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 215.0, \"y\": 50.0, \"z\": 0.0, \"width\": 205.0, \"height\": 80, \"tabOrder\": 1 } } ], \"singleVisual\": { \"visualType\": \"card\", \"projections\": {\"Values\": [{\"queryRef\": \"Sum(Orders.Profit)\", \"active\": true}]}, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Profit\"}}, \"Function\": 0}, \"Name\": \"Sum(Orders.Profit)\", \"NativeReferenceName\": \"Sum of Profit\"}] }, \"drillFilterOtherVisuals\": true, \"objects\": {\"categoryLabels\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}], \"labels\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"bold\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"fontSize\": {\"expr\": {\"Literal\": {\"Value\": \"12D\"}}}}}]}, \"vcObjects\": { \"title\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"text\": {\"expr\": {\"Literal\": {\"Value\": \"'Profit'\"}}}, \"fontFamily\": {\"expr\": {\"Literal\": {\"Value\": \"'Calibri'\"}}}, \"alignment\": {\"expr\": {\"Literal\": {\"Value\": \"'center'\"}}}}}] } } }", "filters": "[]", "x": 215.0, "y": 50.0, "width": 205.0, "height": 80, "z": 0.0}, {"config": "{ \"name\": \"0cd784bd3a374bac8a85\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 430.0, \"y\": 50.0, \"z\": 0.0, \"width\": 205.0, \"height\": 80, \"tabOrder\": 1 } } ], \"singleVisual\": { \"visualType\": \"card\", \"projections\": {\"Values\": [{\"queryRef\": \"Sum(Orders.Profit per Order)\", \"active\": true}]}, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Profit per Order\"}}, \"Function\": 0}, \"Name\": \"Sum(Orders.Profit per Order)\", \"NativeReferenceName\": \"Profit per Order\"}] }, \"drillFilterOtherVisuals\": true, \"objects\": {\"categoryLabels\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}], \"labels\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"bold\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"fontSize\": {\"expr\": {\"Literal\": {\"Value\": \"12D\"}}}}}]}, \"vcObjects\": { \"title\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"text\": {\"expr\": {\"Literal\": {\"Value\": \"'Profit per Order'\"}}}, \"fontFamily\": {\"expr\": {\"Literal\": {\"Value\": \"'Calibri'\"}}}, \"alignment\": {\"expr\": {\"Literal\": {\"Value\": \"'center'\"}}}}}] } } }", "filters": "[]", "x": 430.0, "y": 50.0, "width": 205.0, "height": 80, "z": 0.0}, {"config": "{ \"name\": \"06046fe78181490bad3b\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 645.0, \"y\": 50.0, \"z\": 0.0, \"width\": 205.0, \"height\": 80, \"tabOrder\": 1 } } ], \"singleVisual\": { \"visualType\": \"card\", \"projections\": {\"Values\": [{\"queryRef\": \"Max(Orders.Discount)\", \"active\": true}]}, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Discount\"}}, \"Function\": 4}, \"Name\": \"Max(Orders.Discount)\", \"NativeReferenceName\": \"Discount\"}] }, \"drillFilterOtherVisuals\": true, \"objects\": {\"categoryLabels\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}], \"labels\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"bold\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"fontSize\": {\"expr\": {\"Literal\": {\"Value\": \"12D\"}}}}}]}, \"vcObjects\": { \"title\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"text\": {\"expr\": {\"Literal\": {\"Value\": \"'Discount'\"}}}, \"fontFamily\": {\"expr\": {\"Literal\": {\"Value\": \"'Calibri'\"}}}, \"alignment\": {\"expr\": {\"Literal\": {\"Value\": \"'center'\"}}}}}] } } }", "filters": "[]", "x": 645.0, "y": 50.0, "width": 205.0, "height": 80, "z": 0.0}, {"config": "{ \"name\": \"4280b060dd184d4b87e9\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 860.0, \"y\": 50.0, \"z\": 0.0, \"width\": 205.0, \"height\": 80, \"tabOrder\": 1 } } ], \"singleVisual\": { \"visualType\": \"card\", \"projections\": {\"Values\": [{\"queryRef\": \"Sum(Orders.Quantity)\", \"active\": true}]}, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Quantity\"}}, \"Function\": 0}, \"Name\": \"Sum(Orders.Quantity)\", \"NativeReferenceName\": \"Sum of Quantity\"}] }, \"drillFilterOtherVisuals\": true, \"objects\": {\"categoryLabels\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}], \"labels\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"bold\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"fontSize\": {\"expr\": {\"Literal\": {\"Value\": \"12D\"}}}}}]}, \"vcObjects\": { \"title\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"text\": {\"expr\": {\"Literal\": {\"Value\": \"'Quantity'\"}}}, \"fontFamily\": {\"expr\": {\"Literal\": {\"Value\": \"'Calibri'\"}}}, \"alignment\": {\"expr\": {\"Literal\": {\"Value\": \"'center'\"}}}}}] } } }", "filters": "[]", "x": 860.0, "y": 50.0, "width": 205.0, "height": 80, "z": 0.0}, {"config": "{ \"name\": \"9a771c359370470ca506\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 1075.0, \"y\": 50.0, \"z\": 0.0, \"width\": 205.0, \"height\": 80, \"tabOrder\": 1 } } ], \"singleVisual\": { \"visualType\": \"card\", \"projections\": {\"Values\": [{\"queryRef\": \"Average(Orders.Discount)\", \"active\": true}]}, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Discount\"}}, \"Function\": 1}, \"Name\": \"Average(Orders.Discount)\", \"NativeReferenceName\": \"Discount\"}] }, \"drillFilterOtherVisuals\": true, \"objects\": {\"categoryLabels\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}], \"labels\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"bold\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"fontSize\": {\"expr\": {\"Literal\": {\"Value\": \"12D\"}}}}}]}, \"vcObjects\": { \"title\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"text\": {\"expr\": {\"Literal\": {\"Value\": \"'Discount'\"}}}, \"fontFamily\": {\"expr\": {\"Literal\": {\"Value\": \"'Calibri'\"}}}, \"alignment\": {\"expr\": {\"Literal\": {\"Value\": \"'center'\"}}}}}] } } }", "filters": "[]", "x": 1075.0, "y": 50.0, "width": 205.0, "height": 80, "z": 0.0}], "width": 1280.0}, {"config": "{}", "displayName": "Customer Analysis", "displayOption": 1, "filters": "[]", "height": 720.0, "name": "7092c6d4ceb146abbb0d", "visualContainers": [{"config": "{ \"name\": \"0b6734ec715d4782b2a9\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 0.0, \"y\":0.0, \"z\": 0, \"width\": 1277, \"height\": 50, \"tabOrder\": 1 } } ], \"singleVisual\": { \"visualType\": \"textbox\", \"drillFilterOtherVisuals\": true, \"objects\": { \"general\": [ { \"properties\": { \"paragraphs\": [ { \"textRuns\": [ { \"value\": \"Customer Analysis\", \"textStyle\": {\"fontSize\": \"18pt\", \"fontFamily\": \"Calibri\"} } ], \"horizontalTextAlignment\": \"left\" } ] } } ] }, \"vcObjects\": { \"border\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}], \"background\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}] } } }", "height": 50, "width": 1277, "x": 0.0, "y": 0.0, "z": 0}, {"config": "{ \"name\": \"9be49dc4882c40d39475\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 1146.3552, \"y\": 317.2752, \"z\": 0.0, \"width\": 123.5968, \"height\": 101.5272, \"tabOrder\": 5000 } } ], \"singleVisual\": { \"visualType\": \"slicer\", \"projections\": { \"Values\": [ { \"queryRef\": \"Orders.Category\", \"active\": true } ] }, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Category\"}, \"Name\": \"Orders.Category\", \"NativeReferenceName\": \"Category\"}] }, \"drillFilterOtherVisuals\": true, \"hasDefaultSort\": true, \"objects\": { \"data\": [ { \"properties\": { \"mode\": { \"expr\": { \"Literal\": { \"Value\": \"'None'\" } } } } } ], \"header\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } } } } ], \"items\": [ { \"properties\": { \"outlineStyle\": { \"expr\": { \"Literal\": { \"Value\": \"0D\" } } } } } ], \"general\": [ { \"properties\": { \"outlineWeight\": { \"expr\": { \"Literal\": { \"Value\": \"1D\" } } } } } ] }, \"vcObjects\": { \"title\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"true\" } } }, \"text\": { \"expr\": { \"Literal\": { \"Value\": \"'Category'\" } } } } } ], \"background\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } }, \"color\": { \"solid\": { \"color\": { \"expr\": { \"ThemeDataColor\": { \"ColorId\": 0, \"Percent\": 0 } } } } } } } ], \"visualHeader\": [ { \"properties\": { \"border\": { \"solid\": { \"color\": { \"expr\": { \"Literal\": { \"Value\": \"'#252423'\" } } } } }, \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } } } } ], \"border\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"true\" } } }, \"color\": { \"solid\": { \"color\": { \"expr\": { \"Literal\": { \"Value\": \"'None'\" } } } } } } } ] } } }", "filters": "[]", "height": 101.5272, "width": 123.5968, "x": 1146.3552, "y": 317.2752, "z": 0.0}, {"config": "{ \"name\": \"2c546520dd014b8aa70c\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 1147.136, \"y\": 424.7208, \"z\": 0.0, \"width\": 123.5968, \"height\": 101.5272, \"tabOrder\": 5000 } } ], \"singleVisual\": { \"visualType\": \"slicer\", \"projections\": { \"Values\": [ { \"queryRef\": \"Orders.Segment\", \"active\": true } ] }, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Segment\"}, \"Name\": \"Orders.Segment\", \"NativeReferenceName\": \"Segment\"}] }, \"drillFilterOtherVisuals\": true, \"hasDefaultSort\": true, \"objects\": { \"data\": [ { \"properties\": { \"mode\": { \"expr\": { \"Literal\": { \"Value\": \"'None'\" } } } } } ], \"header\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } } } } ], \"items\": [ { \"properties\": { \"outlineStyle\": { \"expr\": { \"Literal\": { \"Value\": \"0D\" } } } } } ], \"general\": [ { \"properties\": { \"outlineWeight\": { \"expr\": { \"Literal\": { \"Value\": \"1D\" } } } } } ] }, \"vcObjects\": { \"title\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"true\" } } }, \"text\": { \"expr\": { \"Literal\": { \"Value\": \"'Segment'\" } } } } } ], \"background\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } }, \"color\": { \"solid\": { \"color\": { \"expr\": { \"ThemeDataColor\": { \"ColorId\": 0, \"Percent\": 0 } } } } } } } ], \"visualHeader\": [ { \"properties\": { \"border\": { \"solid\": { \"color\": { \"expr\": { \"Literal\": { \"Value\": \"'#252423'\" } } } } }, \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } } } } ], \"border\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"true\" } } }, \"color\": { \"solid\": { \"color\": { \"expr\": { \"Literal\": { \"Value\": \"'None'\" } } } } } } } ] } } }", "filters": "[]", "height": 101.5272, "width": 123.5968, "x": 1147.136, "y": 424.7208, "z": 0.0}, {"config": "{ \"name\": \"e27dc0ca51cc4d95b953\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 1143.2704, \"y\": 253.8216, \"z\": 0.0, \"width\": 123.5968, \"height\": 59.2272, \"tabOrder\": 5000 } } ], \"singleVisual\": { \"visualType\": \"slicer\", \"projections\": { \"Values\": [ { \"queryRef\": \"Orders.Order Date.Variation.Date Hierarchy.Year\", \"active\": true } ] }, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"HierarchyLevel\": {\"Expression\": {\"Hierarchy\": {\"Expression\": {\"PropertyVariationSource\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Name\": \"Variation\", \"Property\": \"Order Date\"}}, \"Hierarchy\": \"Date Hierarchy\"}}, \"Level\": \"Year\"}, \"Name\": \"Orders.Order Date.Variation.Date Hierarchy.Year\", \"NativeReferenceName\": \"Order Date Year\"}] }, \"drillFilterOtherVisuals\": true, \"hasDefaultSort\": true, \"objects\": { \"data\": [ { \"properties\": { \"mode\": { \"expr\": { \"Literal\": { \"Value\": \"'Dropdown'\" } } } } } ], \"header\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } } } } ], \"items\": [ { \"properties\": { \"outlineStyle\": { \"expr\": { \"Literal\": { \"Value\": \"0D\" } } } } } ], \"general\": [ { \"properties\": { \"outlineWeight\": { \"expr\": { \"Literal\": { \"Value\": \"1D\" } } } } } ] }, \"vcObjects\": { \"title\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"true\" } } }, \"text\": { \"expr\": { \"Literal\": { \"Value\": \"'Order Date'\" } } } } } ], \"background\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } }, \"color\": { \"solid\": { \"color\": { \"expr\": { \"ThemeDataColor\": { \"ColorId\": 0, \"Percent\": 0 } } } } } } } ], \"visualHeader\": [ { \"properties\": { \"border\": { \"solid\": { \"color\": { \"expr\": { \"Literal\": { \"Value\": \"'#252423'\" } } } } }, \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } } } } ], \"border\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"true\" } } }, \"color\": { \"solid\": { \"color\": { \"expr\": { \"Literal\": { \"Value\": \"'None'\" } } } } } } } ] } } }", "filters": "[]", "height": 59.2272, "width": 123.5968, "x": 1143.2704, "y": 253.8216, "z": 0.0}, {"config": "{\"name\":\"0f4ceb147d0146a989c6\",\"layouts\":[{\"id\":0,\"position\":{\"x\":14.6816,\"y\":60.9192,\"z\":0.0,\"width\":583.2192,\"height\":654.0048,\"tabOrder\":0}}],\"singleVisual\":{\"visualType\":\"scatterChart\",\"projections\":{\"X\":[{\"queryRef\": \"Sum(Orders.Sales)\", \"active\": true}],\"Category\":[{\"queryRef\": \"Orders.Customer Name\", \"active\": true}],\"Series\":[{\"queryRef\": \"Sum(Orders.Profit)\", \"active\": true}],\"Y\":[{\"queryRef\": \"Sum(Orders.Profit)\", \"active\": true}]},\"prototypeQuery\":{\"Version\":2,\"From\":[{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}],\"Select\":[{\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Sales\"}}, \"Function\": 0}, \"Name\": \"Sum(Orders.Sales)\", \"NativeReferenceName\": \"Sum of Sales\"}, {\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Profit\"}}, \"Function\": 0}, \"Name\": \"Sum(Orders.Profit)\", \"NativeReferenceName\": \"Sum of Profit\"}, {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Customer Name\"}, \"Name\": \"Orders.Customer Name\", \"NativeReferenceName\": \"Customer Name\"}]},\"drillFilterOtherVisuals\":true,\"objects\":{\"categoryLabels\":[{\"properties\":{\"show\":{\"expr\":{\"Literal\":{\"Value\":\"true\"}}}}}]}}}", "filters": "[]", "height": 654.0048, "width": 583.2192, "x": 14.6816, "y": 60.9192, "z": 0.0}, {"config": "{ \"name\": \"dd9613f2d3954dbca711\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 607.936, \"y\": 63.4536, \"z\": 0.0, \"width\": 532.2368, \"height\": 651.4704, \"tabOrder\": 1001 } } ], \"singleVisual\": { \"visualType\": \"clusteredBarChart\", \"projections\": {\"Y\": [{\"queryRef\": \"Sum(Orders.Sales)\", \"active\": true}], \"Category\": [{\"queryRef\": \"Orders.Customer Name\", \"active\": true}], \"Tooltips\": [{\"queryRef\": \"Sum(Orders.Profit)\", \"active\": true}]}, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Customer Name\"}, \"Name\": \"Orders.Customer Name\", \"NativeReferenceName\": \"Customer Name\"}, {\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Sales\"}}, \"Function\": 0}, \"Name\": \"Sum(Orders.Sales)\", \"NativeReferenceName\": \"Sum of Sales\"}, {\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Profit\"}}, \"Function\": 0}, \"Name\": \"Sum(Orders.Profit)\", \"NativeReferenceName\": \"Sum of Profit\"}]}, \"drillFilterOtherVisuals\": true, \"hasDefaultSort\": true, \"objects\": {\"lineStyles\": [{\"properties\": {\"showMarker\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}}}], \"legend\": [{\"properties\": {\"position\": {\"expr\": {\"Literal\": {\"Value\": \"'Right'\"}}}}}], \"dataPoint\": [{\"properties\": {\"fill\": {\"solid\": {\"color\": {\"expr\": {\"FillRule\": {\"Input\": {\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Entity\": \"Orders\"}}, \"Property\": \"Sales\"}}, \"Function\": 0}}, \"FillRule\": {\"linearGradient3\": {\"min\": {\"color\": {\"Literal\": {\"Value\": \"'#AE123A'\"}}}, \"mid\": {\"color\": {\"Literal\": {\"Value\": \"'#D9D9D9'\"}}}, \"max\": {\"color\": {\"Literal\": {\"Value\": \"'#49525E'\"}}}, \"nullColoringStrategy\": {\"strategy\": {\"Literal\": {\"Value\": \"'asZero'\"}}}}}}}}}}}, \"selector\": {\"data\": [{\"dataViewWildcard\": {\"matchingOption\": 1}}]}}], \"labels\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}}}], \"valueAxis\": [{\"properties\": {\"switchAxisPosition\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}, \"labelPrecision\": {\"expr\": {\"Literal\": {\"Value\": \"1L\"}}}, \"showAxisTitle\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}}}], \"categoryAxis\": [{\"properties\": {\"switchAxisPosition\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}, \"labelPrecision\": {\"expr\": {\"Literal\": {\"Value\": \"1L\"}}}, \"showAxisTitle\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}}}]}, \"vcObjects\": { \"title\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"text\": {\"expr\": {\"Literal\": {\"Value\": \"'Customer Ranking'\"}}}, \"fontFamily\": {\"expr\": {\"Literal\": {\"Value\": \"'Calibri'\"}}}}}], \"background\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}], \"border\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}] } } }", "filters": "[]", "height": 651.4704, "width": 532.2368, "x": 607.936, "y": 63.4536, "z": 0.0}], "width": 1280.0}, {"config": "{}", "displayName": "Order Details", "displayOption": 1, "filters": "[]", "height": 720.0, "name": "7c4a0a2d9b154913b289", "visualContainers": [{"config": "{ \"name\": \"2affe25ac6354aa88ef3\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 0.0, \"y\":0.0, \"z\": 0, \"width\": 1277, \"height\": 50, \"tabOrder\": 1 } } ], \"singleVisual\": { \"visualType\": \"textbox\", \"drillFilterOtherVisuals\": true, \"objects\": { \"general\": [ { \"properties\": { \"paragraphs\": [ { \"textRuns\": [ { \"value\": \"Order Details\", \"textStyle\": {\"fontSize\": \"18pt\", \"fontFamily\": \"Calibri\"} } ], \"horizontalTextAlignment\": \"left\" } ] } } ] }, \"vcObjects\": { \"border\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}], \"background\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}] } } }", "height": 50, "width": 1277, "x": 0.0, "y": 0.0, "z": 0}, {"config": "{ \"name\": \"efbdb134caaa44d4ada3\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 1161.0368, \"y\": 74.4552, \"z\": 0.0, \"width\": 107.3792, \"height\": 55.8432, \"tabOrder\": 5000 } } ], \"singleVisual\": { \"visualType\": \"slicer\", \"projections\": { \"Values\": [ { \"queryRef\": \"Orders.Region\", \"active\": true } ] }, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Region\"}, \"Name\": \"Orders.Region\", \"NativeReferenceName\": \"Region\"}] }, \"drillFilterOtherVisuals\": true, \"hasDefaultSort\": true, \"objects\": { \"data\": [ { \"properties\": { \"mode\": { \"expr\": { \"Literal\": { \"Value\": \"'Dropdown'\" } } } } } ], \"header\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } } } } ], \"items\": [ { \"properties\": { \"outlineStyle\": { \"expr\": { \"Literal\": { \"Value\": \"0D\" } } } } } ], \"general\": [ { \"properties\": { \"outlineWeight\": { \"expr\": { \"Literal\": { \"Value\": \"1D\" } } } } } ] }, \"vcObjects\": { \"title\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"true\" } } }, \"text\": { \"expr\": { \"Literal\": { \"Value\": \"'Region'\" } } } } } ], \"background\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } }, \"color\": { \"solid\": { \"color\": { \"expr\": { \"ThemeDataColor\": { \"ColorId\": 0, \"Percent\": 0 } } } } } } } ], \"visualHeader\": [ { \"properties\": { \"border\": { \"solid\": { \"color\": { \"expr\": { \"Literal\": { \"Value\": \"'#252423'\" } } } } }, \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } } } } ], \"border\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"true\" } } }, \"color\": { \"solid\": { \"color\": { \"expr\": { \"Literal\": { \"Value\": \"'None'\" } } } } } } } ] } } }", "filters": "[]", "height": 55.8432, "width": 107.3792, "x": 1161.0368, "y": 74.4552, "z": 0.0}, {"config": "{ \"name\": \"88d97fd230e343a9afdc\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 1163.3536, \"y\": 144.6768, \"z\": 0.0, \"width\": 104.2816, \"height\": 56.6856, \"tabOrder\": 5000 } } ], \"singleVisual\": { \"visualType\": \"slicer\", \"projections\": { \"Values\": [ { \"queryRef\": \"Orders.Segment\", \"active\": true } ] }, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Segment\"}, \"Name\": \"Orders.Segment\", \"NativeReferenceName\": \"Segment\"}] }, \"drillFilterOtherVisuals\": true, \"hasDefaultSort\": true, \"objects\": { \"data\": [ { \"properties\": { \"mode\": { \"expr\": { \"Literal\": { \"Value\": \"'Dropdown'\" } } } } } ], \"header\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } } } } ], \"items\": [ { \"properties\": { \"outlineStyle\": { \"expr\": { \"Literal\": { \"Value\": \"0D\" } } } } } ], \"general\": [ { \"properties\": { \"outlineWeight\": { \"expr\": { \"Literal\": { \"Value\": \"1D\" } } } } } ] }, \"vcObjects\": { \"title\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"true\" } } }, \"text\": { \"expr\": { \"Literal\": { \"Value\": \"'Segment'\" } } } } } ], \"background\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } }, \"color\": { \"solid\": { \"color\": { \"expr\": { \"ThemeDataColor\": { \"ColorId\": 0, \"Percent\": 0 } } } } } } } ], \"visualHeader\": [ { \"properties\": { \"border\": { \"solid\": { \"color\": { \"expr\": { \"Literal\": { \"Value\": \"'#252423'\" } } } } }, \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } } } } ], \"border\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"true\" } } }, \"color\": { \"solid\": { \"color\": { \"expr\": { \"Literal\": { \"Value\": \"'None'\" } } } } } } } ] } } }", "filters": "[]", "height": 56.6856, "width": 104.2816, "x": 1163.3536, "y": 144.6768, "z": 0.0}, {"config": "{ \"name\": \"f76ec8e240ec4e3093aa\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 1164.1216, \"y\": 228.4344, \"z\": 0.0, \"width\": 103.5136, \"height\": 58.3776, \"tabOrder\": 5000 } } ], \"singleVisual\": { \"visualType\": \"slicer\", \"projections\": { \"Values\": [ { \"queryRef\": \"Orders.Ship Mode\", \"active\": true } ] }, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Ship Mode\"}, \"Name\": \"Orders.Ship Mode\", \"NativeReferenceName\": \"Ship Mode\"}] }, \"drillFilterOtherVisuals\": true, \"hasDefaultSort\": true, \"objects\": { \"data\": [ { \"properties\": { \"mode\": { \"expr\": { \"Literal\": { \"Value\": \"'Dropdown'\" } } } } } ], \"header\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } } } } ], \"items\": [ { \"properties\": { \"outlineStyle\": { \"expr\": { \"Literal\": { \"Value\": \"0D\" } } } } } ], \"general\": [ { \"properties\": { \"outlineWeight\": { \"expr\": { \"Literal\": { \"Value\": \"1D\" } } } } } ] }, \"vcObjects\": { \"title\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"true\" } } }, \"text\": { \"expr\": { \"Literal\": { \"Value\": \"'Ship Mode'\" } } } } } ], \"background\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } }, \"color\": { \"solid\": { \"color\": { \"expr\": { \"ThemeDataColor\": { \"ColorId\": 0, \"Percent\": 0 } } } } } } } ], \"visualHeader\": [ { \"properties\": { \"border\": { \"solid\": { \"color\": { \"expr\": { \"Literal\": { \"Value\": \"'#252423'\" } } } } }, \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } } } } ], \"border\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"true\" } } }, \"color\": { \"solid\": { \"color\": { \"expr\": { \"Literal\": { \"Value\": \"'None'\" } } } } } } } ] } } }", "filters": "[]", "height": 58.3776, "width": 103.5136, "x": 1164.1216, "y": 228.4344, "z": 0.0}, {"config": "{ \"name\": \"3b7b5965595a45b9a979\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 1169.536, \"y\": 305.4312, \"z\": 0.0, \"width\": 101.9648, \"height\": 71.0712, \"tabOrder\": 5000 } } ], \"singleVisual\": { \"visualType\": \"slicer\", \"projections\": { \"Values\": [ { \"queryRef\": \"Orders.State/Province\", \"active\": true } ] }, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"State/Province\"}, \"Name\": \"Orders.State/Province\", \"NativeReferenceName\": \"State/Province\"}] }, \"drillFilterOtherVisuals\": true, \"hasDefaultSort\": true, \"objects\": { \"data\": [ { \"properties\": { \"mode\": { \"expr\": { \"Literal\": { \"Value\": \"'Dropdown'\" } } } } } ], \"header\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } } } } ], \"items\": [ { \"properties\": { \"outlineStyle\": { \"expr\": { \"Literal\": { \"Value\": \"0D\" } } } } } ], \"general\": [ { \"properties\": { \"outlineWeight\": { \"expr\": { \"Literal\": { \"Value\": \"1D\" } } } } } ] }, \"vcObjects\": { \"title\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"true\" } } }, \"text\": { \"expr\": { \"Literal\": { \"Value\": \"'State/Province'\" } } } } } ], \"background\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } }, \"color\": { \"solid\": { \"color\": { \"expr\": { \"ThemeDataColor\": { \"ColorId\": 0, \"Percent\": 0 } } } } } } } ], \"visualHeader\": [ { \"properties\": { \"border\": { \"solid\": { \"color\": { \"expr\": { \"Literal\": { \"Value\": \"'#252423'\" } } } } }, \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } } } } ], \"border\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"true\" } } }, \"color\": { \"solid\": { \"color\": { \"expr\": { \"Literal\": { \"Value\": \"'None'\" } } } } } } } ] } } }", "filters": "[]", "height": 71.0712, "width": 101.9648, "x": 1169.536, "y": 305.4312, "z": 0.0}, {"config": "{ \"name\": \"2bb9ed4ae660e7a710b4\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 10.048, \"y\": 50.7672, \"z\": 0.0, \"width\": 1149.4528, \"height\": 670.9248, \"tabOrder\": 6000 } } ], \"singleVisual\": { \"visualType\": \"tableEx\", \"projections\": { \"Values\": [{\"queryRef\": \"Orders.Order ID\"}, {\"queryRef\": \"Orders.Customer Name\"}, {\"queryRef\": \"Orders.Customer ID\"}, {\"queryRef\": \"Orders.Region\"}, {\"queryRef\": \"Orders.Category\"}, {\"queryRef\": \"Orders.Segment\"}, {\"queryRef\": \"Orders.State/Province\"}, {\"queryRef\": \"Orders.Order Date.Variation.Date Hierarchy.Year\"}, {\"queryRef\": \"Orders.Order Date.Variation.Date Hierarchy.Quarter\"}, {\"queryRef\": \"Orders.Order Date.Variation.Date Hierarchy.Month\"}, {\"queryRef\": \"Orders.Ship Mode\"}] }, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Order ID\"}, \"Name\": \"Orders.Order ID\", \"NativeReferenceName\": \"Order ID\"}, {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Customer Name\"}, \"Name\": \"Orders.Customer Name\", \"NativeReferenceName\": \"Customer Name\"}, {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Customer ID\"}, \"Name\": \"Orders.Customer ID\", \"NativeReferenceName\": \"Customer ID\"}, {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Region\"}, \"Name\": \"Orders.Region\", \"NativeReferenceName\": \"Region\"}, {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Category\"}, \"Name\": \"Orders.Category\", \"NativeReferenceName\": \"Category\"}, {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Segment\"}, \"Name\": \"Orders.Segment\", \"NativeReferenceName\": \"Segment\"}, {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"State/Province\"}, \"Name\": \"Orders.State/Province\", \"NativeReferenceName\": \"State/Province\"}, {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Order Date\"}, \"Name\": \"Orders.Order Date.Variation.Date Hierarchy.Year\", \"NativeReferenceName\": \"Order Date Year\"}, {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Order Date\"}, \"Name\": \"Orders.Order Date.Variation.Date Hierarchy.Quarter\", \"NativeReferenceName\": \"Order Date\"}, {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Order Date\"}, \"Name\": \"Orders.Order Date.Variation.Date Hierarchy.Month\", \"NativeReferenceName\": \"Order Date Month\"}, {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Ship Mode\"}, \"Name\": \"Orders.Ship Mode\", \"NativeReferenceName\": \"Ship Mode\"}] }, \"drillFilterOtherVisuals\": true, \"vcObjects\": {\"title\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"text\": {\"expr\": {\"Literal\": {\"Value\": \"'Product Detail Sheet'\"}}}, \"fontFamily\": {\"expr\": {\"Literal\": {\"Value\": \"'Calibri'\"}}}}}], \"background\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}], \"border\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}]} } }", "filters": "[]", "height": 670.9248, "width": 1149.4528, "x": 10.048, "y": 50.7672, "z": 0.0}], "width": 1280.0}, {"config": "{}", "displayName": "Overview", "displayOption": 1, "filters": "[]", "height": 720.0, "name": "2c6ab79f001e4e049bdf", "visualContainers": [{"config": "{ \"name\": \"0f44700fe2c14f2b9278\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 1133.2224, \"y\": 223.3584, \"z\": 0.0, \"width\": 129.7792, \"height\": 60.0696, \"tabOrder\": 5000 } } ], \"singleVisual\": { \"visualType\": \"slicer\", \"projections\": { \"Values\": [ { \"queryRef\": \"Orders.Order Date\", \"active\": true } ] }, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Order Date\"}, \"Name\": \"Orders.Order Date\", \"NativeReferenceName\": \"Order Date\"}] }, \"drillFilterOtherVisuals\": true, \"hasDefaultSort\": true, \"objects\": { \"data\": [ { \"properties\": { \"mode\": { \"expr\": { \"Literal\": { \"Value\": \"'None'\" } } } } } ], \"header\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } } } } ], \"items\": [ { \"properties\": { \"outlineStyle\": { \"expr\": { \"Literal\": { \"Value\": \"0D\" } } } } } ], \"general\": [ { \"properties\": { \"outlineWeight\": { \"expr\": { \"Literal\": { \"Value\": \"1D\" } } } } } ] }, \"vcObjects\": { \"title\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"true\" } } }, \"text\": { \"expr\": { \"Literal\": { \"Value\": \"'Order Date'\" } } } } } ], \"background\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } }, \"color\": { \"solid\": { \"color\": { \"expr\": { \"ThemeDataColor\": { \"ColorId\": 0, \"Percent\": 0 } } } } } } } ], \"visualHeader\": [ { \"properties\": { \"border\": { \"solid\": { \"color\": { \"expr\": { \"Literal\": { \"Value\": \"'#252423'\" } } } } }, \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } } } } ], \"border\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"true\" } } }, \"color\": { \"solid\": { \"color\": { \"expr\": { \"Literal\": { \"Value\": \"'None'\" } } } } } } } ] } } }", "filters": "[]", "height": 60.0696, "width": 129.7792, "x": 1133.2224, "y": 223.3584, "z": 0.0}, {"config": "{ \"name\": \"62c10e0cb6db4f7ab0cb\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 1134.0032, \"y\": 296.9712, \"z\": 0.0, \"width\": 129.7792, \"height\": 64.3032, \"tabOrder\": 5000 } } ], \"singleVisual\": { \"visualType\": \"slicer\", \"projections\": { \"Values\": [ { \"queryRef\": \"Orders.Region\", \"active\": true } ] }, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Region\"}, \"Name\": \"Orders.Region\", \"NativeReferenceName\": \"Region\"}] }, \"drillFilterOtherVisuals\": true, \"hasDefaultSort\": true, \"objects\": { \"data\": [ { \"properties\": { \"mode\": { \"expr\": { \"Literal\": { \"Value\": \"'Dropdown'\" } } } } } ], \"header\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } } } } ], \"items\": [ { \"properties\": { \"outlineStyle\": { \"expr\": { \"Literal\": { \"Value\": \"0D\" } } } } } ], \"general\": [ { \"properties\": { \"outlineWeight\": { \"expr\": { \"Literal\": { \"Value\": \"1D\" } } } } } ] }, \"vcObjects\": { \"title\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"true\" } } }, \"text\": { \"expr\": { \"Literal\": { \"Value\": \"'Region'\" } } } } } ], \"background\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } }, \"color\": { \"solid\": { \"color\": { \"expr\": { \"ThemeDataColor\": { \"ColorId\": 0, \"Percent\": 0 } } } } } } } ], \"visualHeader\": [ { \"properties\": { \"border\": { \"solid\": { \"color\": { \"expr\": { \"Literal\": { \"Value\": \"'#252423'\" } } } } }, \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } } } } ], \"border\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"true\" } } }, \"color\": { \"solid\": { \"color\": { \"expr\": { \"Literal\": { \"Value\": \"'None'\" } } } } } } } ] } } }", "filters": "[]", "height": 64.3032, "width": 129.7792, "x": 1134.0032, "y": 296.9712, "z": 0.0}, {"config": "{ \"name\": \"a2ccea5896004485a353\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 18.8416, \"y\":14.796, \"z\": 0.0, \"width\": 1244.5312, \"height\": 50, \"tabOrder\": 1 } } ], \"singleVisual\": { \"visualType\": \"textbox\", \"drillFilterOtherVisuals\": true, \"objects\": { \"general\": [ { \"properties\": { \"paragraphs\": [ { \"textRuns\": [ { \"value\": \"Executive Overview - Profitability\", \"textStyle\": {\"fontSize\": \"18pt\", \"fontFamily\": \"Calibri\"} } ], \"horizontalTextAlignment\": \"1\" } ] } } ] }, \"vcObjects\": { \"border\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}], \"background\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}] } } }", "filters": "[]", "x": 18.8416, "y": 14.796, "width": 1244.5312, "height": 50, "z": 0.0}, {"config": "{ \"name\": \"aa0856a5e6e54babae63\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 18.8416, \"y\": 64.79599999999999, \"z\": 0.0, \"width\": 199.08853333333332, \"height\": 130.6872, \"tabOrder\": 1 } } ], \"singleVisual\": { \"visualType\": \"card\", \"projections\": {\"Values\": [{\"queryRef\": \"Sum(Orders.Sales)\", \"active\": true}]}, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Sales\"}}, \"Function\": 0}, \"Name\": \"Sum(Orders.Sales)\", \"NativeReferenceName\": \"Sum of Sales\"}] }, \"drillFilterOtherVisuals\": true, \"objects\": {\"categoryLabels\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}], \"labels\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"bold\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"fontSize\": {\"expr\": {\"Literal\": {\"Value\": \"12D\"}}}}}]}, \"vcObjects\": { \"title\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"text\": {\"expr\": {\"Literal\": {\"Value\": \"'Sales'\"}}}, \"fontFamily\": {\"expr\": {\"Literal\": {\"Value\": \"'Calibri'\"}}}, \"alignment\": {\"expr\": {\"Literal\": {\"Value\": \"'center'\"}}}}}] } } }", "filters": "[]", "x": 18.8416, "y": 64.79599999999999, "width": 199.08853333333332, "height": 130.6872, "z": 0.0}, {"config": "{ \"name\": \"6de17e726afc496b8627\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 227.93013333333332, \"y\": 64.79599999999999, \"z\": 0.0, \"width\": 199.08853333333332, \"height\": 130.6872, \"tabOrder\": 1 } } ], \"singleVisual\": { \"visualType\": \"card\", \"projections\": {\"Values\": [{\"queryRef\": \"Sum(Orders.Profit)\", \"active\": true}]}, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Profit\"}}, \"Function\": 0}, \"Name\": \"Sum(Orders.Profit)\", \"NativeReferenceName\": \"Sum of Profit\"}] }, \"drillFilterOtherVisuals\": true, \"objects\": {\"categoryLabels\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}], \"labels\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"bold\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"fontSize\": {\"expr\": {\"Literal\": {\"Value\": \"12D\"}}}}}]}, \"vcObjects\": { \"title\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"text\": {\"expr\": {\"Literal\": {\"Value\": \"'Profit'\"}}}, \"fontFamily\": {\"expr\": {\"Literal\": {\"Value\": \"'Calibri'\"}}}, \"alignment\": {\"expr\": {\"Literal\": {\"Value\": \"'center'\"}}}}}] } } }", "filters": "[]", "x": 227.93013333333332, "y": 64.79599999999999, "width": 199.08853333333332, "height": 130.6872, "z": 0.0}, {"config": "{ \"name\": \"d77f9b24923e488295a4\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 437.0186666666666, \"y\": 64.79599999999999, \"z\": 0.0, \"width\": 199.08853333333332, \"height\": 130.6872, \"tabOrder\": 1 } } ], \"singleVisual\": { \"visualType\": \"card\", \"projections\": {\"Values\": [{\"queryRef\": \"Sum(Orders.Profit per Order)\", \"active\": true}]}, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Profit per Order\"}}, \"Function\": 0}, \"Name\": \"Sum(Orders.Profit per Order)\", \"NativeReferenceName\": \"Profit per Order\"}] }, \"drillFilterOtherVisuals\": true, \"objects\": {\"categoryLabels\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}], \"labels\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"bold\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"fontSize\": {\"expr\": {\"Literal\": {\"Value\": \"12D\"}}}}}]}, \"vcObjects\": { \"title\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"text\": {\"expr\": {\"Literal\": {\"Value\": \"'Profit per Order'\"}}}, \"fontFamily\": {\"expr\": {\"Literal\": {\"Value\": \"'Calibri'\"}}}, \"alignment\": {\"expr\": {\"Literal\": {\"Value\": \"'center'\"}}}}}] } } }", "filters": "[]", "x": 437.0186666666666, "y": 64.79599999999999, "width": 199.08853333333332, "height": 130.6872, "z": 0.0}, {"config": "{ \"name\": \"dfda45eaed244ca7aa57\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 646.1071999999999, \"y\": 64.79599999999999, \"z\": 0.0, \"width\": 199.08853333333332, \"height\": 130.6872, \"tabOrder\": 1 } } ], \"singleVisual\": { \"visualType\": \"card\", \"projections\": {\"Values\": [{\"queryRef\": \"Max(Orders.Discount)\", \"active\": true}]}, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Discount\"}}, \"Function\": 4}, \"Name\": \"Max(Orders.Discount)\", \"NativeReferenceName\": \"Discount\"}] }, \"drillFilterOtherVisuals\": true, \"objects\": {\"categoryLabels\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}], \"labels\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"bold\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"fontSize\": {\"expr\": {\"Literal\": {\"Value\": \"12D\"}}}}}]}, \"vcObjects\": { \"title\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"text\": {\"expr\": {\"Literal\": {\"Value\": \"'Discount'\"}}}, \"fontFamily\": {\"expr\": {\"Literal\": {\"Value\": \"'Calibri'\"}}}, \"alignment\": {\"expr\": {\"Literal\": {\"Value\": \"'center'\"}}}}}] } } }", "filters": "[]", "x": 646.1071999999999, "y": 64.79599999999999, "width": 199.08853333333332, "height": 130.6872, "z": 0.0}, {"config": "{ \"name\": \"db8a99d914584babb9d0\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 855.1957333333332, \"y\": 64.79599999999999, \"z\": 0.0, \"width\": 199.08853333333332, \"height\": 130.6872, \"tabOrder\": 1 } } ], \"singleVisual\": { \"visualType\": \"card\", \"projections\": {\"Values\": [{\"queryRef\": \"Sum(Orders.Quantity)\", \"active\": true}]}, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Quantity\"}}, \"Function\": 0}, \"Name\": \"Sum(Orders.Quantity)\", \"NativeReferenceName\": \"Sum of Quantity\"}] }, \"drillFilterOtherVisuals\": true, \"objects\": {\"categoryLabels\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}], \"labels\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"bold\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"fontSize\": {\"expr\": {\"Literal\": {\"Value\": \"12D\"}}}}}]}, \"vcObjects\": { \"title\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"text\": {\"expr\": {\"Literal\": {\"Value\": \"'Quantity'\"}}}, \"fontFamily\": {\"expr\": {\"Literal\": {\"Value\": \"'Calibri'\"}}}, \"alignment\": {\"expr\": {\"Literal\": {\"Value\": \"'center'\"}}}}}] } } }", "filters": "[]", "x": 855.1957333333332, "y": 64.79599999999999, "width": 199.08853333333332, "height": 130.6872, "z": 0.0}, {"config": "{ \"name\": \"3a87103d791c48b48b28\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 1064.2842666666666, \"y\": 64.79599999999999, \"z\": 0.0, \"width\": 199.08853333333332, \"height\": 130.6872, \"tabOrder\": 1 } } ], \"singleVisual\": { \"visualType\": \"card\", \"projections\": {\"Values\": [{\"queryRef\": \"Average(Orders.Discount)\", \"active\": true}]}, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Discount\"}}, \"Function\": 1}, \"Name\": \"Average(Orders.Discount)\", \"NativeReferenceName\": \"Discount\"}] }, \"drillFilterOtherVisuals\": true, \"objects\": {\"categoryLabels\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}], \"labels\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"bold\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"fontSize\": {\"expr\": {\"Literal\": {\"Value\": \"12D\"}}}}}]}, \"vcObjects\": { \"title\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"text\": {\"expr\": {\"Literal\": {\"Value\": \"'Discount'\"}}}, \"fontFamily\": {\"expr\": {\"Literal\": {\"Value\": \"'Calibri'\"}}}, \"alignment\": {\"expr\": {\"Literal\": {\"Value\": \"'center'\"}}}}}] } } }", "filters": "[]", "x": 1064.2842666666666, "y": 64.79599999999999, "width": 199.08853333333332, "height": 130.6872, "z": 0.0}, {"config": "{ \"name\": \"5c47b7ba771c473f966c\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 22.4, \"y\": 142.9848, \"z\": 0.0, \"width\": 1105.4208, \"height\": 280.8936, \"tabOrder\": 1 } } ], \"singleVisual\": { \"visualType\": \"filledMap\", \"projections\": {\"Category\": [{\"queryRef\": \"Orders.Country/Region\", \"active\": true}, {\"queryRef\": \"Orders.State/Province\", \"active\": true}], \"Tooltips\": [{\"queryRef\": \"Sum(Orders.Sales)\", \"active\": true}]}, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Country/Region\"}, \"Name\": \"Orders.Country/Region\", \"NativeReferenceName\": \"Country/Region\"}, {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"State/Province\"}, \"Name\": \"Orders.State/Province\", \"NativeReferenceName\": \"State/Province\"}, {\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Sales\"}}, \"Function\": 0}, \"Name\": \"Sum(Orders.Sales)\", \"NativeReferenceName\": \"Sum of Sales\"}] }, \"drillFilterOtherVisuals\": true, \"objects\": {\"mapStyles\": [{\"properties\": {\"mapTheme\": {\"expr\": {\"Literal\": {\"Value\": \"'canvasLight'\"}}}, \"showLabels\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}}}], \"dataPoint\": [{\"properties\": {\"fill\": {\"solid\": {\"color\": {\"expr\": {\"FillRule\": {\"Input\": {\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Entity\": \"Orders\"}}, \"Property\": \"Sales\"}}, \"Function\": 0}}, \"FillRule\": {\"linearGradient3\": {\"min\": {\"color\": {\"Literal\": {\"Value\": \"'#9E3D22'\"}}}, \"mid\": {\"color\": {\"Literal\": {\"Value\": \"'#D9D5C9'\"}}}, \"max\": {\"color\": {\"Literal\": {\"Value\": \"'#2B5C88'\"}}}, \"nullColoringStrategy\": {\"strategy\": {\"Literal\": {\"Value\": \"'asZero'\"}}}}}}}}}}}, \"selector\": {\"data\": [{\"dataViewWildcard\": {\"matchingOption\": 1}}]}}], \"mapControls\": [{\"properties\": {\"autoZoom\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"showZoomButtons\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}, \"showLassoButton\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}]}, \"vcObjects\": { \"title\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"text\": {\"expr\": {\"Literal\": {\"Value\": \"'Sales by Geography'\"}}}, \"fontFamily\": {\"expr\": {\"Literal\": {\"Value\": \"'Calibri'\"}}}}}], \"background\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}], \"border\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}] } } }", "filters": "[]", "height": 280.8936, "width": 1105.4208, "x": 22.4, "y": 142.9848, "z": 0.0}, {"config": "{\"name\":\"1257ec2aca0c48cc8dd9\",\"layouts\":[{\"id\":0,\"position\":{\"x\":17.7664,\"y\":434.88,\"z\":0.0,\"width\":675.9168,\"height\":275.8176,\"tabOrder\":0}}],\"singleVisual\":{\"visualType\":\"areaChart\",\"projections\":{\"Category\":[{\"queryRef\": \"Orders.Order Date.Variation.Date Hierarchy.Year\", \"active\": true}, {\"queryRef\": \"Orders.Order Date.Variation.Date Hierarchy.Quarter\", \"active\": true}, {\"queryRef\": \"Orders.Order Date.Variation.Date Hierarchy.Month\", \"active\": true}],\"Y\":[{\"queryRef\": \"Sum(Orders.Sales)\", \"active\": true}],\"Series\":[{\"queryRef\": \"Orders.Order Profitable?\", \"active\": true}]},\"prototypeQuery\":{\"Version\":2,\"From\":[{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}],\"Select\":[{\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Sales\"}}, \"Function\": 0}, \"Name\": \"Sum(Orders.Sales)\", \"NativeReferenceName\": \"Sum of Sales\"}, {\"HierarchyLevel\": {\"Expression\": {\"Hierarchy\": {\"Expression\": {\"PropertyVariationSource\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Name\": \"Variation\", \"Property\": \"Order Date\"}}, \"Hierarchy\": \"Date Hierarchy\"}}, \"Level\": \"Year\"}, \"Name\": \"Orders.Order Date.Variation.Date Hierarchy.Year\", \"NativeReferenceName\": \"Order Date Year\"}, {\"HierarchyLevel\": {\"Expression\": {\"Hierarchy\": {\"Expression\": {\"PropertyVariationSource\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Name\": \"Variation\", \"Property\": \"Order Date\"}}, \"Hierarchy\": \"Date Hierarchy\"}}, \"Level\": \"Quarter\"}, \"Name\": \"Orders.Order Date.Variation.Date Hierarchy.Quarter\", \"NativeReferenceName\": \"Order Date Quarter\"}, {\"HierarchyLevel\": {\"Expression\": {\"Hierarchy\": {\"Expression\": {\"PropertyVariationSource\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Name\": \"Variation\", \"Property\": \"Order Date\"}}, \"Hierarchy\": \"Date Hierarchy\"}}, \"Level\": \"Month\"}, \"Name\": \"Orders.Order Date.Variation.Date Hierarchy.Month\", \"NativeReferenceName\": \"Order Date Month\"}, {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Order Profitable?\"}, \"Name\": \"Orders.Order Profitable?\", \"NativeReferenceName\": \"Order Profitable?\"}],\"OrderBy\":[]},\"drillFilterOtherVisuals\":true,\"objects\":{\"labels\":[{\"properties\":{\"show\":{\"expr\":{\"Literal\":{\"Value\":\"true\"}}}}}],\"seriesLabels\":[{\"properties\":{\"show\":{\"expr\":{\"Literal\":{\"Value\":\"true\"}}}}}]}}}", "filters": "[]", "height": 275.8176, "width": 675.9168, "x": 17.7664, "y": 434.88, "z": 0.0}, {"config": "{\"name\":\"3bce19dc437b41faac6f\",\"layouts\":[{\"id\":0,\"position\":{\"x\":699.8656,\"y\":433.6488,\"z\":0.0,\"width\":571.6352,\"height\":281.4192,\"tabOrder\":0}}],\"singleVisual\":{\"visualType\":\"areaChart\",\"projections\":{\"Category\":[{\"queryRef\": \"Orders.Order Date.Variation.Date Hierarchy.Year\", \"active\": true}, {\"queryRef\": \"Orders.Order Date.Variation.Date Hierarchy.Quarter\", \"active\": true}],\"Y\":[{\"queryRef\": \"Sum(Orders.Sales)\", \"active\": true}],\"Series\":[{\"queryRef\": \"Orders.Order Profitable?\", \"active\": true}]},\"prototypeQuery\":{\"Version\":2,\"From\":[{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}],\"Select\":[{\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Sales\"}}, \"Function\": 0}, \"Name\": \"Sum(Orders.Sales)\", \"NativeReferenceName\": \"Sum of Sales\"}, {\"HierarchyLevel\": {\"Expression\": {\"Hierarchy\": {\"Expression\": {\"PropertyVariationSource\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Name\": \"Variation\", \"Property\": \"Order Date\"}}, \"Hierarchy\": \"Date Hierarchy\"}}, \"Level\": \"Year\"}, \"Name\": \"Orders.Order Date.Variation.Date Hierarchy.Year\", \"NativeReferenceName\": \"Order Date Year\"}, {\"HierarchyLevel\": {\"Expression\": {\"Hierarchy\": {\"Expression\": {\"PropertyVariationSource\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Name\": \"Variation\", \"Property\": \"Order Date\"}}, \"Hierarchy\": \"Date Hierarchy\"}}, \"Level\": \"Quarter\"}, \"Name\": \"Orders.Order Date.Variation.Date Hierarchy.Quarter\", \"NativeReferenceName\": \"Order Date Quarter\"}, {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Order Profitable?\"}, \"Name\": \"Orders.Order Profitable?\", \"NativeReferenceName\": \"Order Profitable?\"}],\"OrderBy\":[]},\"drillFilterOtherVisuals\":true,\"objects\":{\"labels\":[{\"properties\":{\"show\":{\"expr\":{\"Literal\":{\"Value\":\"true\"}}}}}],\"seriesLabels\":[{\"properties\":{\"show\":{\"expr\":{\"Literal\":{\"Value\":\"true\"}}}}}]}}}", "filters": "[]", "height": 281.4192, "width": 571.6352, "x": 699.8656, "y": 433.6488, "z": 0.0}], "width": 1280.0}, {"config": "{}", "displayName": "Product", "displayOption": 1, "filters": "[]", "height": 720.0, "name": "c99075ea7c4442bcad42", "visualContainers": [{"config": "{ \"name\": \"3e3702650e1b45acb91f\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 1153.3184, \"y\": 69.3792, \"z\": 0.0, \"width\": 123.5968, \"height\": 120.9888, \"tabOrder\": 5000 } } ], \"singleVisual\": { \"visualType\": \"slicer\", \"projections\": { \"Values\": [ { \"queryRef\": \"Orders.Region\", \"active\": true } ] }, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Region\"}, \"Name\": \"Orders.Region\", \"NativeReferenceName\": \"Region\"}] }, \"drillFilterOtherVisuals\": true, \"hasDefaultSort\": true, \"objects\": { \"data\": [ { \"properties\": { \"mode\": { \"expr\": { \"Literal\": { \"Value\": \"'Basic'\" } } } } } ], \"header\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } } } } ], \"items\": [ { \"properties\": { \"outlineStyle\": { \"expr\": { \"Literal\": { \"Value\": \"0D\" } } } } } ], \"general\": [ { \"properties\": { \"outlineWeight\": { \"expr\": { \"Literal\": { \"Value\": \"1D\" } } } } } ] }, \"vcObjects\": { \"title\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"true\" } } }, \"text\": { \"expr\": { \"Literal\": { \"Value\": \"'Region'\" } } } } } ], \"background\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } }, \"color\": { \"solid\": { \"color\": { \"expr\": { \"ThemeDataColor\": { \"ColorId\": 0, \"Percent\": 0 } } } } } } } ], \"visualHeader\": [ { \"properties\": { \"border\": { \"solid\": { \"color\": { \"expr\": { \"Literal\": { \"Value\": \"'#252423'\" } } } } }, \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } } } } ], \"border\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"true\" } } }, \"color\": { \"solid\": { \"color\": { \"expr\": { \"Literal\": { \"Value\": \"'None'\" } } } } } } } ] } } }", "filters": "[]", "height": 120.9888, "width": 123.5968, "x": 1153.3184, "y": 69.3792, "z": 0.0}, {"config": "{\"name\":\"e3936094664f4752a01f\",\"layouts\":[{\"id\":0,\"position\":{\"x\":1.5488,\"y\":47.376,\"z\":0.0,\"width\":1146.3552,\"height\":313.8912,\"tabOrder\":0}}],\"singleVisual\":{\"visualType\":\"pivotTable\",\"projections\":{\"Rows\": [{\"queryRef\": \"Orders.Category\", \"active\": true}, {\"queryRef\": \"Orders.Order Date.Variation.Date Hierarchy.Year\", \"active\": true}], \"Columns\": [{\"queryRef\": \"Orders.Order Date.Variation.Date Hierarchy.Month\", \"active\": true}], \"Values\": [{\"queryRef\": \"Sum(Orders.Sales)\", \"active\": true}]},\"prototypeQuery\":{\"Version\":2,\"From\":[{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}],\"Select\":[{\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Category\"}, \"Name\": \"Orders.Category\", \"NativeReferenceName\": \"Category\"}, {\"HierarchyLevel\": {\"Expression\": {\"Hierarchy\": {\"Expression\": {\"PropertyVariationSource\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Name\": \"Variation\", \"Property\": \"Order Date\"}}, \"Hierarchy\": \"Date Hierarchy\"}}, \"Level\": \"Year\"}, \"Name\": \"Orders.Order Date.Variation.Date Hierarchy.Year\", \"NativeReferenceName\": \"Order Date Year\"}, {\"HierarchyLevel\": {\"Expression\": {\"Hierarchy\": {\"Expression\": {\"PropertyVariationSource\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Name\": \"Variation\", \"Property\": \"Order Date\"}}, \"Hierarchy\": \"Date Hierarchy\"}}, \"Level\": \"Month\"}, \"Name\": \"Orders.Order Date.Variation.Date Hierarchy.Month\", \"NativeReferenceName\": \"Order Date Month\"}, {\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Sales\"}}, \"Function\": 0}, \"Name\": \"Sum(Orders.Sales)\", \"NativeReferenceName\": \"Sum of Sales\"}]},\"drillFilterOtherVisuals\":true,\"objects\":{\"values\":[ { \"properties\": { \"backColor\": { \"solid\": { \"color\": { \"expr\": { \"FillRule\": { \"Input\": {\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Entity\": \"Orders\"}}, \"Property\": \"Sales\"}}, \"Function\": 0}}, \"FillRule\": { \"linearGradient3\": { \"min\": { \"color\": { \"Literal\": { \"Value\": \"'#B9DDF1'\" } } }, \"mid\": { \"color\": { \"Literal\": { \"Value\": \"'#6798C1'\" } } }, \"max\": { \"color\": { \"Literal\": { \"Value\": \"'#2A5783'\" } } }, \"nullColoringStrategy\": { \"strategy\": { \"Literal\": { \"Value\": \"'asZero'\" } } } } } } } } } } }, \"selector\": { \"data\": [ { \"dataViewWildcard\": { \"matchingOption\": 1 } } ], \"metadata\": \"Sum(Orders.Sales)\" } }, { \"properties\": { \"bandedRowHeaders\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } }, \"fontSize\": { \"expr\": { \"Literal\": { \"Value\": \"13D\" } } } } } ],\"columnHeaders\":[{\"properties\":{\"wordWrap\":{\"expr\":{\"Literal\":{\"Value\":\"true\"}}},\"fontSize\":{\"expr\":{\"Literal\":{\"Value\":\"10D\"}}},\"fontFamily\":{\"expr\":{\"Literal\":{\"Value\":\"'Calibri'\"}}}}}],\"subTotals\":[{\"properties\":{\"columnSubtotals\":{\"expr\":{\"Literal\":{\"Value\":\"true\"}}},\"rowSubtotals\":{\"expr\":{\"Literal\":{\"Value\":\"false\"}}}}}],\"rowHeaders\":[{\"properties\":{\"fontSize\":{\"expr\":{\"Literal\":{\"Value\":\"13D\"}}},\"fontFamily\":{\"expr\":{\"Literal\":{\"Value\":\"'Calibri'\"}}}}}],\"grid\":[{\"properties\":{\"outlineColor\":{\"solid\":{\"color\":{\"expr\":{\"Literal\":{\"Value\":\"'#CCCCCC'\"}}}}}}}],\"columnTotal\":[{\"properties\":{\"fontFamily\":{\"expr\":{\"Literal\":{\"Value\":\"'Calibri'\"}}}}}],\"rowTotal\":[{\"properties\":{\"fontFamily\":{\"expr\":{\"Literal\":{\"Value\":\"'Calibri'\"}}}}}]}, \"vcObjects\": { \"title\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"text\": {\"expr\": {\"Literal\": {\"Value\": \"'Sales by Product Category'\"}}}, \"fontFamily\": {\"expr\": {\"Literal\": {\"Value\": \"'Calibri'\"}}}}}], \"background\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}], \"border\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}] }}}", "filters": "[]", "height": 313.8912, "width": 1146.3552, "x": 1.5488, "y": 47.376, "z": 0.0}, {"config": "{\"name\":\"ed0001c184f047ab9164\",\"layouts\":[{\"id\":0,\"position\":{\"x\":0.0,\"y\":7.6176,\"z\":0,\"width\":716.5224,\"height\":40.608,\"tabOrder\":0}}],\"singleVisual\":{\"visualType\":\"textbox\",\"drillFilterOtherVisuals\":true,\"objects\": {\"general\": [{\"properties\": {\"paragraphs\": [{\"textRuns\": [{\"value\": \"Product Drilldown\", \"textStyle\": {\"fontFamily\": \"Calibri\", \"fontSize\": \"20pt\"}}]}]}}]},\"vcObjects\": {\"border\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}}}], \"background\": [{\"properties\": {\"color\": {\"solid\": {\"color\": {\"expr\": {\"Literal\": {\"Value\": \"'#ffffff'\"}}}}}}}], \"title\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}]}}}", "filters": "[]", "height": 40.608, "width": 716.5224, "x": 0.0, "y": 7.6176, "z": 0.0}, {"config": "{\"name\":\"4290292309894cebb3ec\",\"layouts\":[{\"id\":0,\"position\":{\"x\":1.5488,\"y\":363.8088,\"z\":0,\"width\":1145.5872,\"height\":356.1912,\"tabOrder\":0}}],\"singleVisual\":{\"visualType\":\"textbox\",\"drillFilterOtherVisuals\":true,\"objects\": {\"general\": [{\"properties\": {\"paragraphs\": [{\"textRuns\": [{\"value\": \"This Visual is not supported\", \"textStyle\": {\"fontFamily\": \"Calibri\", \"fontSize\": \"14pt\", \"textDecoration\": \"underline\", \"fontWeight\": \"bold\", \"fontStyle\": \"italic\"}}], \"horizontalTextAlignment\": \"center\"}]}}]},\"vcObjects\": {\"border\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}}}], \"background\": [{\"properties\": {\"color\": {\"solid\": {\"color\": {\"expr\": {\"Literal\": {\"Value\": \"'#ffffff'\"}}}}}}}], \"title\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}]}}}", "filters": "[]", "height": 356.1912, "width": 1145.5872, "x": 1.5488, "y": 363.8088, "z": 0.0}, {"config": "{\"name\":\"57d18156d9064d78993e\",\"layouts\":[{\"id\":0,\"position\":{\"x\":0.0,\"y\":7.6176,\"z\":0,\"width\":716.5224,\"height\":40.608,\"tabOrder\":0}}],\"singleVisual\":{\"visualType\":\"textbox\",\"drillFilterOtherVisuals\":true,\"objects\": {\"general\": [{\"properties\": {\"paragraphs\": [{\"textRuns\": [{\"value\": \"Product Drilldown\", \"textStyle\": {\"fontFamily\": \"Calibri\", \"fontSize\": \"20pt\"}}]}]}}]},\"vcObjects\": {\"border\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}}}], \"background\": [{\"properties\": {\"color\": {\"solid\": {\"color\": {\"expr\": {\"Literal\": {\"Value\": \"'#ffffff'\"}}}}}}}], \"title\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}]}}}", "filters": "[]", "height": 40.608, "width": 716.5224, "x": 0.0, "y": 7.6176, "z": 0.0}], "width": 1280.0}, {"config": "{}", "displayName": "Segment Analysis", "displayOption": 1, "filters": "[]", "height": 720.0, "name": "72abacb1ec534012b7b1", "visualContainers": [{"config": "{ \"name\": \"6a43a8d6fd5341d1a4d1\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 0.0, \"y\":0.0, \"z\": 0, \"width\": 1277, \"height\": 50, \"tabOrder\": 1 } } ], \"singleVisual\": { \"visualType\": \"textbox\", \"drillFilterOtherVisuals\": true, \"objects\": { \"general\": [ { \"properties\": { \"paragraphs\": [ { \"textRuns\": [ { \"value\": \"Segment Analysis\", \"textStyle\": {\"fontSize\": \"18pt\", \"fontFamily\": \"Calibri\"} } ], \"horizontalTextAlignment\": \"left\" } ] } } ] }, \"vcObjects\": { \"border\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}], \"background\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}] } } }", "height": 50, "width": 1277, "x": 0.0, "y": 0.0, "z": 0}, {"config": "{ \"name\": \"20f85291a8fb4b83b322\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 1146.3552, \"y\": 210.672, \"z\": 0.0, \"width\": 123.5968, \"height\": 276.66, \"tabOrder\": 5000 } } ], \"singleVisual\": { \"visualType\": \"slicer\", \"projections\": { \"Values\": [ { \"queryRef\": \"Orders.Order Date.Variation.Date Hierarchy.Month\", \"active\": true } ] }, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"HierarchyLevel\": {\"Expression\": {\"Hierarchy\": {\"Expression\": {\"PropertyVariationSource\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Name\": \"Variation\", \"Property\": \"Order Date\"}}, \"Hierarchy\": \"Date Hierarchy\"}}, \"Level\": \"Month\"}, \"Name\": \"Orders.Order Date.Variation.Date Hierarchy.Month\", \"NativeReferenceName\": \"Order Date Month\"}] }, \"drillFilterOtherVisuals\": true, \"hasDefaultSort\": true, \"objects\": { \"data\": [ { \"properties\": { \"mode\": { \"expr\": { \"Literal\": { \"Value\": \"'Basic'\" } } } } } ], \"header\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } } } } ], \"items\": [ { \"properties\": { \"outlineStyle\": { \"expr\": { \"Literal\": { \"Value\": \"0D\" } } } } } ], \"general\": [ { \"properties\": { \"outlineWeight\": { \"expr\": { \"Literal\": { \"Value\": \"1D\" } } } } } ] }, \"vcObjects\": { \"title\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"true\" } } }, \"text\": { \"expr\": { \"Literal\": { \"Value\": \"'Order Date'\" } } } } } ], \"background\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } }, \"color\": { \"solid\": { \"color\": { \"expr\": { \"ThemeDataColor\": { \"ColorId\": 0, \"Percent\": 0 } } } } } } } ], \"visualHeader\": [ { \"properties\": { \"border\": { \"solid\": { \"color\": { \"expr\": { \"Literal\": { \"Value\": \"'#252423'\" } } } } }, \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } } } } ], \"border\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"true\" } } }, \"color\": { \"solid\": { \"color\": { \"expr\": { \"Literal\": { \"Value\": \"'None'\" } } } } } } } ] } } }", "filters": "[]", "height": 276.66, "width": 123.5968, "x": 1146.3552, "y": 210.672, "z": 0.0}, {"config": "{ \"name\": \"bad42ce95625414cb625\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 1147.904, \"y\": 73.6056, \"z\": 0.0, \"width\": 115.8784, \"height\": 118.4472, \"tabOrder\": 5000 } } ], \"singleVisual\": { \"visualType\": \"slicer\", \"projections\": { \"Values\": [ { \"queryRef\": \"Orders.Order Date.Variation.Date Hierarchy.Year\", \"active\": true } ] }, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"HierarchyLevel\": {\"Expression\": {\"Hierarchy\": {\"Expression\": {\"PropertyVariationSource\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Name\": \"Variation\", \"Property\": \"Order Date\"}}, \"Hierarchy\": \"Date Hierarchy\"}}, \"Level\": \"Year\"}, \"Name\": \"Orders.Order Date.Variation.Date Hierarchy.Year\", \"NativeReferenceName\": \"Order Date Year\"}] }, \"drillFilterOtherVisuals\": true, \"hasDefaultSort\": true, \"objects\": { \"data\": [ { \"properties\": { \"mode\": { \"expr\": { \"Literal\": { \"Value\": \"'Basic'\" } } } } } ], \"header\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } } } } ], \"items\": [ { \"properties\": { \"outlineStyle\": { \"expr\": { \"Literal\": { \"Value\": \"0D\" } } } } } ], \"general\": [ { \"properties\": { \"outlineWeight\": { \"expr\": { \"Literal\": { \"Value\": \"1D\" } } } } } ] }, \"vcObjects\": { \"title\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"true\" } } }, \"text\": { \"expr\": { \"Literal\": { \"Value\": \"'Order Date'\" } } } } } ], \"background\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } }, \"color\": { \"solid\": { \"color\": { \"expr\": { \"ThemeDataColor\": { \"ColorId\": 0, \"Percent\": 0 } } } } } } } ], \"visualHeader\": [ { \"properties\": { \"border\": { \"solid\": { \"color\": { \"expr\": { \"Literal\": { \"Value\": \"'#252423'\" } } } } }, \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } } } } ], \"border\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"true\" } } }, \"color\": { \"solid\": { \"color\": { \"expr\": { \"Literal\": { \"Value\": \"'None'\" } } } } } } } ] } } }", "filters": "[]", "height": 118.4472, "width": 115.8784, "x": 1147.904, "y": 73.6056, "z": 0.0}, {"config": "{\"name\":\"3bdb0bf43f6849c2b67f\",\"layouts\":[{\"id\":0,\"position\":{\"x\":5.4016,\"y\":51.6096,\"z\":0,\"width\":1140.1856,\"height\":326.5776,\"tabOrder\":0}}],\"singleVisual\":{\"visualType\":\"textbox\",\"drillFilterOtherVisuals\":true,\"objects\": {\"general\": [{\"properties\": {\"paragraphs\": [{\"textRuns\": [{\"value\": \"This Visual is not supported\", \"textStyle\": {\"fontFamily\": \"Calibri\", \"fontSize\": \"14pt\", \"textDecoration\": \"underline\", \"fontWeight\": \"bold\", \"fontStyle\": \"italic\"}}], \"horizontalTextAlignment\": \"center\"}]}}]},\"vcObjects\": {\"border\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}}}], \"background\": [{\"properties\": {\"color\": {\"solid\": {\"color\": {\"expr\": {\"Literal\": {\"Value\": \"'#ffffff'\"}}}}}}}], \"title\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}]}}}", "filters": "[]", "height": 326.5776, "width": 1140.1856, "x": 5.4016, "y": 51.6096, "z": 0.0}, {"config": "{ \"name\": \"5a4695cd586545ddb720\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 4.6336, \"y\": 385.8048, \"z\": 0.0, \"width\":1138.6368, \"height\": 330.8112, \"tabOrder\": 0 } } ], \"singleVisual\": { \"visualType\": \"lineChart\", \"projections\": {\"Y\": [{\"queryRef\": \"Sum(Orders.Sales)\", \"active\": true}], \"Category\": [{\"queryRef\": \"Orders.Ship Date.Variation.Date Hierarchy.Year\", \"active\": true}, {\"queryRef\": \"Orders.Ship Date.Variation.Date Hierarchy.Quarter\", \"active\": true}], \"Series\": [{\"queryRef\": \"Orders.Segment\", \"active\": true}]}, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Sales\"}}, \"Function\": 0}, \"Name\": \"Sum(Orders.Sales)\", \"NativeReferenceName\": \"Sum of Sales\"}, {\"HierarchyLevel\": {\"Expression\": {\"Hierarchy\": {\"Expression\": {\"PropertyVariationSource\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Name\": \"Variation\", \"Property\": \"Ship Date\"}}, \"Hierarchy\": \"Date Hierarchy\"}}, \"Level\": \"Year\"}, \"Name\": \"Orders.Ship Date.Variation.Date Hierarchy.Year\", \"NativeReferenceName\": \"Ship Date Year\"}, {\"HierarchyLevel\": {\"Expression\": {\"Hierarchy\": {\"Expression\": {\"PropertyVariationSource\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Name\": \"Variation\", \"Property\": \"Ship Date\"}}, \"Hierarchy\": \"Date Hierarchy\"}}, \"Level\": \"Quarter\"}, \"Name\": \"Orders.Ship Date.Variation.Date Hierarchy.Quarter\", \"NativeReferenceName\": \"Ship Date Quarter\"}, {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Segment\"}, \"Name\": \"Orders.Segment\", \"NativeReferenceName\": \"Segment\"}] }, \"drillFilterOtherVisuals\": true, \"objects\": {\"lineStyles\": [{\"properties\": {\"showMarker\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}}}], \"legend\": [{\"properties\": {\"position\": {\"expr\": {\"Literal\": {\"Value\": \"'Right'\"}}}}}], \"labels\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}}}], \"categoryAxis\": [{\"properties\": {\"switchAxisPosition\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}, \"labelPrecision\": {\"expr\": {\"Literal\": {\"Value\": \"1L\"}}}, \"showAxisTitle\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}}}], \"valueAxis\": [{\"properties\": {\"switchAxisPosition\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}, \"labelPrecision\": {\"expr\": {\"Literal\": {\"Value\": \"1L\"}}}, \"showAxisTitle\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}}}]}, \"vcObjects\": { \"background\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}], \"title\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}, \"text\": {\"expr\": {\"Literal\": {\"Value\": \"'Segment Sales'\"}}}, \"fontFamily\": {\"expr\": {\"Literal\": {\"Value\": \"'Calibri'\"}}}}}], \"border\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}] } } }", "filters": "[]", "height": 330.8112, "width": 1138.6368, "x": 4.6336, "y": 385.8048, "z": 0.0}], "width": 1280.0}, {"config": "{}", "displayName": "Shipping", "displayOption": 1, "filters": "[]", "height": 720.0, "name": "6419376b644d4bf3bc26", "visualContainers": [{"config": "{ \"name\": \"51cd81871a114570854b\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 0.0, \"y\":0.0, \"z\": 0, \"width\": 1277, \"height\": 50, \"tabOrder\": 1 } } ], \"singleVisual\": { \"visualType\": \"textbox\", \"drillFilterOtherVisuals\": true, \"objects\": { \"general\": [ { \"properties\": { \"paragraphs\": [ { \"textRuns\": [ { \"value\": \"On-Time Shipment Trends\", \"textStyle\": {\"fontSize\": \"18pt\", \"fontFamily\": \"Calibri\"} } ], \"horizontalTextAlignment\": \"left\" } ] } } ] }, \"vcObjects\": { \"border\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}], \"background\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}] } } }", "height": 50, "width": 1277, "x": 0.0, "y": 0.0, "z": 0}, {"config": "{ \"name\": \"837479d5080e4866b105\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 1143.2704, \"y\": 340.9632, \"z\": 0.0, \"width\": 123.5968, \"height\": 60.0696, \"tabOrder\": 5000 } } ], \"singleVisual\": { \"visualType\": \"slicer\", \"projections\": { \"Values\": [ { \"queryRef\": \"Orders.Region\", \"active\": true } ] }, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Region\"}, \"Name\": \"Orders.Region\", \"NativeReferenceName\": \"Region\"}] }, \"drillFilterOtherVisuals\": true, \"hasDefaultSort\": true, \"objects\": { \"data\": [ { \"properties\": { \"mode\": { \"expr\": { \"Literal\": { \"Value\": \"'Dropdown'\" } } } } } ], \"header\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } } } } ], \"items\": [ { \"properties\": { \"outlineStyle\": { \"expr\": { \"Literal\": { \"Value\": \"0D\" } } } } } ], \"general\": [ { \"properties\": { \"outlineWeight\": { \"expr\": { \"Literal\": { \"Value\": \"1D\" } } } } } ] }, \"vcObjects\": { \"title\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"true\" } } }, \"text\": { \"expr\": { \"Literal\": { \"Value\": \"'Region'\" } } } } } ], \"background\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } }, \"color\": { \"solid\": { \"color\": { \"expr\": { \"ThemeDataColor\": { \"ColorId\": 0, \"Percent\": 0 } } } } } } } ], \"visualHeader\": [ { \"properties\": { \"border\": { \"solid\": { \"color\": { \"expr\": { \"Literal\": { \"Value\": \"'#252423'\" } } } } }, \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } } } } ], \"border\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"true\" } } }, \"color\": { \"solid\": { \"color\": { \"expr\": { \"Literal\": { \"Value\": \"'None'\" } } } } } } } ] } } }", "filters": "[]", "height": 60.0696, "width": 123.5968, "x": 1143.2704, "y": 340.9632, "z": 0.0}, {"config": "{ \"name\": \"546eac1250c54612892b\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 1147.904, \"y\": 406.1088, \"z\": 0.0, \"width\": 123.5968, \"height\": 60.0696, \"tabOrder\": 5000 } } ], \"singleVisual\": { \"visualType\": \"slicer\", \"projections\": { \"Values\": [ { \"queryRef\": \"Orders.Ship Mode\", \"active\": true } ] }, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Ship Mode\"}, \"Name\": \"Orders.Ship Mode\", \"NativeReferenceName\": \"Ship Mode\"}] }, \"drillFilterOtherVisuals\": true, \"hasDefaultSort\": true, \"objects\": { \"data\": [ { \"properties\": { \"mode\": { \"expr\": { \"Literal\": { \"Value\": \"'Dropdown'\" } } } } } ], \"header\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } } } } ], \"items\": [ { \"properties\": { \"outlineStyle\": { \"expr\": { \"Literal\": { \"Value\": \"0D\" } } } } } ], \"general\": [ { \"properties\": { \"outlineWeight\": { \"expr\": { \"Literal\": { \"Value\": \"1D\" } } } } } ] }, \"vcObjects\": { \"title\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"true\" } } }, \"text\": { \"expr\": { \"Literal\": { \"Value\": \"'Ship Mode'\" } } } } } ], \"background\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } }, \"color\": { \"solid\": { \"color\": { \"expr\": { \"ThemeDataColor\": { \"ColorId\": 0, \"Percent\": 0 } } } } } } } ], \"visualHeader\": [ { \"properties\": { \"border\": { \"solid\": { \"color\": { \"expr\": { \"Literal\": { \"Value\": \"'#252423'\" } } } } }, \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } } } } ], \"border\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"true\" } } }, \"color\": { \"solid\": { \"color\": { \"expr\": { \"Literal\": { \"Value\": \"'None'\" } } } } } } } ] } } }", "filters": "[]", "height": 60.0696, "width": 123.5968, "x": 1147.904, "y": 406.1088, "z": 0.0}, {"config": "{ \"name\": \"d24a4769476b4bf69396\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 1140.1856, \"y\": 201.3624, \"z\": 0.0, \"width\": 123.5968, \"height\": 120.9888, \"tabOrder\": 5000 } } ], \"singleVisual\": { \"visualType\": \"slicer\", \"projections\": { \"Values\": [ { \"queryRef\": \"Orders.Order Date.Variation.Date Hierarchy.Quarter\", \"active\": true } ] }, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"HierarchyLevel\": {\"Expression\": {\"Hierarchy\": {\"Expression\": {\"PropertyVariationSource\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Name\": \"Variation\", \"Property\": \"Order Date\"}}, \"Hierarchy\": \"Date Hierarchy\"}}, \"Level\": \"Quarter\"}, \"Name\": \"Orders.Order Date.Variation.Date Hierarchy.Quarter\", \"NativeReferenceName\": \"Order Date Quarter\"}] }, \"drillFilterOtherVisuals\": true, \"hasDefaultSort\": true, \"objects\": { \"data\": [ { \"properties\": { \"mode\": { \"expr\": { \"Literal\": { \"Value\": \"'Basic'\" } } } } } ], \"header\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } } } } ], \"items\": [ { \"properties\": { \"outlineStyle\": { \"expr\": { \"Literal\": { \"Value\": \"0D\" } } } } } ], \"general\": [ { \"properties\": { \"outlineWeight\": { \"expr\": { \"Literal\": { \"Value\": \"1D\" } } } } } ] }, \"vcObjects\": { \"title\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"true\" } } }, \"text\": { \"expr\": { \"Literal\": { \"Value\": \"'Order Date'\" } } } } } ], \"background\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } }, \"color\": { \"solid\": { \"color\": { \"expr\": { \"ThemeDataColor\": { \"ColorId\": 0, \"Percent\": 0 } } } } } } } ], \"visualHeader\": [ { \"properties\": { \"border\": { \"solid\": { \"color\": { \"expr\": { \"Literal\": { \"Value\": \"'#252423'\" } } } } }, \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } } } } ], \"border\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"true\" } } }, \"color\": { \"solid\": { \"color\": { \"expr\": { \"Literal\": { \"Value\": \"'None'\" } } } } } } } ] } } }", "filters": "[]", "height": 120.9888, "width": 123.5968, "x": 1140.1856, "y": 201.3624, "z": 0.0}, {"config": "{ \"name\": \"91740ae76fc649d99335\", \"layouts\": [ { \"id\": 0, \"position\": { \"x\": 1140.1856, \"y\": 61.7616, \"z\": 0.0, \"width\": 123.5968, \"height\": 127.7568, \"tabOrder\": 5000 } } ], \"singleVisual\": { \"visualType\": \"slicer\", \"projections\": { \"Values\": [ { \"queryRef\": \"Orders.Order Date.Variation.Date Hierarchy.Year\", \"active\": true } ] }, \"prototypeQuery\": { \"Version\": 2, \"From\": [{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}], \"Select\": [{\"HierarchyLevel\": {\"Expression\": {\"Hierarchy\": {\"Expression\": {\"PropertyVariationSource\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Name\": \"Variation\", \"Property\": \"Order Date\"}}, \"Hierarchy\": \"Date Hierarchy\"}}, \"Level\": \"Year\"}, \"Name\": \"Orders.Order Date.Variation.Date Hierarchy.Year\", \"NativeReferenceName\": \"Order Date Year\"}] }, \"drillFilterOtherVisuals\": true, \"hasDefaultSort\": true, \"objects\": { \"data\": [ { \"properties\": { \"mode\": { \"expr\": { \"Literal\": { \"Value\": \"'Basic'\" } } } } } ], \"header\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } } } } ], \"items\": [ { \"properties\": { \"outlineStyle\": { \"expr\": { \"Literal\": { \"Value\": \"0D\" } } } } } ], \"general\": [ { \"properties\": { \"outlineWeight\": { \"expr\": { \"Literal\": { \"Value\": \"1D\" } } } } } ] }, \"vcObjects\": { \"title\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"true\" } } }, \"text\": { \"expr\": { \"Literal\": { \"Value\": \"'Order Date'\" } } } } } ], \"background\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } }, \"color\": { \"solid\": { \"color\": { \"expr\": { \"ThemeDataColor\": { \"ColorId\": 0, \"Percent\": 0 } } } } } } } ], \"visualHeader\": [ { \"properties\": { \"border\": { \"solid\": { \"color\": { \"expr\": { \"Literal\": { \"Value\": \"'#252423'\" } } } } }, \"show\": { \"expr\": { \"Literal\": { \"Value\": \"false\" } } } } } ], \"border\": [ { \"properties\": { \"show\": { \"expr\": { \"Literal\": { \"Value\": \"true\" } } }, \"color\": { \"solid\": { \"color\": { \"expr\": { \"Literal\": { \"Value\": \"'None'\" } } } } } } } ] } } }", "filters": "[]", "height": 127.7568, "width": 123.5968, "x": 1140.1856, "y": 61.7616, "z": 0.0}, {"config": "{\"name\":\"8449b6627af347e3a943\",\"layouts\":[{\"id\":0,\"position\":{\"x\":9.2672,\"y\":52.4592,\"z\":0.0,\"width\":1131.6864,\"height\":292.7376,\"tabOrder\":0}}],\"singleVisual\":{\"visualType\":\"areaChart\",\"projections\":{\"Category\":[{\"queryRef\": \"Orders.Order Date.Variation.Date Hierarchy.Year\", \"active\": true}, {\"queryRef\": \"Orders.Order Date.Variation.Date Hierarchy.Quarter\", \"active\": true}, {\"queryRef\": \"Orders.Order Date.Variation.Date Hierarchy.Month\", \"active\": true}],\"Y\":[{\"queryRef\": \"Sum(Orders.Sales)\", \"active\": true}],\"Series\":[]},\"prototypeQuery\":{\"Version\":2,\"From\":[{\"Name\": \"Orders\", \"Entity\": \"Orders\", \"Type\": 0}],\"Select\":[{\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Property\": \"Sales\"}}, \"Function\": 0}, \"Name\": \"Sum(Orders.Sales)\", \"NativeReferenceName\": \"Sum of Sales\"}, {\"HierarchyLevel\": {\"Expression\": {\"Hierarchy\": {\"Expression\": {\"PropertyVariationSource\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Name\": \"Variation\", \"Property\": \"Order Date\"}}, \"Hierarchy\": \"Date Hierarchy\"}}, \"Level\": \"Year\"}, \"Name\": \"Orders.Order Date.Variation.Date Hierarchy.Year\", \"NativeReferenceName\": \"Order Date Year\"}, {\"HierarchyLevel\": {\"Expression\": {\"Hierarchy\": {\"Expression\": {\"PropertyVariationSource\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Name\": \"Variation\", \"Property\": \"Order Date\"}}, \"Hierarchy\": \"Date Hierarchy\"}}, \"Level\": \"Quarter\"}, \"Name\": \"Orders.Order Date.Variation.Date Hierarchy.Quarter\", \"NativeReferenceName\": \"Order Date Quarter\"}, {\"HierarchyLevel\": {\"Expression\": {\"Hierarchy\": {\"Expression\": {\"PropertyVariationSource\": {\"Expression\": {\"SourceRef\": {\"Source\": \"Orders\"}}, \"Name\": \"Variation\", \"Property\": \"Order Date\"}}, \"Hierarchy\": \"Date Hierarchy\"}}, \"Level\": \"Month\"}, \"Name\": \"Orders.Order Date.Variation.Date Hierarchy.Month\", \"NativeReferenceName\": \"Order Date Month\"}],\"OrderBy\":[]},\"drillFilterOtherVisuals\":true,\"objects\":{\"labels\":[{\"properties\":{\"show\":{\"expr\":{\"Literal\":{\"Value\":\"true\"}}}}}],\"seriesLabels\":[{\"properties\":{\"show\":{\"expr\":{\"Literal\":{\"Value\":\"true\"}}}}}]}}}", "filters": "[]", "height": 292.7376, "width": 1131.6864, "x": 9.2672, "y": 52.4592, "z": 0.0}, {"config": "{\"name\":\"bfd1b4e7b6ca456aa417\",\"layouts\":[{\"id\":0,\"position\":{\"x\":10.048,\"y\":350.2728,\"z\":0,\"width\":1130.9056,\"height\":352.8072,\"tabOrder\":0}}],\"singleVisual\":{\"visualType\":\"textbox\",\"drillFilterOtherVisuals\":true,\"objects\": {\"general\": [{\"properties\": {\"paragraphs\": [{\"textRuns\": [{\"value\": \"This Visual is not supported\", \"textStyle\": {\"fontFamily\": \"Calibri\", \"fontSize\": \"14pt\", \"textDecoration\": \"underline\", \"fontWeight\": \"bold\", \"fontStyle\": \"italic\"}}], \"horizontalTextAlignment\": \"center\"}]}}]},\"vcObjects\": {\"border\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"true\"}}}}}], \"background\": [{\"properties\": {\"color\": {\"solid\": {\"color\": {\"expr\": {\"Literal\": {\"Value\": \"'#ffffff'\"}}}}}}}], \"title\": [{\"properties\": {\"show\": {\"expr\": {\"Literal\": {\"Value\": \"false\"}}}}}]}}}", "filters": "[]", "height": 352.8072, "width": 1130.9056, "x": 10.048, "y": 350.2728, "z": 0.0}], "width": 1280.0}], "pods": []}