#!/usr/bin/env python3
"""
Test the actual workspace service implementation
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import psycopg2
from app.services.workspace.workspace_service import WorkspaceService
from app.services.workspace.workspace_procssor import WorkspaceProcessor

class MockUser:
    """Mock user object to simulate get_current_user response"""
    def __init__(self, user_id, role_name):
        self.id = user_id
        self.role_name = role_name

def get_test_users():
    """Get test users from database"""
    DB_CONFIG = {
        'host': 'localhost',
        'port': '5432',
        'user': 'postgres',
        'password': 'postgres',
        'database': 'test'
    }
    
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # Get users with assigned projects for testing
        cursor.execute("""
            SELECT DISTINCT u.id, u.name, u.email, r.name as role_name
            FROM biport_dev.users u
            JOIN biport_dev.roles r ON u.role_id = r.id
            LEFT JOIN biport_dev.project_details p ON p.assigned_to = u.id
            WHERE r.name IN ('ADMIN', 'MANAGER', 'DEVELOPER')
            ORDER BY r.name, u.name
            LIMIT 5
        """)
        users = cursor.fetchall()
        
        cursor.close()
        conn.close()
        
        return users
        
    except Exception as e:
        print(f"Error getting test users: {e}")
        return []

def test_workspace_service():
    """Test the workspace service"""
    print("=== Testing Workspace Service Implementation ===\n")
    
    users = get_test_users()
    if not users:
        print("❌ No test users found")
        return
    
    print(f"Testing with {len(users)} users:")
    for user in users:
        print(f"  - {user[1]} ({user[2]}) - Role: {user[3]}")
    
    print("\n" + "="*60)
    
    # Test WorkspaceService directly
    service = WorkspaceService()
    
    for user in users:
        user_id, user_name, user_email, role_name = user
        print(f"\n🧪 Testing WorkspaceService for {user_name} (Role: {role_name})")
        
        # Create mock user
        mock_user = MockUser(user_id, role_name)
        
        try:
            response = service.get_reports_by_user_role(mock_user)
            
            if response.success:
                print(f"   ✅ SUCCESS: Found {len(response.data)} reports")
                
                # Show sample reports with required fields
                for i, report in enumerate(response.data[:2]):  # Show first 2
                    print(f"     {i+1}. Report ID: {report['report_id']}")
                    print(f"        Report Name: {report['report_name']}")
                    print(f"        Project Name: {report['project_name']}")
                    print(f"        Status Flags: Analyzed={report['is_analyzed']}, Converted={report['is_converted']}, Migrated={report['is_migrated']}")
                    print(f"        Status Enums: analyzed_status={report['analyzed_status']}, converted_status={report['converted_status']}, migrated_status={report['migrated_status']}")
                    
            else:
                print(f"   ❌ FAILED: {response.error}")
                
        except Exception as e:
            print(f"   ❌ EXCEPTION: {e}")
            import traceback
            traceback.print_exc()

def test_workspace_processor():
    """Test the workspace processor"""
    print(f"\n" + "="*60)
    print("=== Testing Workspace Processor ===\n")
    
    users = get_test_users()
    if not users:
        print("❌ No test users found")
        return
    
    # Test one user with each role
    test_users = {}
    for user in users:
        role_name = user[3]
        if role_name not in test_users:
            test_users[role_name] = user
    
    for role_name, user in test_users.items():
        user_id, user_name, user_email, role_name = user
        print(f"🧪 Testing WorkspaceProcessor for {user_name} (Role: {role_name})")
        
        # Create mock user
        mock_user = MockUser(user_id, role_name)
        
        try:
            response = WorkspaceProcessor.process_get_reports_by_user_role(mock_user)
            
            if response.success:
                print(f"   ✅ SUCCESS: Found {len(response.data)} reports")
                print(f"   Response format matches requirements: ✅")
                
                # Verify response format
                if response.data:
                    sample_report = response.data[0]
                    required_fields = [
                        'report_id', 'report_name', 'project_name',
                        'is_analyzed', 'is_converted', 'is_migrated',
                        'analyzed_status', 'converted_status', 'migrated_status'
                    ]
                    
                    missing_fields = [field for field in required_fields if field not in sample_report]
                    if missing_fields:
                        print(f"   ❌ Missing fields: {missing_fields}")
                    else:
                        print(f"   ✅ All required fields present")
                        
            else:
                print(f"   ❌ FAILED: {response.error}")
                
        except Exception as e:
            print(f"   ❌ EXCEPTION: {e}")
            import traceback
            traceback.print_exc()

def test_role_based_access():
    """Test role-based access control"""
    print(f"\n" + "="*60)
    print("=== Testing Role-Based Access Control ===\n")
    
    users = get_test_users()
    if not users:
        print("❌ No test users found")
        return
    
    service = WorkspaceService()
    results = {}
    
    # Test each role
    for user in users:
        user_id, user_name, user_email, role_name = user
        mock_user = MockUser(user_id, role_name)
        
        try:
            response = service.get_reports_by_user_role(mock_user)
            if response.success:
                if role_name not in results:
                    results[role_name] = []
                results[role_name].append((user_name, len(response.data)))
        except Exception as e:
            print(f"Error testing {user_name}: {e}")
    
    # Display results
    print("Role-based access summary:")
    for role, user_results in results.items():
        print(f"\n{role} Role:")
        for user_name, report_count in user_results:
            print(f"  - {user_name}: {report_count} reports")
    
    # Verify role hierarchy
    admin_counts = [count for _, count in results.get('ADMIN', [])]
    manager_counts = [count for _, count in results.get('MANAGER', [])]
    developer_counts = [count for _, count in results.get('DEVELOPER', [])]
    
    print(f"\nAccess Control Verification:")
    if admin_counts and manager_counts:
        if max(admin_counts) >= max(manager_counts):
            print("✅ ADMIN sees more/equal reports than MANAGER")
        else:
            print("❌ ADMIN sees fewer reports than MANAGER")
    
    if manager_counts and developer_counts:
        if max(manager_counts) >= max(developer_counts):
            print("✅ MANAGER sees more/equal reports than DEVELOPER")
        else:
            print("❌ MANAGER sees fewer reports than DEVELOPER")

if __name__ == "__main__":
    try:
        test_workspace_service()
        test_workspace_processor()
        test_role_based_access()
        print(f"\n🎉 All tests completed!")
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
