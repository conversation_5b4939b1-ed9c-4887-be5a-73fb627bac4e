<?xml version='1.0' encoding='utf-8' ?>

<!-- build 20242.24.0807.0327                               -->
<workbook include-phone-layouts='false' original-version='18.1' source-build='2024.2.2 (20242.24.0807.0327)' source-platform='win' version='18.1' xmlns:user='http://www.tableausoftware.com/xml/user'>
  <document-format-change-manifest>
    <_.fcp.AccessibleZoneTabOrder.true...AccessibleZoneTabOrder />
    <_.fcp.AnimationOnByDefault.true...AnimationOnByDefault />
    <AutoCreateAndUpdateDSDPhoneLayouts />
    <IncludePhoneLayoutsOptOut />
    <_.fcp.IndividualControlFormatting.true...IndividualControlFormatting />
    <MapboxVectorStylesAndLayers />
    <_.fcp.MarkAnimation.true...MarkAnimation />
    <_.fcp.ObjectModelEncapsulateLegacy.true...ObjectModelEncapsulateLegacy />
    <_.fcp.ObjectModelRelationshipPerfOptions.true...ObjectModelRelationshipPerfOptions />
    <_.fcp.ObjectModelTableType.true...ObjectModelTableType />
    <_.fcp.SchemaViewerObjectModel.true...SchemaViewerObjectModel />
    <SetMembershipControl />
    <SheetIdentifierTracking />
    <SortTagCleanup />
    <WindowsPersistSimpleIdentifiers />
  </document-format-change-manifest>
  <preferences>
    <preference name='ui.encoding.shelf.height' value='24' />
    <preference name='ui.shelf.height' value='26' />
  </preferences>
  <_.fcp.AnimationOnByDefault.false...style>
    <_.fcp.AnimationOnByDefault.false..._.fcp.MarkAnimation.true...style-rule element='animation'>
      <_.fcp.AnimationOnByDefault.false...format attr='animation-on' value='ao-on' />
    </_.fcp.AnimationOnByDefault.false..._.fcp.MarkAnimation.true...style-rule>
  </_.fcp.AnimationOnByDefault.false...style>
  <datasources>
    <datasource hasconnection='false' inline='true' name='Parameters' version='18.1'>
      <aliases enabled='yes' />
      <column caption='Parameter 1' datatype='integer' name='[Parameter 1]' param-domain-type='list' role='measure' type='quantitative' value='10'>
        <calculation class='tableau' formula='10' />
        <members />
      </column>
      <column caption='DynamicValue' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;PROFIT&quot;'>
        <calculation class='tableau' formula='&quot;PROFIT&quot;' />
        <members>
          <member value='&quot;PROFIT&quot;' />
          <member value='&quot;SALES&quot;' />
          <member value='&quot;COST&quot;' />
        </members>
      </column>
    </datasource>
    <datasource caption='FactInternetSales+ (AdventureWorksDW2022)' inline='true' name='federated.1nl426t13auwkc10rmst40m1iuwe' version='18.1'>
      <connection class='federated'>
        <named-connections>
          <named-connection caption='SPARITY-SRIKRIS\SQL_SERVER_1' name='sqlserver.0k11eaw1owt4cc1b7523912w0z0e'>
            <connection authentication='sspi' class='sqlserver' dbname='AdventureWorksDW2022' minimum-driver-version='SQL Server Native Client 10.0' odbc-native-protocol='yes' one-time-sql='' server='localhost' />
          </named-connection>
        </named-connections>
        <_.fcp.ObjectModelEncapsulateLegacy.false...relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimDate' table='[dbo].[DimDate]' type='table' />
        <_.fcp.ObjectModelEncapsulateLegacy.true...relation type='collection'>
          <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimProductCategory' table='[dbo].[DimProductCategory]' type='table' />
          <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimProductSubcategory' table='[dbo].[DimProductSubcategory]' type='table' />
          <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimProduct' table='[dbo].[DimProduct]' type='table' />
          <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='FactInternetSales' table='[dbo].[FactInternetSales]' type='table' />
          <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimDate' table='[dbo].[DimDate]' type='table' />
          <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimSalesTerritory' table='[dbo].[DimSalesTerritory]' type='table' />
        </_.fcp.ObjectModelEncapsulateLegacy.true...relation>
        <cols>
          <map key='[ArabicDescription]' value='[DimProduct].[ArabicDescription]' />
          <map key='[CalendarQuarter]' value='[DimDate].[CalendarQuarter]' />
          <map key='[CalendarSemester]' value='[DimDate].[CalendarSemester]' />
          <map key='[CalendarYear]' value='[DimDate].[CalendarYear]' />
          <map key='[CarrierTrackingNumber]' value='[FactInternetSales].[CarrierTrackingNumber]' />
          <map key='[ChineseDescription]' value='[DimProduct].[ChineseDescription]' />
          <map key='[Class]' value='[DimProduct].[Class]' />
          <map key='[Color]' value='[DimProduct].[Color]' />
          <map key='[CurrencyKey]' value='[FactInternetSales].[CurrencyKey]' />
          <map key='[CustomerKey]' value='[FactInternetSales].[CustomerKey]' />
          <map key='[CustomerPONumber]' value='[FactInternetSales].[CustomerPONumber]' />
          <map key='[DateKey]' value='[DimDate].[DateKey]' />
          <map key='[DayNumberOfMonth]' value='[DimDate].[DayNumberOfMonth]' />
          <map key='[DayNumberOfWeek]' value='[DimDate].[DayNumberOfWeek]' />
          <map key='[DayNumberOfYear]' value='[DimDate].[DayNumberOfYear]' />
          <map key='[DaysToManufacture]' value='[DimProduct].[DaysToManufacture]' />
          <map key='[DealerPrice]' value='[DimProduct].[DealerPrice]' />
          <map key='[DiscountAmount]' value='[FactInternetSales].[DiscountAmount]' />
          <map key='[DueDateKey]' value='[FactInternetSales].[DueDateKey]' />
          <map key='[DueDate]' value='[FactInternetSales].[DueDate]' />
          <map key='[EndDate]' value='[DimProduct].[EndDate]' />
          <map key='[EnglishDayNameOfWeek]' value='[DimDate].[EnglishDayNameOfWeek]' />
          <map key='[EnglishDescription]' value='[DimProduct].[EnglishDescription]' />
          <map key='[EnglishMonthName]' value='[DimDate].[EnglishMonthName]' />
          <map key='[EnglishProductCategoryName]' value='[DimProductCategory].[EnglishProductCategoryName]' />
          <map key='[EnglishProductName]' value='[DimProduct].[EnglishProductName]' />
          <map key='[EnglishProductSubcategoryName]' value='[DimProductSubcategory].[EnglishProductSubcategoryName]' />
          <map key='[ExtendedAmount]' value='[FactInternetSales].[ExtendedAmount]' />
          <map key='[FinishedGoodsFlag]' value='[DimProduct].[FinishedGoodsFlag]' />
          <map key='[FiscalQuarter]' value='[DimDate].[FiscalQuarter]' />
          <map key='[FiscalSemester]' value='[DimDate].[FiscalSemester]' />
          <map key='[FiscalYear]' value='[DimDate].[FiscalYear]' />
          <map key='[Freight]' value='[FactInternetSales].[Freight]' />
          <map key='[FrenchDayNameOfWeek]' value='[DimDate].[FrenchDayNameOfWeek]' />
          <map key='[FrenchDescription]' value='[DimProduct].[FrenchDescription]' />
          <map key='[FrenchMonthName]' value='[DimDate].[FrenchMonthName]' />
          <map key='[FrenchProductCategoryName]' value='[DimProductCategory].[FrenchProductCategoryName]' />
          <map key='[FrenchProductName]' value='[DimProduct].[FrenchProductName]' />
          <map key='[FrenchProductSubcategoryName]' value='[DimProductSubcategory].[FrenchProductSubcategoryName]' />
          <map key='[FullDateAlternateKey]' value='[DimDate].[FullDateAlternateKey]' />
          <map key='[GermanDescription]' value='[DimProduct].[GermanDescription]' />
          <map key='[HebrewDescription]' value='[DimProduct].[HebrewDescription]' />
          <map key='[JapaneseDescription]' value='[DimProduct].[JapaneseDescription]' />
          <map key='[LargePhoto]' value='[DimProduct].[LargePhoto]' />
          <map key='[ListPrice]' value='[DimProduct].[ListPrice]' />
          <map key='[ModelName]' value='[DimProduct].[ModelName]' />
          <map key='[MonthNumberOfYear]' value='[DimDate].[MonthNumberOfYear]' />
          <map key='[OrderDateKey]' value='[FactInternetSales].[OrderDateKey]' />
          <map key='[OrderDate]' value='[FactInternetSales].[OrderDate]' />
          <map key='[OrderQuantity]' value='[FactInternetSales].[OrderQuantity]' />
          <map key='[ProductAlternateKey]' value='[DimProduct].[ProductAlternateKey]' />
          <map key='[ProductCategoryAlternateKey]' value='[DimProductCategory].[ProductCategoryAlternateKey]' />
          <map key='[ProductCategoryKey (DimProductSubcategory)]' value='[DimProductSubcategory].[ProductCategoryKey]' />
          <map key='[ProductCategoryKey]' value='[DimProductCategory].[ProductCategoryKey]' />
          <map key='[ProductKey (DimProduct)]' value='[DimProduct].[ProductKey]' />
          <map key='[ProductKey]' value='[FactInternetSales].[ProductKey]' />
          <map key='[ProductLine]' value='[DimProduct].[ProductLine]' />
          <map key='[ProductStandardCost]' value='[FactInternetSales].[ProductStandardCost]' />
          <map key='[ProductSubcategoryAlternateKey]' value='[DimProductSubcategory].[ProductSubcategoryAlternateKey]' />
          <map key='[ProductSubcategoryKey (DimProduct)]' value='[DimProduct].[ProductSubcategoryKey]' />
          <map key='[ProductSubcategoryKey]' value='[DimProductSubcategory].[ProductSubcategoryKey]' />
          <map key='[PromotionKey]' value='[FactInternetSales].[PromotionKey]' />
          <map key='[ReorderPoint]' value='[DimProduct].[ReorderPoint]' />
          <map key='[RevisionNumber]' value='[FactInternetSales].[RevisionNumber]' />
          <map key='[SafetyStockLevel]' value='[DimProduct].[SafetyStockLevel]' />
          <map key='[SalesAmount]' value='[FactInternetSales].[SalesAmount]' />
          <map key='[SalesOrderLineNumber]' value='[FactInternetSales].[SalesOrderLineNumber]' />
          <map key='[SalesOrderNumber]' value='[FactInternetSales].[SalesOrderNumber]' />
          <map key='[SalesTerritoryAlternateKey]' value='[DimSalesTerritory].[SalesTerritoryAlternateKey]' />
          <map key='[SalesTerritoryCountry]' value='[DimSalesTerritory].[SalesTerritoryCountry]' />
          <map key='[SalesTerritoryGroup]' value='[DimSalesTerritory].[SalesTerritoryGroup]' />
          <map key='[SalesTerritoryImage]' value='[DimSalesTerritory].[SalesTerritoryImage]' />
          <map key='[SalesTerritoryKey (DimSalesTerritory)]' value='[DimSalesTerritory].[SalesTerritoryKey]' />
          <map key='[SalesTerritoryKey]' value='[FactInternetSales].[SalesTerritoryKey]' />
          <map key='[SalesTerritoryRegion]' value='[DimSalesTerritory].[SalesTerritoryRegion]' />
          <map key='[ShipDateKey]' value='[FactInternetSales].[ShipDateKey]' />
          <map key='[ShipDate]' value='[FactInternetSales].[ShipDate]' />
          <map key='[SizeRange]' value='[DimProduct].[SizeRange]' />
          <map key='[SizeUnitMeasureCode]' value='[DimProduct].[SizeUnitMeasureCode]' />
          <map key='[Size]' value='[DimProduct].[Size]' />
          <map key='[SpanishDayNameOfWeek]' value='[DimDate].[SpanishDayNameOfWeek]' />
          <map key='[SpanishMonthName]' value='[DimDate].[SpanishMonthName]' />
          <map key='[SpanishProductCategoryName]' value='[DimProductCategory].[SpanishProductCategoryName]' />
          <map key='[SpanishProductName]' value='[DimProduct].[SpanishProductName]' />
          <map key='[SpanishProductSubcategoryName]' value='[DimProductSubcategory].[SpanishProductSubcategoryName]' />
          <map key='[StandardCost]' value='[DimProduct].[StandardCost]' />
          <map key='[StartDate]' value='[DimProduct].[StartDate]' />
          <map key='[Status]' value='[DimProduct].[Status]' />
          <map key='[Style]' value='[DimProduct].[Style]' />
          <map key='[TaxAmt]' value='[FactInternetSales].[TaxAmt]' />
          <map key='[ThaiDescription]' value='[DimProduct].[ThaiDescription]' />
          <map key='[TotalProductCost]' value='[FactInternetSales].[TotalProductCost]' />
          <map key='[TurkishDescription]' value='[DimProduct].[TurkishDescription]' />
          <map key='[UnitPriceDiscountPct]' value='[FactInternetSales].[UnitPriceDiscountPct]' />
          <map key='[UnitPrice]' value='[FactInternetSales].[UnitPrice]' />
          <map key='[WeekNumberOfYear]' value='[DimDate].[WeekNumberOfYear]' />
          <map key='[WeightUnitMeasureCode]' value='[DimProduct].[WeightUnitMeasureCode]' />
          <map key='[Weight]' value='[DimProduct].[Weight]' />
        </cols>
        <metadata-records>
          <metadata-record class='column'>
            <remote-name>ProductCategoryKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ProductCategoryKey]</local-name>
            <parent-name>[DimProductCategory]</parent-name>
            <remote-alias>ProductCategoryKey</remote-alias>
            <ordinal>1</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductCategory_B7603872C0DE49DBAB7949F0D2E44B7A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductCategoryAlternateKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ProductCategoryAlternateKey]</local-name>
            <parent-name>[DimProductCategory]</parent-name>
            <remote-alias>ProductCategoryAlternateKey</remote-alias>
            <ordinal>2</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductCategory_B7603872C0DE49DBAB7949F0D2E44B7A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>EnglishProductCategoryName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[EnglishProductCategoryName]</local-name>
            <parent-name>[DimProductCategory]</parent-name>
            <remote-alias>EnglishProductCategoryName</remote-alias>
            <ordinal>3</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductCategory_B7603872C0DE49DBAB7949F0D2E44B7A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SpanishProductCategoryName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SpanishProductCategoryName]</local-name>
            <parent-name>[DimProductCategory]</parent-name>
            <remote-alias>SpanishProductCategoryName</remote-alias>
            <ordinal>4</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductCategory_B7603872C0DE49DBAB7949F0D2E44B7A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FrenchProductCategoryName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[FrenchProductCategoryName]</local-name>
            <parent-name>[DimProductCategory]</parent-name>
            <remote-alias>FrenchProductCategoryName</remote-alias>
            <ordinal>5</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductCategory_B7603872C0DE49DBAB7949F0D2E44B7A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductSubcategoryKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ProductSubcategoryKey]</local-name>
            <parent-name>[DimProductSubcategory]</parent-name>
            <remote-alias>ProductSubcategoryKey</remote-alias>
            <ordinal>7</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductSubcategory_AD2268E558794BFCA99892F6E071319B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductSubcategoryAlternateKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ProductSubcategoryAlternateKey]</local-name>
            <parent-name>[DimProductSubcategory]</parent-name>
            <remote-alias>ProductSubcategoryAlternateKey</remote-alias>
            <ordinal>8</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductSubcategory_AD2268E558794BFCA99892F6E071319B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>EnglishProductSubcategoryName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[EnglishProductSubcategoryName]</local-name>
            <parent-name>[DimProductSubcategory]</parent-name>
            <remote-alias>EnglishProductSubcategoryName</remote-alias>
            <ordinal>9</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductSubcategory_AD2268E558794BFCA99892F6E071319B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SpanishProductSubcategoryName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SpanishProductSubcategoryName]</local-name>
            <parent-name>[DimProductSubcategory]</parent-name>
            <remote-alias>SpanishProductSubcategoryName</remote-alias>
            <ordinal>10</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductSubcategory_AD2268E558794BFCA99892F6E071319B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FrenchProductSubcategoryName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[FrenchProductSubcategoryName]</local-name>
            <parent-name>[DimProductSubcategory]</parent-name>
            <remote-alias>FrenchProductSubcategoryName</remote-alias>
            <ordinal>11</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductSubcategory_AD2268E558794BFCA99892F6E071319B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductCategoryKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ProductCategoryKey (DimProductSubcategory)]</local-name>
            <parent-name>[DimProductSubcategory]</parent-name>
            <remote-alias>ProductCategoryKey</remote-alias>
            <ordinal>12</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductSubcategory_AD2268E558794BFCA99892F6E071319B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ProductKey (DimProduct)]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ProductKey</remote-alias>
            <ordinal>14</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductAlternateKey</remote-name>
            <remote-type>130</remote-type>
            <local-name>[ProductAlternateKey]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ProductAlternateKey</remote-alias>
            <ordinal>15</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>25</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductSubcategoryKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ProductSubcategoryKey (DimProduct)]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ProductSubcategoryKey</remote-alias>
            <ordinal>16</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>WeightUnitMeasureCode</remote-name>
            <remote-type>130</remote-type>
            <local-name>[WeightUnitMeasureCode]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>WeightUnitMeasureCode</remote-alias>
            <ordinal>17</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>3</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SizeUnitMeasureCode</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SizeUnitMeasureCode]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>SizeUnitMeasureCode</remote-alias>
            <ordinal>18</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>3</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>EnglishProductName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[EnglishProductName]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>EnglishProductName</remote-alias>
            <ordinal>19</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SpanishProductName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SpanishProductName]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>SpanishProductName</remote-alias>
            <ordinal>20</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FrenchProductName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[FrenchProductName]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>FrenchProductName</remote-alias>
            <ordinal>21</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>StandardCost</remote-name>
            <remote-type>131</remote-type>
            <local-name>[StandardCost]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>StandardCost</remote-alias>
            <ordinal>22</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FinishedGoodsFlag</remote-name>
            <remote-type>11</remote-type>
            <local-name>[FinishedGoodsFlag]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>FinishedGoodsFlag</remote-alias>
            <ordinal>23</ordinal>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_BIT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_BIT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Color</remote-name>
            <remote-type>130</remote-type>
            <local-name>[Color]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>Color</remote-alias>
            <ordinal>24</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>15</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SafetyStockLevel</remote-name>
            <remote-type>2</remote-type>
            <local-name>[SafetyStockLevel]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>SafetyStockLevel</remote-alias>
            <ordinal>25</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>5</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_SMALLINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SSHORT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ReorderPoint</remote-name>
            <remote-type>2</remote-type>
            <local-name>[ReorderPoint]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ReorderPoint</remote-alias>
            <ordinal>26</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>5</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_SMALLINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SSHORT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ListPrice</remote-name>
            <remote-type>131</remote-type>
            <local-name>[ListPrice]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ListPrice</remote-alias>
            <ordinal>27</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Size</remote-name>
            <remote-type>130</remote-type>
            <local-name>[Size]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>Size</remote-alias>
            <ordinal>28</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SizeRange</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SizeRange]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>SizeRange</remote-alias>
            <ordinal>29</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Weight</remote-name>
            <remote-type>5</remote-type>
            <local-name>[Weight]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>Weight</remote-alias>
            <ordinal>30</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>15</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_FLOAT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_DOUBLE&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DaysToManufacture</remote-name>
            <remote-type>3</remote-type>
            <local-name>[DaysToManufacture]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>DaysToManufacture</remote-alias>
            <ordinal>31</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductLine</remote-name>
            <remote-type>130</remote-type>
            <local-name>[ProductLine]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ProductLine</remote-alias>
            <ordinal>32</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>2</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DealerPrice</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DealerPrice]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>DealerPrice</remote-alias>
            <ordinal>33</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Class</remote-name>
            <remote-type>130</remote-type>
            <local-name>[Class]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>Class</remote-alias>
            <ordinal>34</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>2</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Style</remote-name>
            <remote-type>130</remote-type>
            <local-name>[Style]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>Style</remote-alias>
            <ordinal>35</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>2</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ModelName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[ModelName]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ModelName</remote-alias>
            <ordinal>36</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>LargePhoto</remote-name>
            <remote-type>128</remote-type>
            <local-name>[LargePhoto]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>LargePhoto</remote-alias>
            <ordinal>37</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='0' name='LROOT' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARBINARY&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_BINARY&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>EnglishDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[EnglishDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>EnglishDescription</remote-alias>
            <ordinal>38</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FrenchDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[FrenchDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>FrenchDescription</remote-alias>
            <ordinal>39</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ChineseDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[ChineseDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ChineseDescription</remote-alias>
            <ordinal>40</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ArabicDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[ArabicDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ArabicDescription</remote-alias>
            <ordinal>41</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>HebrewDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[HebrewDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>HebrewDescription</remote-alias>
            <ordinal>42</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ThaiDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[ThaiDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ThaiDescription</remote-alias>
            <ordinal>43</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>GermanDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[GermanDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>GermanDescription</remote-alias>
            <ordinal>44</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>JapaneseDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[JapaneseDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>JapaneseDescription</remote-alias>
            <ordinal>45</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>TurkishDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[TurkishDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>TurkishDescription</remote-alias>
            <ordinal>46</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>StartDate</remote-name>
            <remote-type>7</remote-type>
            <local-name>[StartDate]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>StartDate</remote-alias>
            <ordinal>47</ordinal>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_TIMESTAMP&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_TIMESTAMP&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>EndDate</remote-name>
            <remote-type>7</remote-type>
            <local-name>[EndDate]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>EndDate</remote-alias>
            <ordinal>48</ordinal>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_TIMESTAMP&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_TIMESTAMP&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Status</remote-name>
            <remote-type>130</remote-type>
            <local-name>[Status]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>Status</remote-alias>
            <ordinal>49</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>7</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ProductKey]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>ProductKey</remote-alias>
            <ordinal>51</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>OrderDateKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[OrderDateKey]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>OrderDateKey</remote-alias>
            <ordinal>52</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DueDateKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[DueDateKey]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>DueDateKey</remote-alias>
            <ordinal>53</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ShipDateKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ShipDateKey]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>ShipDateKey</remote-alias>
            <ordinal>54</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CustomerKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[CustomerKey]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>CustomerKey</remote-alias>
            <ordinal>55</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PromotionKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[PromotionKey]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>PromotionKey</remote-alias>
            <ordinal>56</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CurrencyKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[CurrencyKey]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>CurrencyKey</remote-alias>
            <ordinal>57</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesTerritoryKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[SalesTerritoryKey]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>SalesTerritoryKey</remote-alias>
            <ordinal>58</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesOrderNumber</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SalesOrderNumber]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>SalesOrderNumber</remote-alias>
            <ordinal>59</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>20</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesOrderLineNumber</remote-name>
            <remote-type>17</remote-type>
            <local-name>[SalesOrderLineNumber]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>SalesOrderLineNumber</remote-alias>
            <ordinal>60</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>RevisionNumber</remote-name>
            <remote-type>17</remote-type>
            <local-name>[RevisionNumber]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>RevisionNumber</remote-alias>
            <ordinal>61</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>OrderQuantity</remote-name>
            <remote-type>2</remote-type>
            <local-name>[OrderQuantity]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>OrderQuantity</remote-alias>
            <ordinal>62</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>5</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_SMALLINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SSHORT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>UnitPrice</remote-name>
            <remote-type>131</remote-type>
            <local-name>[UnitPrice]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>UnitPrice</remote-alias>
            <ordinal>63</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ExtendedAmount</remote-name>
            <remote-type>131</remote-type>
            <local-name>[ExtendedAmount]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>ExtendedAmount</remote-alias>
            <ordinal>64</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>UnitPriceDiscountPct</remote-name>
            <remote-type>5</remote-type>
            <local-name>[UnitPriceDiscountPct]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>UnitPriceDiscountPct</remote-alias>
            <ordinal>65</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>15</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_FLOAT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_DOUBLE&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DiscountAmount</remote-name>
            <remote-type>5</remote-type>
            <local-name>[DiscountAmount]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>DiscountAmount</remote-alias>
            <ordinal>66</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>15</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_FLOAT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_DOUBLE&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductStandardCost</remote-name>
            <remote-type>131</remote-type>
            <local-name>[ProductStandardCost]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>ProductStandardCost</remote-alias>
            <ordinal>67</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>TotalProductCost</remote-name>
            <remote-type>131</remote-type>
            <local-name>[TotalProductCost]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>TotalProductCost</remote-alias>
            <ordinal>68</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesAmount</remote-name>
            <remote-type>131</remote-type>
            <local-name>[SalesAmount]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>SalesAmount</remote-alias>
            <ordinal>69</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>TaxAmt</remote-name>
            <remote-type>131</remote-type>
            <local-name>[TaxAmt]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>TaxAmt</remote-alias>
            <ordinal>70</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Freight</remote-name>
            <remote-type>131</remote-type>
            <local-name>[Freight]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>Freight</remote-alias>
            <ordinal>71</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CarrierTrackingNumber</remote-name>
            <remote-type>130</remote-type>
            <local-name>[CarrierTrackingNumber]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>CarrierTrackingNumber</remote-alias>
            <ordinal>72</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>25</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CustomerPONumber</remote-name>
            <remote-type>130</remote-type>
            <local-name>[CustomerPONumber]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>CustomerPONumber</remote-alias>
            <ordinal>73</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>25</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>OrderDate</remote-name>
            <remote-type>7</remote-type>
            <local-name>[OrderDate]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>OrderDate</remote-alias>
            <ordinal>74</ordinal>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_TIMESTAMP&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_TIMESTAMP&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DueDate</remote-name>
            <remote-type>7</remote-type>
            <local-name>[DueDate]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>DueDate</remote-alias>
            <ordinal>75</ordinal>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_TIMESTAMP&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_TIMESTAMP&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ShipDate</remote-name>
            <remote-type>7</remote-type>
            <local-name>[ShipDate]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>ShipDate</remote-alias>
            <ordinal>76</ordinal>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_TIMESTAMP&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_TIMESTAMP&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DateKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[DateKey]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>DateKey</remote-alias>
            <ordinal>78</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FullDateAlternateKey</remote-name>
            <remote-type>7</remote-type>
            <local-name>[FullDateAlternateKey]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>FullDateAlternateKey</remote-alias>
            <ordinal>79</ordinal>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_DATE&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_DATE&quot;</attribute>
              <attribute datatype='boolean' name='TypeIsDateTime2orDate'>true</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DayNumberOfWeek</remote-name>
            <remote-type>17</remote-type>
            <local-name>[DayNumberOfWeek]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>DayNumberOfWeek</remote-alias>
            <ordinal>80</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>EnglishDayNameOfWeek</remote-name>
            <remote-type>130</remote-type>
            <local-name>[EnglishDayNameOfWeek]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>EnglishDayNameOfWeek</remote-alias>
            <ordinal>81</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>10</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SpanishDayNameOfWeek</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SpanishDayNameOfWeek]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>SpanishDayNameOfWeek</remote-alias>
            <ordinal>82</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>10</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FrenchDayNameOfWeek</remote-name>
            <remote-type>130</remote-type>
            <local-name>[FrenchDayNameOfWeek]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>FrenchDayNameOfWeek</remote-alias>
            <ordinal>83</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>10</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DayNumberOfMonth</remote-name>
            <remote-type>17</remote-type>
            <local-name>[DayNumberOfMonth]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>DayNumberOfMonth</remote-alias>
            <ordinal>84</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DayNumberOfYear</remote-name>
            <remote-type>2</remote-type>
            <local-name>[DayNumberOfYear]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>DayNumberOfYear</remote-alias>
            <ordinal>85</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>5</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_SMALLINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SSHORT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>WeekNumberOfYear</remote-name>
            <remote-type>17</remote-type>
            <local-name>[WeekNumberOfYear]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>WeekNumberOfYear</remote-alias>
            <ordinal>86</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>EnglishMonthName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[EnglishMonthName]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>EnglishMonthName</remote-alias>
            <ordinal>87</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>10</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SpanishMonthName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SpanishMonthName]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>SpanishMonthName</remote-alias>
            <ordinal>88</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>10</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FrenchMonthName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[FrenchMonthName]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>FrenchMonthName</remote-alias>
            <ordinal>89</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>10</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>MonthNumberOfYear</remote-name>
            <remote-type>17</remote-type>
            <local-name>[MonthNumberOfYear]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>MonthNumberOfYear</remote-alias>
            <ordinal>90</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CalendarQuarter</remote-name>
            <remote-type>17</remote-type>
            <local-name>[CalendarQuarter]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>CalendarQuarter</remote-alias>
            <ordinal>91</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CalendarYear</remote-name>
            <remote-type>2</remote-type>
            <local-name>[CalendarYear]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>CalendarYear</remote-alias>
            <ordinal>92</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>5</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_SMALLINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SSHORT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CalendarSemester</remote-name>
            <remote-type>17</remote-type>
            <local-name>[CalendarSemester]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>CalendarSemester</remote-alias>
            <ordinal>93</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FiscalQuarter</remote-name>
            <remote-type>17</remote-type>
            <local-name>[FiscalQuarter]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>FiscalQuarter</remote-alias>
            <ordinal>94</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FiscalYear</remote-name>
            <remote-type>2</remote-type>
            <local-name>[FiscalYear]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>FiscalYear</remote-alias>
            <ordinal>95</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>5</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_SMALLINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SSHORT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FiscalSemester</remote-name>
            <remote-type>17</remote-type>
            <local-name>[FiscalSemester]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>FiscalSemester</remote-alias>
            <ordinal>96</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesTerritoryKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[SalesTerritoryKey (DimSalesTerritory)]</local-name>
            <parent-name>[DimSalesTerritory]</parent-name>
            <remote-alias>SalesTerritoryKey</remote-alias>
            <ordinal>98</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesTerritoryAlternateKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[SalesTerritoryAlternateKey]</local-name>
            <parent-name>[DimSalesTerritory]</parent-name>
            <remote-alias>SalesTerritoryAlternateKey</remote-alias>
            <ordinal>99</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesTerritoryRegion</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SalesTerritoryRegion]</local-name>
            <parent-name>[DimSalesTerritory]</parent-name>
            <remote-alias>SalesTerritoryRegion</remote-alias>
            <ordinal>100</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesTerritoryCountry</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SalesTerritoryCountry]</local-name>
            <parent-name>[DimSalesTerritory]</parent-name>
            <remote-alias>SalesTerritoryCountry</remote-alias>
            <ordinal>101</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesTerritoryGroup</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SalesTerritoryGroup]</local-name>
            <parent-name>[DimSalesTerritory]</parent-name>
            <remote-alias>SalesTerritoryGroup</remote-alias>
            <ordinal>102</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesTerritoryImage</remote-name>
            <remote-type>128</remote-type>
            <local-name>[SalesTerritoryImage]</local-name>
            <parent-name>[DimSalesTerritory]</parent-name>
            <remote-alias>SalesTerritoryImage</remote-alias>
            <ordinal>103</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='0' name='LROOT' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARBINARY&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_BINARY&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
        </metadata-records>
      </connection>
      <aliases enabled='yes' />
      <column caption='Arabic Description' datatype='string' name='[ArabicDescription]' role='dimension' type='nominal' />
      <column caption='CLEAR FILTERS' datatype='string' name='[Calculation_295830217743659008]' role='dimension' type='nominal'>
        <calculation class='tableau' formula='&apos;Clear All&apos;' />
      </column>
      <column aggregation='Sum' caption='Quarter' datatype='integer' name='[Calculation_447545216406446083]' role='dimension' type='ordinal'>
        <calculation class='tableau' formula='QUARTER([OrderDate])' />
      </column>
      <column aggregation='Sum' caption='Month' datatype='integer' name='[Calculation_447545216414334984]' role='dimension' type='ordinal'>
        <calculation class='tableau' formula='MONTH([OrderDate])' />
      </column>
      <column caption='MonthName' datatype='string' name='[Calculation_447545216416518153]' role='dimension' type='nominal'>
        <calculation class='tableau' formula='DATENAME(&apos;month&apos;,[OrderDate])' />
      </column>
      <column caption='MonthShortName' datatype='string' name='[Calculation_447545216416817162]' role='dimension' type='nominal'>
        <calculation class='tableau' formula='LEFT(DATENAME(&apos;month&apos;,[OrderDate]),3)' />
      </column>
      <column aggregation='Sum' caption='Weekday' datatype='integer' name='[Calculation_447545216417103883]' role='dimension' type='ordinal'>
        <calculation class='tableau' formula='DATEPART(&apos;weekday&apos;,[OrderDate])' />
      </column>
      <column caption='WeekdayName' datatype='string' name='[Calculation_447545216417656844]' role='dimension' type='nominal'>
        <calculation class='tableau' formula='DATENAME(&apos;weekday&apos;,[OrderDate])' />
      </column>
      <column caption='WeekEnd/WeekDay' datatype='string' name='[Calculation_447545216417820685]' role='dimension' type='nominal'>
        <calculation class='tableau' formula='IF [Calculation_447545216417103883]=1 or [Calculation_447545216417103883]=7 THEN &quot;WeekEnd&quot; &#13;&#10;ELSE &quot;WeekDay&quot;&#13;&#10;END' />
      </column>
      <column aggregation='Sum' caption='FinacialMonth' datatype='integer' name='[Calculation_447545216418512910]' role='dimension' type='ordinal'>
        <calculation class='tableau' formula='IF MONTH([OrderDate])-3&gt;0&#13;&#10;THEN MONTH([OrderDate])-3&#13;&#10;ELSE MONTH([OrderDate])+12-3&#13;&#10;END' />
      </column>
      <column aggregation='Sum' caption='Finacial Quarter' datatype='integer' name='[Calculation_447545216418717711]' role='dimension' type='ordinal'>
        <calculation class='tableau' formula='DATEPART(&apos;quarter&apos;,DATEADD(&apos;month&apos;,-3,[OrderDate]))' />
      </column>
      <column caption='YearMonth' datatype='string' name='[Calculation_447545216419405840]' role='dimension' type='nominal'>
        <calculation class='tableau' formula='DATENAME(&apos;year&apos;,[OrderDate])+&quot;-&quot;+DATENAME(&apos;month&apos;,[OrderDate])' />
      </column>
      <column caption='Month,Day,Year' datatype='integer' name='[Calculation_447545216421498898]' role='measure' type='quantitative'>
        <calculation class='tableau' formula='(DATEPART(&apos;year&apos;, [OrderDate])*10000 + DATEPART(&apos;month&apos;, [OrderDate])*100 + DATEPART(&apos;day&apos;, [OrderDate]))' />
      </column>
      <column caption='Profit' datatype='real' name='[Calculation_447545216423890964]' role='measure' type='quantitative'>
        <calculation class='tableau' formula='([SalesAmount])-([TotalProductCost])' />
      </column>
      <column caption='DM' datatype='real' name='[Calculation_447545216423968789]' role='measure' type='quantitative'>
        <calculation class='tableau' formula='IF [Parameters].[Parameter 2]==&quot;PROFIT&quot; THEN [Calculation_447545216423890964]&#13;&#10;ELSEIF [Parameters].[Parameter 2]==&quot;SALES&quot; THEN [SalesAmount]&#13;&#10;ELSEIF [Parameters].[Parameter 2]==&quot;COST&quot; THEN [TotalProductCost]&#13;&#10;ELSE 0&#13;&#10;END' />
      </column>
      <column caption='OrderDate' datatype='date' name='[Calculation_474848296898699264]' role='dimension' type='ordinal'>
        <calculation class='tableau' formula='DATE(LEFT(STR([OrderDateKey]),4)+&quot;-&quot;+MID(STR([OrderDateKey]),5,2)+&quot;-&quot;+RIGHT(STR([OrderDateKey]),2))' />
      </column>
      <column caption='Year' datatype='integer' name='[Calculation_561261115181088768]' role='dimension' type='ordinal'>
        <calculation class='tableau' formula='YEAR([OrderDate])' />
      </column>
      <column caption='Reset Filters' datatype='string' name='[Calculation_778278323468275715]' role='dimension' type='nominal'>
        <calculation class='tableau' formula='&apos;Reset Filters&apos;' />
      </column>
      <column caption='Calendar Quarter' datatype='integer' name='[CalendarQuarter]' role='dimension' type='quantitative' />
      <column caption='Calendar Semester' datatype='integer' name='[CalendarSemester]' role='measure' type='quantitative' />
      <column caption='Calendar Year' datatype='integer' name='[CalendarYear]' role='dimension' type='quantitative' />
      <column caption='Carrier Tracking Number' datatype='string' name='[CarrierTrackingNumber]' role='dimension' type='nominal' />
      <column caption='Chinese Description' datatype='string' name='[ChineseDescription]' role='dimension' type='nominal' />
      <column aggregation='Sum' caption='Currency Key' datatype='integer' name='[CurrencyKey]' role='dimension' type='ordinal' />
      <column aggregation='Sum' caption='Customer Key' datatype='integer' name='[CustomerKey]' role='dimension' type='ordinal' />
      <column caption='Customer PO Number' datatype='string' name='[CustomerPONumber]' role='dimension' type='nominal' />
      <column aggregation='Sum' caption='Date Key' datatype='integer' name='[DateKey]' role='dimension' type='ordinal' />
      <column caption='Day Number Of Month' datatype='integer' name='[DayNumberOfMonth]' role='dimension' type='quantitative' />
      <column caption='Day Number Of Week' datatype='integer' name='[DayNumberOfWeek]' role='dimension' type='quantitative' />
      <column caption='Day Number Of Year' datatype='integer' name='[DayNumberOfYear]' role='dimension' type='quantitative' />
      <column caption='Days To Manufacture' datatype='integer' name='[DaysToManufacture]' role='measure' type='quantitative' />
      <column caption='Dealer Price' datatype='real' name='[DealerPrice]' role='measure' type='quantitative' />
      <column caption='Discount Amount' datatype='real' name='[DiscountAmount]' role='measure' type='quantitative' />
      <column aggregation='Sum' caption='Due Date Key' datatype='integer' name='[DueDateKey]' role='dimension' type='ordinal' />
      <column caption='Due Date' datatype='datetime' name='[DueDate]' role='dimension' type='ordinal' />
      <column caption='End Date' datatype='datetime' name='[EndDate]' role='dimension' type='ordinal' />
      <column caption='English Day Name Of Week' datatype='string' name='[EnglishDayNameOfWeek]' role='dimension' type='nominal' />
      <column caption='English Description' datatype='string' name='[EnglishDescription]' role='dimension' type='nominal' />
      <column caption='English Month Name' datatype='string' name='[EnglishMonthName]' role='dimension' type='nominal' />
      <column caption='English Product Category Name' datatype='string' name='[EnglishProductCategoryName]' role='dimension' type='nominal' />
      <column caption='English Product Name' datatype='string' name='[EnglishProductName]' role='dimension' type='nominal' />
      <column caption='English Product Subcategory Name' datatype='string' name='[EnglishProductSubcategoryName]' role='dimension' type='nominal' />
      <column caption='Extended Amount' datatype='real' name='[ExtendedAmount]' role='measure' type='quantitative' />
      <column caption='Finished Goods Flag' datatype='boolean' name='[FinishedGoodsFlag]' role='dimension' type='nominal' />
      <column caption='Fiscal Quarter' datatype='integer' name='[FiscalQuarter]' role='dimension' type='quantitative' />
      <column caption='Fiscal Semester' datatype='integer' name='[FiscalSemester]' role='measure' type='quantitative' />
      <column caption='Fiscal Year' datatype='integer' name='[FiscalYear]' role='dimension' type='quantitative' />
      <column caption='French Day Name Of Week' datatype='string' name='[FrenchDayNameOfWeek]' role='dimension' type='nominal' />
      <column caption='French Description' datatype='string' name='[FrenchDescription]' role='dimension' type='nominal' />
      <column caption='French Month Name' datatype='string' name='[FrenchMonthName]' role='dimension' type='nominal' />
      <column caption='French Product Category Name' datatype='string' name='[FrenchProductCategoryName]' role='dimension' type='nominal' />
      <column caption='French Product Name' datatype='string' name='[FrenchProductName]' role='dimension' type='nominal' />
      <column caption='French Product Subcategory Name' datatype='string' name='[FrenchProductSubcategoryName]' role='dimension' type='nominal' />
      <column caption='Full Date Alternate Key' datatype='date' name='[FullDateAlternateKey]' role='dimension' type='ordinal' />
      <column caption='German Description' datatype='string' name='[GermanDescription]' role='dimension' type='nominal' />
      <column caption='Hebrew Description' datatype='string' name='[HebrewDescription]' role='dimension' type='nominal' />
      <column caption='Japanese Description' datatype='string' name='[JapaneseDescription]' role='dimension' type='nominal' />
      <column caption='Large Photo' datatype='string' name='[LargePhoto]' role='dimension' type='nominal' />
      <column caption='List Price' datatype='real' name='[ListPrice]' role='measure' type='quantitative' />
      <column caption='Model Name' datatype='string' name='[ModelName]' role='dimension' type='nominal' />
      <column caption='Month Number Of Year' datatype='integer' name='[MonthNumberOfYear]' role='dimension' type='quantitative' />
      <column aggregation='Sum' caption='Order Date Key' datatype='integer' name='[OrderDateKey]' role='dimension' type='ordinal' />
      <column caption='Order Date' datatype='datetime' name='[OrderDate]' role='dimension' type='ordinal' />
      <column caption='Order Quantity' datatype='integer' name='[OrderQuantity]' role='measure' type='quantitative' />
      <column caption='Product Alternate Key' datatype='string' name='[ProductAlternateKey]' role='dimension' type='nominal' />
      <column caption='Product Category Alternate Key' datatype='integer' name='[ProductCategoryAlternateKey]' role='dimension' type='ordinal' />
      <column aggregation='Sum' datatype='integer' name='[ProductCategoryKey (DimProductSubcategory)]' role='dimension' type='ordinal' />
      <column caption='Product Category Key' datatype='integer' name='[ProductCategoryKey]' role='dimension' type='ordinal' />
      <column aggregation='Sum' datatype='integer' name='[ProductKey (DimProduct)]' role='dimension' type='ordinal' />
      <column aggregation='Sum' caption='Product Key' datatype='integer' name='[ProductKey]' role='dimension' type='ordinal' />
      <column caption='Product Line' datatype='string' name='[ProductLine]' role='dimension' type='nominal' />
      <column caption='Product Standard Cost' datatype='real' name='[ProductStandardCost]' role='measure' type='quantitative' />
      <column caption='Product Subcategory Alternate Key' datatype='integer' name='[ProductSubcategoryAlternateKey]' role='dimension' type='ordinal' />
      <column aggregation='Sum' datatype='integer' name='[ProductSubcategoryKey (DimProduct)]' role='dimension' type='ordinal' />
      <column caption='Product Subcategory Key' datatype='integer' name='[ProductSubcategoryKey]' role='dimension' type='ordinal' />
      <column aggregation='Sum' caption='Promotion Key' datatype='integer' name='[PromotionKey]' role='dimension' type='ordinal' />
      <column caption='Reorder Point' datatype='integer' name='[ReorderPoint]' role='measure' type='quantitative' />
      <column caption='Revision Number' datatype='integer' name='[RevisionNumber]' role='dimension' type='ordinal' />
      <column caption='Safety Stock Level' datatype='integer' name='[SafetyStockLevel]' role='measure' type='quantitative' />
      <column caption='Sales Amount' datatype='real' name='[SalesAmount]' role='measure' type='quantitative' />
      <column caption='Sales Order Line Number' datatype='integer' name='[SalesOrderLineNumber]' role='dimension' type='ordinal' />
      <column caption='Sales Order Number' datatype='string' name='[SalesOrderNumber]' role='dimension' type='nominal' />
      <column caption='Sales Territory Alternate Key' datatype='integer' name='[SalesTerritoryAlternateKey]' role='dimension' type='ordinal' />
      <column caption='Sales Territory Country' datatype='string' name='[SalesTerritoryCountry]' role='dimension' semantic-role='[Country].[ISO3166_2]' type='nominal' />
      <column caption='Sales Territory Group' datatype='string' name='[SalesTerritoryGroup]' role='dimension' type='nominal' />
      <column caption='Sales Territory Image' datatype='string' name='[SalesTerritoryImage]' role='dimension' type='nominal' />
      <column aggregation='Sum' datatype='integer' name='[SalesTerritoryKey (DimSalesTerritory)]' role='dimension' type='ordinal' />
      <column aggregation='Sum' caption='Sales Territory Key' datatype='integer' name='[SalesTerritoryKey]' role='dimension' type='ordinal' />
      <column caption='Sales Territory Region' datatype='string' name='[SalesTerritoryRegion]' role='dimension' type='nominal' />
      <column aggregation='Sum' caption='Ship Date Key' datatype='integer' name='[ShipDateKey]' role='dimension' type='ordinal' />
      <column caption='Ship Date' datatype='datetime' name='[ShipDate]' role='dimension' type='ordinal' />
      <column caption='Size Range' datatype='string' name='[SizeRange]' role='dimension' type='nominal' />
      <column caption='Size Unit Measure Code' datatype='string' name='[SizeUnitMeasureCode]' role='dimension' type='nominal' />
      <column caption='Spanish Day Name Of Week' datatype='string' name='[SpanishDayNameOfWeek]' role='dimension' type='nominal' />
      <column caption='Spanish Month Name' datatype='string' name='[SpanishMonthName]' role='dimension' type='nominal' />
      <column caption='Spanish Product Category Name' datatype='string' name='[SpanishProductCategoryName]' role='dimension' type='nominal' />
      <column caption='Spanish Product Name' datatype='string' name='[SpanishProductName]' role='dimension' type='nominal' />
      <column caption='Spanish Product Subcategory Name' datatype='string' name='[SpanishProductSubcategoryName]' role='dimension' type='nominal' />
      <column caption='Standard Cost' datatype='real' name='[StandardCost]' role='measure' type='quantitative' />
      <column caption='Start Date' datatype='datetime' name='[StartDate]' role='dimension' type='ordinal' />
      <column caption='Tax Amt' datatype='real' name='[TaxAmt]' role='measure' type='quantitative' />
      <column caption='Thai Description' datatype='string' name='[ThaiDescription]' role='dimension' type='nominal' />
      <column caption='Total Product Cost' datatype='real' name='[TotalProductCost]' role='measure' type='quantitative' />
      <column caption='Turkish Description' datatype='string' name='[TurkishDescription]' role='dimension' type='nominal' />
      <column caption='Unit Price Discount Pct' datatype='real' name='[UnitPriceDiscountPct]' role='measure' type='quantitative' />
      <column caption='Unit Price' datatype='real' name='[UnitPrice]' role='measure' type='quantitative' />
      <column caption='Week Number Of Year' datatype='integer' name='[WeekNumberOfYear]' role='dimension' type='quantitative' />
      <column caption='Weight Unit Measure Code' datatype='string' name='[WeightUnitMeasureCode]' role='dimension' type='nominal' />
      <_.fcp.ObjectModelTableType.true...column caption='DimDate' datatype='table' name='[__tableau_internal_object_id__].[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]' role='measure' type='quantitative' />
      <_.fcp.ObjectModelTableType.true...column caption='DimProductCategory' datatype='table' name='[__tableau_internal_object_id__].[DimProductCategory_B7603872C0DE49DBAB7949F0D2E44B7A]' role='measure' type='quantitative' />
      <_.fcp.ObjectModelTableType.true...column caption='DimProductSubcategory' datatype='table' name='[__tableau_internal_object_id__].[DimProductSubcategory_AD2268E558794BFCA99892F6E071319B]' role='measure' type='quantitative' />
      <_.fcp.ObjectModelTableType.true...column caption='DimProduct' datatype='table' name='[__tableau_internal_object_id__].[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]' role='measure' type='quantitative' />
      <_.fcp.ObjectModelTableType.true...column caption='DimSalesTerritory' datatype='table' name='[__tableau_internal_object_id__].[DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949]' role='measure' type='quantitative' />
      <_.fcp.ObjectModelTableType.true...column caption='Sales' datatype='table' name='[__tableau_internal_object_id__].[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]' role='measure' type='quantitative' />
      <column-instance column='[SalesTerritoryCountry]' derivation='Attribute' name='[attr:SalesTerritoryCountry:nk]' pivot='key' type='nominal' />
      <column-instance column='[SalesAmount]' derivation='Sum' name='[cum:sum:SalesAmount:qk]' pivot='key' type='quantitative'>
        <table-calc aggregation='Sum' ordering-type='Rows' type='CumTotal' />
      </column-instance>
      <column-instance column='[SalesAmount]' derivation='Sum' name='[diff:cum:sum:SalesAmount:qk]' pivot='key' type='quantitative'>
        <table-calc aggregation='Sum' ordering-type='Rows' type='CumTotal' />
        <table-calc diff-options='Relative' ordering-type='Rows' type='Difference'>
          <address>
            <value>-1</value>
          </address>
        </table-calc>
      </column-instance>
      <column-instance column='[SalesAmount]' derivation='Sum' name='[diff:sum:SalesAmount:qk]' pivot='key' type='quantitative'>
        <table-calc diff-options='Relative' ordering-type='Rows' type='Difference'>
          <address>
            <value>-1</value>
          </address>
        </table-calc>
      </column-instance>
      <column-instance column='[SalesTerritoryCountry]' derivation='Max' name='[max:SalesTerritoryCountry:nk]' pivot='key' type='nominal' />
      <column-instance column='[Calculation_447545216406446083]' derivation='None' name='[none:Calculation_447545216406446083:ok]' pivot='key' type='ordinal' />
      <column-instance column='[Calculation_561261115181088768]' derivation='None' name='[none:Calculation_561261115181088768:ok]' pivot='key' type='ordinal' />
      <column-instance column='[SalesTerritoryCountry]' derivation='None' name='[none:SalesTerritoryCountry:nk]' pivot='key' type='nominal' />
      <column-instance column='[SalesAmount]' derivation='Sum' name='[pcto:cum:sum:SalesAmount:qk]' pivot='key' type='quantitative'>
        <table-calc aggregation='Sum' ordering-type='Rows' type='CumTotal' />
        <table-calc ordering-type='Rows' type='PctTotal' />
      </column-instance>
      <column-instance column='[SalesAmount]' derivation='Sum' name='[pcto:sum:SalesAmount:qk]' pivot='key' type='quantitative'>
        <table-calc ordering-type='Rows' type='PctTotal' />
      </column-instance>
      <column-instance column='[Calculation_474848296898699264]' derivation='Quarter' name='[qr:Calculation_474848296898699264:ok]' pivot='key' type='ordinal' />
      <column-instance column='[SalesAmount]' derivation='Sum' name='[rank:sum:SalesAmount:qk:2]' pivot='key' type='quantitative'>
        <table-calc ordering-field='[federated.1nl426t13auwkc10rmst40m1iuwe].[EnglishMonthName]' ordering-type='Field' rank-options='Competition,Descending' type='Rank' />
      </column-instance>
      <column-instance column='[SalesAmount]' derivation='Sum' name='[rank:sum:SalesAmount:qk:3]' pivot='key' type='quantitative'>
        <table-calc ordering-field='[federated.1nl426t13auwkc10rmst40m1iuwe].[SalesTerritoryCountry]' ordering-type='Field' rank-options='Competition,Descending' type='Rank' />
      </column-instance>
      <column-instance column='[SalesAmount]' derivation='Sum' name='[rank:sum:SalesAmount:qk:4]' pivot='key' type='quantitative'>
        <table-calc ordering-type='CellInPane' rank-options='Competition,Descending' type='Rank' />
      </column-instance>
      <column-instance column='[SalesAmount]' derivation='Sum' name='[rank:sum:SalesAmount:qk]' pivot='key' type='quantitative'>
        <table-calc ordering-type='Rows' rank-options='Competition,Descending' type='Rank' />
      </column-instance>
      <column-instance column='[SalesAmount]' derivation='Sum' name='[sum:SalesAmount:qk]' pivot='key' type='quantitative' />
      <column-instance column='[TotalProductCost]' derivation='Sum' name='[sum:TotalProductCost:qk]' pivot='key' type='quantitative' />
      <column-instance column='[Calculation_474848296898699264]' derivation='Year-Trunc' name='[tyr:Calculation_474848296898699264:ok]' pivot='key' type='ordinal' />
      <group caption='Action (CLEAR FILTERS)' hidden='true' name='[Action (CLEAR FILTERS)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_295830217743659008]' />
        </groupfilter>
      </group>
      <group caption='Action (Clear All)' hidden='true' name='[Action (Clear All)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_778278323462979586]' />
        </groupfilter>
      </group>
      <group caption='Action (MonthName,Sales Territory Country,QUARTER(OrderDate),YEAR(OrderDate))' hidden='true' name='[Action (MonthName,Sales Territory Country,QUARTER(OrderDate),YEAR(OrderDate))]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_561261115185774595]' />
          <groupfilter function='level-members' level='[SalesTerritoryCountry]' />
          <groupfilter function='level-members' level='[qr:Calculation_474848296898699264:ok]' />
          <groupfilter function='level-members' level='[tyr:Calculation_474848296898699264:ok]' />
        </groupfilter>
      </group>
      <group caption='Action (Reset Filters)' hidden='true' name='[Action (Reset Filters)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_778278323468275715]' />
        </groupfilter>
      </group>
      <group caption='Action (Reset)' hidden='true' name='[Action (Reset)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_778278323455827969]' />
        </groupfilter>
      </group>
      <group caption='Action (Sales Territory Country)' hidden='true' name='[Action (Sales Territory Country)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[SalesTerritoryCountry]' />
        </groupfilter>
      </group>
      <group caption='Action (YEAR(OrderDate))' hidden='true' name='[Action (YEAR(OrderDate))]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[tyr:Calculation_474848296898699264:ok]' />
        </groupfilter>
      </group>
      <group caption='Action (Year)' hidden='true' name='[Action (Year)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_561261115181088768]' />
        </groupfilter>
      </group>
      <group caption='Action (Year,Month,Quarter,Sales Territory Country)' hidden='true' name='[Action (Year,Month,Quarter,Sales Territory Country)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_561261115181088768]' />
          <groupfilter function='level-members' level='[Calculation_561261115184209922]' />
          <groupfilter function='level-members' level='[Calculation_561261115188973574]' />
          <groupfilter function='level-members' level='[SalesTerritoryCountry]' />
        </groupfilter>
      </group>
      <group caption='Action (Year,MonthName,Quarter,Sales Territory Country)' hidden='true' name='[Action (Year,MonthName,Quarter,Sales Territory Country)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_561261115181088768]' />
          <groupfilter function='level-members' level='[Calculation_561261115185774595]' />
          <groupfilter function='level-members' level='[Calculation_561261115188973574]' />
          <groupfilter function='level-members' level='[SalesTerritoryCountry]' />
        </groupfilter>
      </group>
      <group caption='Action (Year,Quarter)' hidden='true' name='[Action (Year,Quarter)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_561261115181088768]' />
          <groupfilter function='level-members' level='[Calculation_561261115188973574]' />
        </groupfilter>
      </group>
      <drill-paths>
        <drill-path name='Sales Territory Region, Sales Territory Country'>
          <field>[SalesTerritoryRegion]</field>
          <field>[SalesTerritoryCountry]</field>
        </drill-path>
      </drill-paths>
      <layout _.fcp.SchemaViewerObjectModel.false...dim-percentage='0.5' _.fcp.SchemaViewerObjectModel.false...measure-percentage='0.4' dim-ordering='alphabetic' measure-ordering='alphabetic' rowDisplayCount='300' show-aliased-fields='true' show-structure='true' />
      <style>
        <style-rule element='mark'>
          <encoding attr='color' field='[none:SalesTerritoryCountry:nk]' type='palette'>
            <map to='#4e79a7'>
              <bucket>&quot;Australia&quot;</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>&quot;United Kingdom&quot;</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>&quot;Germany&quot;</bucket>
            </map>
            <map to='#b07aa1'>
              <bucket>&quot;NA&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;France&quot;</bucket>
            </map>
            <map to='#edc948'>
              <bucket>&quot;United States&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;Canada&quot;</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[max:SalesTerritoryCountry:nk]' type='palette'>
            <map to='#4e79a7'>
              <bucket>&quot;Australia&quot;</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>&quot;United Kingdom&quot;</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>&quot;Germany&quot;</bucket>
            </map>
            <map to='#b07aa1'>
              <bucket>&quot;NA&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;France&quot;</bucket>
            </map>
            <map to='#edc948'>
              <bucket>&quot;United States&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;Canada&quot;</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[attr:SalesTerritoryCountry:nk]' type='palette'>
            <map to='#4e79a7'>
              <bucket>&quot;Australia&quot;</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>&quot;United Kingdom&quot;</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>&quot;Germany&quot;</bucket>
            </map>
            <map to='#b07aa1'>
              <bucket>&quot;NA&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;France&quot;</bucket>
            </map>
            <map to='#edc948'>
              <bucket>&quot;United States&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;Canada&quot;</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[none:Calculation_447545216406446083:ok]' type='palette'>
            <map to='#4e79a7'>
              <bucket>1</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>4</bucket>
            </map>
            <map to='#e15759'>
              <bucket>3</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>2</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[none:Calculation_561261115181088768:ok]' type='palette'>
            <map to='#4e79a7'>
              <bucket>2010</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>2014</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>2013</bucket>
            </map>
            <map to='#e15759'>
              <bucket>2012</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>2011</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[:Measure Names]' type='palette'>
            <map to='#4e79a7'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_597289912409763848:qk]&quot;</bucket>
            </map>
            <map to='#9c755f'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:TotalProductCost:qk]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[:Measure Names]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[none:Calculation_447545216406446083:ok]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_447545216407900165:qk]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_447545216407973894:qk]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_447545216407998471:qk]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_597289912418045966:qk]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:SalesAmount:qk]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[usr:Calculation_447545216407670788:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[cum:sum:SalesAmount:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[diff:cum:sum:SalesAmount:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[diff:sum:SalesAmount:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[pcto:cum:sum:SalesAmount:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[pcto:sum:SalesAmount:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[rank:sum:SalesAmount:qk:2]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[rank:sum:SalesAmount:qk:3]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[rank:sum:SalesAmount:qk:4]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[rank:sum:SalesAmount:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_597289912410996746:qk]&quot;</bucket>
            </map>
            <map to='#ff5500'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_597289912409743366:qk]&quot;</bucket>
            </map>
            <map to='#ffffff'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_597289912411025420:qk]&quot;</bucket>
            </map>
            <map to='#ffffff'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_597289912418058256:qk]&quot;</bucket>
            </map>
          </encoding>
        </style-rule>
      </style>
      <semantic-values>
        <semantic-value key='[Country].[Name]' value='&quot;India&quot;' />
      </semantic-values>
      <field-sort-info field-sort-order-type='custom-order'>
        <field-sort-custom-order field='DateKey' />
        <field-sort-custom-order field='FullDateAlternateKey' />
        <field-sort-custom-order field='DayNumberOfWeek' />
        <field-sort-custom-order field='EnglishDayNameOfWeek' />
        <field-sort-custom-order field='SpanishDayNameOfWeek' />
        <field-sort-custom-order field='FrenchDayNameOfWeek' />
        <field-sort-custom-order field='DayNumberOfMonth' />
        <field-sort-custom-order field='DayNumberOfYear' />
        <field-sort-custom-order field='WeekNumberOfYear' />
        <field-sort-custom-order field='EnglishMonthName' />
        <field-sort-custom-order field='SpanishMonthName' />
        <field-sort-custom-order field='FrenchMonthName' />
        <field-sort-custom-order field='MonthNumberOfYear' />
        <field-sort-custom-order field='CalendarQuarter' />
        <field-sort-custom-order field='CalendarYear' />
        <field-sort-custom-order field='CalendarSemester' />
        <field-sort-custom-order field='FiscalQuarter' />
        <field-sort-custom-order field='FiscalYear' />
        <field-sort-custom-order field='FiscalSemester' />
      </field-sort-info>
      <datasource-dependencies datasource='Parameters'>
        <column caption='DynamicValue' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;PROFIT&quot;'>
          <calculation class='tableau' formula='&quot;PROFIT&quot;' />
        </column>
      </datasource-dependencies>
      <_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
        <objects>
          <object caption='DimDate' id='DimDate_9EC66DEF2B65424CB216051BCC33CAC1'>
            <properties context=''>
              <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimDate' table='[dbo].[DimDate]' type='table' />
            </properties>
          </object>
          <object caption='DimProductCategory' id='DimProductCategory_B7603872C0DE49DBAB7949F0D2E44B7A'>
            <properties context=''>
              <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimProductCategory' table='[dbo].[DimProductCategory]' type='table' />
            </properties>
          </object>
          <object caption='DimProductSubcategory' id='DimProductSubcategory_AD2268E558794BFCA99892F6E071319B'>
            <properties context=''>
              <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimProductSubcategory' table='[dbo].[DimProductSubcategory]' type='table' />
            </properties>
          </object>
          <object caption='DimProduct' id='DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763'>
            <properties context=''>
              <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimProduct' table='[dbo].[DimProduct]' type='table' />
            </properties>
          </object>
          <object caption='DimSalesTerritory' id='DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949'>
            <properties context=''>
              <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimSalesTerritory' table='[dbo].[DimSalesTerritory]' type='table' />
            </properties>
          </object>
          <object caption='Sales' id='FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321'>
            <properties context=''>
              <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='FactInternetSales' table='[dbo].[FactInternetSales]' type='table' />
            </properties>
          </object>
        </objects>
        <relationships>
          <relationship>
            <expression op='='>
              <expression op='[OrderDateKey]' />
              <expression op='[DateKey]' />
            </expression>
            <first-end-point _.fcp.ObjectModelRelationshipPerfOptions.true...is-db-set-guaranteed-value='true' guaranteed-value='true' object-id='FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321' />
            <second-end-point _.fcp.ObjectModelRelationshipPerfOptions.true...is-db-set-unique-key='true' object-id='DimDate_9EC66DEF2B65424CB216051BCC33CAC1' unique-key='true' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[ProductCategoryKey]' />
              <expression op='[ProductCategoryKey (DimProductSubcategory)]' />
            </expression>
            <first-end-point _.fcp.ObjectModelRelationshipPerfOptions.true...is-db-set-unique-key='true' object-id='DimProductCategory_B7603872C0DE49DBAB7949F0D2E44B7A' unique-key='true' />
            <second-end-point object-id='DimProductSubcategory_AD2268E558794BFCA99892F6E071319B' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[ProductSubcategoryKey]' />
              <expression op='[ProductSubcategoryKey (DimProduct)]' />
            </expression>
            <first-end-point _.fcp.ObjectModelRelationshipPerfOptions.true...is-db-set-unique-key='true' object-id='DimProductSubcategory_AD2268E558794BFCA99892F6E071319B' unique-key='true' />
            <second-end-point object-id='DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[SalesTerritoryKey]' />
              <expression op='[SalesTerritoryKey (DimSalesTerritory)]' />
            </expression>
            <first-end-point _.fcp.ObjectModelRelationshipPerfOptions.true...is-db-set-guaranteed-value='true' guaranteed-value='true' object-id='FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321' />
            <second-end-point _.fcp.ObjectModelRelationshipPerfOptions.true...is-db-set-unique-key='true' object-id='DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949' unique-key='true' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[ProductKey (DimProduct)]' />
              <expression op='[ProductKey]' />
            </expression>
            <first-end-point _.fcp.ObjectModelRelationshipPerfOptions.true...is-db-set-unique-key='true' object-id='DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763' unique-key='true' />
            <second-end-point _.fcp.ObjectModelRelationshipPerfOptions.true...is-db-set-guaranteed-value='true' guaranteed-value='true' object-id='FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321' />
          </relationship>
        </relationships>
      </_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
    </datasource>
  </datasources>
  <mapsources>
    <mapsource name='Tableau' />
  </mapsources>
  <worksheets>
    <worksheet name=' Country wise Sales'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true' fontalignment='1' fontcolor='#9d7660' fontname='Tableau Semibold' fontsize='11' italic='true' underline='true'>Country wise Sales</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='FactInternetSales+ (AdventureWorksDW2022)' name='federated.1nl426t13auwkc10rmst40m1iuwe' />
          </datasources>
          <mapsources>
            <mapsource name='Tableau' />
          </mapsources>
          <datasource-dependencies datasource='federated.1nl426t13auwkc10rmst40m1iuwe'>
            <column caption='Sales Amount' datatype='real' name='[SalesAmount]' role='measure' type='quantitative' />
            <column caption='Sales Territory Country' datatype='string' name='[SalesTerritoryCountry]' role='dimension' semantic-role='[Country].[ISO3166_2]' type='nominal' />
            <column-instance column='[SalesTerritoryCountry]' derivation='None' name='[none:SalesTerritoryCountry:nk]' pivot='key' type='nominal' />
            <column-instance column='[SalesAmount]' derivation='Sum' name='[sum:SalesAmount:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <filter class='categorical' column='[federated.1nl426t13auwkc10rmst40m1iuwe].[none:SalesTerritoryCountry:nk]' filter-group='5'>
            <groupfilter function='union' user:ui-domain='database' user:ui-enumeration='inclusive' user:ui-marker='enumerate'>
              <groupfilter function='member' level='[none:SalesTerritoryCountry:nk]' member='&quot;Australia&quot;' />
              <groupfilter function='member' level='[none:SalesTerritoryCountry:nk]' member='&quot;Canada&quot;' />
              <groupfilter function='member' level='[none:SalesTerritoryCountry:nk]' member='&quot;France&quot;' />
              <groupfilter function='member' level='[none:SalesTerritoryCountry:nk]' member='&quot;Germany&quot;' />
              <groupfilter function='member' level='[none:SalesTerritoryCountry:nk]' member='&quot;United Kingdom&quot;' />
              <groupfilter function='member' level='[none:SalesTerritoryCountry:nk]' member='&quot;United States&quot;' />
            </groupfilter>
          </filter>
          <natural-sort column='[federated.1nl426t13auwkc10rmst40m1iuwe].[none:SalesTerritoryCountry:nk]' direction='DESC' />
          <slices>
            <column>[federated.1nl426t13auwkc10rmst40m1iuwe].[none:SalesTerritoryCountry:nk]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='axis'>
            <encoding attr='space' class='0' field='[federated.1nl426t13auwkc10rmst40m1iuwe].[Longitude (generated)]' field-type='quantitative' max='46598283.98457256' min='8871414.0795553122' projection='EPSG:3857' range-type='fixed' scope='cols' type='space' />
            <encoding attr='space' class='0' field='[federated.1nl426t13auwkc10rmst40m1iuwe].[Latitude (generated)]' field-type='quantitative' max='17179040.06295776' min='-12172777.184046622' projection='EPSG:3857' range-type='fixed' scope='rows' type='space' />
          </style-rule>
          <style-rule element='cell'>
            <format attr='border-width' value='2' />
            <format attr='border-style' value='solid' />
          </style-rule>
          <style-rule element='header'>
            <format attr='background-color' field='[federated.1nl426t13auwkc10rmst40m1iuwe].[none:SalesTerritoryCountry:nk]' value='#000000' />
          </style-rule>
          <style-rule element='mark'>
            <encoding attr='color' field='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:SalesAmount:qk]' type='custom-interpolated'>
              <color-palette custom='true' name='' type='ordered-sequential'>
                <color>#f1f1f1</color>
                <color>#eaf2f2</color>
                <color>#e3f3f3</color>
                <color>#dcf5f5</color>
                <color>#d5f6f6</color>
                <color>#cef8f8</color>
                <color>#c7f9f9</color>
                <color>#c0fafa</color>
                <color>#b8fcfc</color>
                <color>#b1fdfd</color>
                <color>#aaffff</color>
              </color-palette>
            </encoding>
          </style-rule>
          <style-rule element='pane'>
            <format attr='background-color' value='#f6eee3' />
            <format attr='border-color' data-class='subtotal' scope='rows' value='#000000' />
            <format attr='border-style' data-class='subtotal' scope='rows' value='solid' />
          </style-rule>
          <style-rule element='title'>
            <format attr='background-color' value='#d4d4d4' />
            <format attr='border-style' value='solid' />
            <format attr='border-width' value='1' />
          </style-rule>
          <style-rule element='map-layer'>
            <format attr='enabled' id='background' value='false' />
            <format attr='enabled' id='barrier_line-land-polygon' value='false' />
            <format attr='enabled' id='barrier_line-land-line' value='false' />
            <format attr='enabled' id='national_park' value='false' />
            <format attr='enabled' id='pitch' value='false' />
            <format attr='enabled' id='industrial' value='false' />
            <format attr='enabled' id='built-up-area' value='false' />
            <format attr='enabled' id='water' value='false' />
            <format attr='enabled' id='waterway-river-canal' value='false' />
            <format attr='enabled' id='aeroway-polygon' value='false' />
            <format attr='enabled' id='aeroway-runway' value='false' />
            <format attr='enabled' id='aeroway-taxiway' value='false' />
            <format attr='enabled' id='b01002_001e' value='false' />
            <format attr='enabled' id='b01002_002e' value='false' />
            <format attr='enabled' id='b01002_003e' value='false' />
            <format attr='enabled' id='dp02_0001e' value='false' />
            <format attr='enabled' id='dp02_0015e' value='false' />
            <format attr='enabled' id='dp03_0027e_plus_dp03_0029e' value='false' />
            <format attr='enabled' id='dp03_0028e' value='false' />
            <format attr='enabled' id='dp03_0030e_plus_dp03_0031e' value='false' />
            <format attr='enabled' id='dp03_0062e' value='false' />
            <format attr='enabled' id='dp03_0088e' value='false' />
            <format attr='enabled' id='dp04_0001e' value='false' />
            <format attr='enabled' id='dp04_0046e' value='false' />
            <format attr='enabled' id='dp04_0047e' value='false' />
            <format attr='enabled' id='dp04_0089e' value='false' />
            <format attr='enabled' id='dp05_0001e' value='false' />
            <format attr='enabled' id='dp05_0002e_div_dp05_0003e' value='false' />
            <format attr='enabled' id='dp05_0032e' value='false' />
            <format attr='enabled' id='dp05_0033e' value='false' />
            <format attr='enabled' id='dp05_0034e' value='false' />
            <format attr='enabled' id='dp05_0039e' value='false' />
            <format attr='enabled' id='dp05_0047e' value='false' />
            <format attr='enabled' id='dp05_0053e' value='false' />
            <format attr='enabled' id='dp05_0066e' value='false' />
            <format attr='enabled' id='dp05_0077e' value='false' />
          </style-rule>
          <style-rule element='map'>
            <format attr='washout' value='0' />
            <format attr='map-style' value='light' />
            <format attr='wrap' value='false' />
          </style-rule>
          <style-rule element='map-data-layer'>
            <format attr='palette' value='tableau-map-blue-green-light' />
            <format attr='geo-area-type' value='State' />
          </style-rule>
          <style-rule element='quick-filter'>
            <format attr='border-width' value='2' />
            <format attr='border-style' value='solid' />
          </style-rule>
          <style-rule element='data-highlighter'>
            <_.fcp.IndividualControlFormatting.true...format attr='color' field='[federated.1nl426t13auwkc10rmst40m1iuwe].[none:SalesTerritoryCountry:nk]' value='#ffffff' />
            <_.fcp.IndividualControlFormatting.true...format attr='font-size' field='[federated.1nl426t13auwkc10rmst40m1iuwe].[none:SalesTerritoryCountry:nk]' value='22' />
            <format attr='color' value='#f5f5f5' />
            <format attr='font-size' value='72' />
          </style-rule>
          <style-rule element='data-highlighter-title'>
            <format attr='title' field='[federated.1nl426t13auwkc10rmst40m1iuwe].[none:SalesTerritoryCountry:nk]' value='Highlight Sales Territory Country'>
              <formatted-text>
                <run>Highlight Sales Territory Country</run>
              </formatted-text>
            </format>
            <_.fcp.IndividualControlFormatting.true...format attr='color' field='[federated.1nl426t13auwkc10rmst40m1iuwe].[none:SalesTerritoryCountry:nk]' value='#ffffff' />
            <format attr='font-size' value='72' />
            <format attr='color' value='#ffffff' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
            <mark-sizing mark-sizing-setting='marks-scaling-off' />
            <encodings>
              <text column='[federated.1nl426t13auwkc10rmst40m1iuwe].[none:SalesTerritoryCountry:nk]' />
              <lod column='[federated.1nl426t13auwkc10rmst40m1iuwe].[none:SalesTerritoryCountry:nk]' />
              <size column='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:SalesAmount:qk]' />
            </encodings>
            <style>
              <style-rule element='datalabel'>
                <format attr='color-mode' value='auto' />
                <format attr='font-size' value='8' />
              </style-rule>
              <style-rule element='mark'>
                <format attr='mark-labels-show' value='true' />
                <format attr='mark-labels-cull' value='true' />
                <format attr='size' value='2.3314917087554932' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>[federated.1nl426t13auwkc10rmst40m1iuwe].[Latitude (generated)]</rows>
        <cols>[federated.1nl426t13auwkc10rmst40m1iuwe].[Longitude (generated)]</cols>
      </table>
      <simple-id uuid='{E2666000-1B1A-4B40-83A7-1FCBDE7AA728}' />
    </worksheet>
    <worksheet name='KPI'>
      <repository-location derived-from='https://public.tableau.com/workbooks/Adventure_Works_16636772947660/KPI?rev=' id='65353092' path='/workbooks/Adventure_Works_16636772947660' revision='' />
      <table>
        <view>
          <datasources>
            <datasource caption='FactInternetSales+ (AdventureWorksDW2022)' name='federated.1nl426t13auwkc10rmst40m1iuwe' />
          </datasources>
          <datasource-dependencies datasource='federated.1nl426t13auwkc10rmst40m1iuwe'>
            <column caption='CLEAR FILTERS' datatype='string' name='[Calculation_295830217743659008]' role='dimension' type='nominal'>
              <calculation class='tableau' formula='&apos;Clear All&apos;' />
            </column>
            <column aggregation='Sum' caption='Quarter' datatype='integer' name='[Calculation_447545216406446083]' role='dimension' type='ordinal'>
              <calculation class='tableau' formula='QUARTER([OrderDate])' />
            </column>
            <column caption='MonthName' datatype='string' name='[Calculation_447545216416518153]' role='dimension' type='nominal'>
              <calculation class='tableau' formula='DATENAME(&apos;month&apos;,[OrderDate])' />
            </column>
            <column caption='Profit' datatype='real' name='[Calculation_447545216423890964]' role='measure' type='quantitative'>
              <calculation class='tableau' formula='([SalesAmount])-([TotalProductCost])' />
            </column>
            <column caption='Year' datatype='integer' name='[Calculation_561261115181088768]' role='dimension' type='ordinal'>
              <calculation class='tableau' formula='YEAR([OrderDate])' />
            </column>
            <column caption='Reset Filters' datatype='string' name='[Calculation_778278323468275715]' role='dimension' type='nominal'>
              <calculation class='tableau' formula='&apos;Reset Filters&apos;' />
            </column>
            <column caption='Order Date' datatype='datetime' name='[OrderDate]' role='dimension' type='ordinal' />
            <column caption='Order Quantity' datatype='integer' name='[OrderQuantity]' role='measure' type='quantitative' />
            <column caption='Sales Amount' datatype='real' name='[SalesAmount]' role='measure' type='quantitative' />
            <column caption='Sales Territory Country' datatype='string' name='[SalesTerritoryCountry]' role='dimension' semantic-role='[Country].[ISO3166_2]' type='nominal' />
            <column caption='Total Product Cost' datatype='real' name='[TotalProductCost]' role='measure' type='quantitative' />
            <column-instance column='[OrderQuantity]' derivation='Count' name='[cnt:OrderQuantity:qk]' pivot='key' type='quantitative' />
            <column-instance column='[Calculation_447545216406446083]' derivation='None' name='[none:Calculation_447545216406446083:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Calculation_447545216416518153]' derivation='None' name='[none:Calculation_447545216416518153:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_561261115181088768]' derivation='None' name='[none:Calculation_561261115181088768:ok]' pivot='key' type='ordinal' />
            <column-instance column='[SalesTerritoryCountry]' derivation='None' name='[none:SalesTerritoryCountry:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_447545216423890964]' derivation='Sum' name='[sum:Calculation_447545216423890964:qk]' pivot='key' type='quantitative' />
            <column-instance column='[SalesAmount]' derivation='Sum' name='[sum:SalesAmount:qk]' pivot='key' type='quantitative' />
            <column-instance column='[TotalProductCost]' derivation='Sum' name='[sum:TotalProductCost:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <filter class='categorical' column='[federated.1nl426t13auwkc10rmst40m1iuwe].[:Measure Names]'>
            <groupfilter function='union' user:op='manual'>
              <groupfilter function='member' level='[:Measure Names]' member='&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:SalesAmount:qk]&quot;' />
              <groupfilter function='member' level='[:Measure Names]' member='&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:TotalProductCost:qk]&quot;' />
              <groupfilter function='member' level='[:Measure Names]' member='&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_447545216423890964:qk]&quot;' />
              <groupfilter function='member' level='[:Measure Names]' member='&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[cnt:OrderQuantity:qk]&quot;' />
            </groupfilter>
          </filter>
          <manual-sort column='[federated.1nl426t13auwkc10rmst40m1iuwe].[:Measure Names]' direction='ASC'>
            <dictionary>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:SalesAmount:qk]&quot;</bucket>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:TotalProductCost:qk]&quot;</bucket>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_447545216423890964:qk]&quot;</bucket>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[cnt:OrderQuantity:qk]&quot;</bucket>
            </dictionary>
          </manual-sort>
          <filter class='categorical' column='[federated.1nl426t13auwkc10rmst40m1iuwe].[Action (CLEAR FILTERS)]'>
            <groupfilter function='level-members' level='[Calculation_295830217743659008]' user:ui-action-filter='[Action2_97DBC24A633E4E5B98F58ABF435EE34C]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[federated.1nl426t13auwkc10rmst40m1iuwe].[Action (Reset Filters)]'>
            <groupfilter function='level-members' level='[Calculation_778278323468275715]' user:ui-action-filter='[Action2_97DBC24A633E4E5B98F58ABF435EE34C]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[federated.1nl426t13auwkc10rmst40m1iuwe].[Action (Year)]'>
            <groupfilter function='level-members' level='[Calculation_561261115181088768]' user:ui-action-filter='[Action2_97DBC24A633E4E5B98F58ABF435EE34C]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[federated.1nl426t13auwkc10rmst40m1iuwe].[none:Calculation_447545216406446083:ok]'>
            <groupfilter function='level-members' level='[none:Calculation_447545216406446083:ok]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[federated.1nl426t13auwkc10rmst40m1iuwe].[none:Calculation_447545216416518153:nk]'>
            <groupfilter function='level-members' level='[none:Calculation_447545216416518153:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[federated.1nl426t13auwkc10rmst40m1iuwe].[none:Calculation_561261115181088768:ok]' filter-group='6'>
            <groupfilter function='level-members' level='[none:Calculation_561261115181088768:ok]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[federated.1nl426t13auwkc10rmst40m1iuwe].[none:SalesTerritoryCountry:nk]' filter-group='5'>
            <groupfilter function='union' user:ui-domain='database' user:ui-enumeration='inclusive' user:ui-marker='enumerate'>
              <groupfilter function='member' level='[none:SalesTerritoryCountry:nk]' member='&quot;Australia&quot;' />
              <groupfilter function='member' level='[none:SalesTerritoryCountry:nk]' member='&quot;Canada&quot;' />
              <groupfilter function='member' level='[none:SalesTerritoryCountry:nk]' member='&quot;France&quot;' />
              <groupfilter function='member' level='[none:SalesTerritoryCountry:nk]' member='&quot;Germany&quot;' />
              <groupfilter function='member' level='[none:SalesTerritoryCountry:nk]' member='&quot;United Kingdom&quot;' />
              <groupfilter function='member' level='[none:SalesTerritoryCountry:nk]' member='&quot;United States&quot;' />
            </groupfilter>
          </filter>
          <slices>
            <column>[federated.1nl426t13auwkc10rmst40m1iuwe].[:Measure Names]</column>
            <column>[federated.1nl426t13auwkc10rmst40m1iuwe].[none:Calculation_561261115181088768:ok]</column>
            <column>[federated.1nl426t13auwkc10rmst40m1iuwe].[none:Calculation_447545216406446083:ok]</column>
            <column>[federated.1nl426t13auwkc10rmst40m1iuwe].[none:Calculation_447545216416518153:nk]</column>
            <column>[federated.1nl426t13auwkc10rmst40m1iuwe].[none:SalesTerritoryCountry:nk]</column>
            <column>[federated.1nl426t13auwkc10rmst40m1iuwe].[Action (Reset Filters)]</column>
            <column>[federated.1nl426t13auwkc10rmst40m1iuwe].[Action (CLEAR FILTERS)]</column>
            <column>[federated.1nl426t13auwkc10rmst40m1iuwe].[Action (Year)]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='cell'>
            <format attr='width' field='[federated.1nl426t13auwkc10rmst40m1iuwe].[:Measure Names]' value='273' />
            <format attr='height' value='19' />
            <format attr='border-color' value='#4e79a7' />
            <format attr='border-color' scope='rows' value='#000000' />
            <format attr='border-width' value='0' />
            <format attr='border-style' value='none' />
            <format attr='border-style' scope='cols' value='dashed' />
            <format attr='border-width' scope='rows' value='0' />
            <format attr='border-style' scope='rows' value='none' />
            <format attr='font-weight' value='bold' />
            <format attr='color' value='#666666' />
            <format attr='text-align' value='center' />
            <format attr='font-size' value='10' />
            <format attr='border-width' scope='cols' value='2' />
            <format attr='vertical-align' value='center' />
          </style-rule>
          <style-rule element='header'>
            <format attr='height' field='[federated.1nl426t13auwkc10rmst40m1iuwe].[:Measure Names]' value='28' />
            <format attr='width-header' value='68' />
            <format attr='border-color' value='#000000' />
            <format attr='border-color' scope='cols' value='#4e79a7' />
            <format attr='border-color' data-class='subtotal' scope='cols' value='#000000' />
            <format attr='border-width' value='0' />
            <format attr='border-style' value='none' />
            <format attr='border-style' scope='cols' value='dashed' />
            <format attr='border-width' scope='rows' value='1' />
            <format attr='border-style' scope='rows' value='solid' />
            <format attr='border-width' data-class='subtotal' value='1' />
            <format attr='border-style' data-class='subtotal' scope='cols' value='dashed' />
            <format attr='font-weight' data-class='subtotal' scope='cols' value='bold' />
            <format attr='color' data-class='subtotal' scope='cols' value='#4e79a7' />
            <format attr='border-width' scope='cols' value='2' />
            <format attr='font-size' data-class='subtotal' scope='cols' value='10' />
          </style-rule>
          <style-rule element='label'>
            <format attr='font-weight' value='bold' />
            <format attr='color' value='#9d7660' />
            <format attr='font-size' value='12' />
            <format attr='font-size' scope='rows' value='10' />
            <format attr='text-align' scope='cols' value='center' />
          </style-rule>
          <style-rule element='pane'>
            <format attr='border-color' value='#000000' />
            <format attr='border-width' value='0' />
            <format attr='border-style' value='none' />
            <format attr='border-style' scope='cols' value='dashed' />
            <format attr='border-width' scope='cols' value='2' />
            <format attr='border-color' scope='cols' value='#4e79a7' />
            <format attr='border-width' scope='rows' value='0' />
            <format attr='border-style' scope='rows' value='none' />
            <format attr='border-width' data-class='total' value='1' />
            <format attr='border-width' data-class='subtotal' value='1' />
            <format attr='border-style' data-class='total' scope='rows' value='dashed' />
            <format attr='border-style' data-class='subtotal' scope='cols' value='dashed' />
            <format attr='border-color' data-class='total' scope='rows' value='#4e79a7' />
          </style-rule>
          <style-rule element='worksheet'>
            <format attr='color' value='#000000' />
          </style-rule>
          <style-rule element='table-div'>
            <format attr='line-visibility' scope='rows' value='on' />
            <format attr='line-pattern-only' scope='rows' value='dashed' />
            <format attr='stroke-color' scope='rows' value='#4e79a7' />
            <format attr='stroke-size' scope='rows' value='2' />
            <format attr='line-visibility' scope='cols' value='on' />
            <format attr='line-pattern-only' scope='cols' value='dashed' />
            <format attr='stroke-size' scope='cols' value='2' />
            <format attr='stroke-color' scope='cols' value='#4e79a7' />
          </style-rule>
          <style-rule element='header-div'>
            <format attr='stroke-size' scope='rows' value='1' />
            <format attr='line-visibility' scope='rows' value='on' />
            <format attr='line-pattern-only' scope='rows' value='solid' />
          </style-rule>
          <style-rule element='title'>
            <format attr='background-color' value='#e6e6e6' />
            <format attr='border-color' value='#000000' />
            <format attr='border-style' value='solid' />
            <format attr='border-width' value='1' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
            <mark-sizing mark-sizing-setting='marks-scaling-off' />
            <encodings>
              <text column='[federated.1nl426t13auwkc10rmst40m1iuwe].[Multiple Values]' />
            </encodings>
            <style>
              <style-rule element='mark'>
                <format attr='mark-labels-show' value='true' />
                <format attr='size' value='0.62569057941436768' />
              </style-rule>
              <style-rule element='pane'>
                <format attr='minwidth' value='-1' />
                <format attr='maxwidth' value='-1' />
                <format attr='minheight' value='-1' />
                <format attr='maxheight' value='-1' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows total='true' />
        <cols total='true'>[federated.1nl426t13auwkc10rmst40m1iuwe].[:Measure Names]</cols>
      </table>
      <simple-id uuid='{849BA632-**************-8DC23D66791A}' />
    </worksheet>
    <worksheet name='Sales&amp;Production Cost'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true' fontalignment='1' fontcolor='#9d7660' fontname='Tableau Semibold' fontsize='12' italic='true' underline='true'>Year wise Sales &amp; Production Cost</run>
            <run fontalignment='1'>Æ&#10;</run>
          </formatted-text>
        </title>
      </layout-options>
      <repository-location derived-from='https://public.tableau.com/workbooks/Adventure_Works_16636772947660/SalesbyYear?rev=' id='65300678' path='/workbooks/Adventure_Works_16636772947660' revision='' />
      <table>
        <view>
          <datasources>
            <datasource caption='FactInternetSales+ (AdventureWorksDW2022)' name='federated.1nl426t13auwkc10rmst40m1iuwe' />
          </datasources>
          <datasource-dependencies datasource='federated.1nl426t13auwkc10rmst40m1iuwe'>
            <column caption='Year' datatype='integer' name='[Calculation_561261115181088768]' role='dimension' type='ordinal'>
              <calculation class='tableau' formula='YEAR([OrderDate])' />
            </column>
            <column caption='Order Date' datatype='datetime' name='[OrderDate]' role='dimension' type='ordinal' />
            <column caption='Sales Amount' datatype='real' name='[SalesAmount]' role='measure' type='quantitative' />
            <column caption='Total Product Cost' datatype='real' name='[TotalProductCost]' role='measure' type='quantitative' />
            <column-instance column='[Calculation_561261115181088768]' derivation='None' name='[none:Calculation_561261115181088768:ok]' pivot='key' type='ordinal' />
            <column-instance column='[SalesAmount]' derivation='Sum' name='[sum:SalesAmount:qk]' pivot='key' type='quantitative' />
            <column-instance column='[TotalProductCost]' derivation='Sum' name='[sum:TotalProductCost:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <filter class='categorical' column='[federated.1nl426t13auwkc10rmst40m1iuwe].[none:Calculation_561261115181088768:ok]' filter-group='6'>
            <groupfilter function='level-members' level='[none:Calculation_561261115181088768:ok]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <slices>
            <column>[federated.1nl426t13auwkc10rmst40m1iuwe].[none:Calculation_561261115181088768:ok]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='axis'>
            <encoding attr='space' class='0' field='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:TotalProductCost:qk]' field-type='quantitative' fold='true' scope='rows' synchronized='true' type='space' />
            <format attr='display' class='0' field='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:TotalProductCost:qk]' scope='rows' value='false' />
            <format attr='tick-color' field='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:SalesAmount:qk]' value='#000000' />
          </style-rule>
          <style-rule element='cell'>
            <format attr='text-format' field='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:SalesAmount:qk]' value='c&quot;$&quot;#,##0,,.00M;(&quot;$&quot;#,##0,,.00M)' />
            <format attr='text-format' field='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:TotalProductCost:qk]' value='c&quot;$&quot;#,##0,,.00M;(&quot;$&quot;#,##0,,.00M)' />
            <format attr='color' field='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:SalesAmount:qk]' value='#000000' />
            <format attr='font-weight' field='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:SalesAmount:qk]' value='normal' />
            <format attr='font-family' field='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:SalesAmount:qk]' value='Tableau Semibold' />
            <format attr='border-style' value='solid' />
            <format attr='border-width' value='4' />
          </style-rule>
          <style-rule element='header'>
            <format attr='border-width' data-class='total' value='0' />
            <format attr='border-style' data-class='total' value='none' />
            <format attr='background-color' field='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:SalesAmount:qk]' value='#ffffff' />
            <format attr='background-color' value='#d4d4d4' />
            <format attr='band-color' scope='cols' value='#00000000' />
            <format attr='band-color' scope='rows' value='#00000000' />
          </style-rule>
          <style-rule element='label'>
            <format attr='text-orientation' field='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:SalesAmount:qk]' value='0' />
            <format attr='font-family' value='Tableau Semibold' />
            <format attr='font-style' value='italic' />
            <format attr='color' value='#79706e' />
          </style-rule>
          <style-rule element='pane'>
            <format attr='border-color' value='#000000' />
            <format attr='border-width' value='0' />
            <format attr='border-style' value='none' />
            <format attr='background-color' value='#00000000' />
            <format attr='background-color' data-class='subtotal' value='#00000000' />
            <format attr='background-color' data-class='subtotal' scope='rows' value='#00000000' />
            <format attr='background-color' data-class='total' scope='rows' value='#00000000' />
            <format attr='band-color' scope='rows' value='#00000000' />
            <format attr='background-color' data-class='subtotal' scope='cols' value='#00000000' />
            <format attr='background-color' data-class='total' scope='cols' value='#00000000' />
            <format attr='band-color' scope='cols' value='#00000000' />
            <format attr='border-style' data-class='subtotal' value='solid' />
            <format attr='border-width' data-class='subtotal' value='2' />
            <format attr='border-style' scope='rows' value='solid' />
            <format attr='border-width' scope='rows' value='1' />
          </style-rule>
          <style-rule element='table'>
            <format attr='band-size' scope='cols' value='1' />
            <format attr='band-size' scope='rows' value='1' />
            <format attr='background-color' value='#faf5f0' />
          </style-rule>
          <style-rule element='worksheet'>
            <format attr='display-field-labels' scope='cols' value='false' />
          </style-rule>
          <style-rule element='gridline'>
            <format attr='stroke-size' scope='rows' value='0' />
            <format attr='line-visibility' scope='rows' value='off' />
          </style-rule>
          <style-rule element='zeroline'>
            <format attr='stroke-size' value='1' />
            <format attr='line-visibility' value='on' />
            <format attr='line-pattern-only' value='dotted' />
          </style-rule>
          <style-rule element='table-div'>
            <format attr='stroke-size' scope='cols' value='0' />
            <format attr='line-visibility' scope='cols' value='off' />
            <format attr='stroke-size' scope='rows' value='2' />
            <format attr='line-visibility' scope='rows' value='on' />
            <format attr='line-pattern-only' scope='rows' value='solid' />
            <format attr='div-level' scope='cols' value='1' />
          </style-rule>
          <style-rule element='title'>
            <format attr='background-color' value='#e6e6e6' />
            <format attr='border-width' value='1' />
            <format attr='border-style' value='solid' />
            <format attr='border-color' value='#000000' />
          </style-rule>
          <style-rule element='axis-title'>
            <format attr='font-weight' field='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:SalesAmount:qk]' value='bold' />
            <format attr='color' field='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:SalesAmount:qk]' value='#e15759' />
          </style-rule>
          <style-rule element='quick-filter'>
            <format attr='border-style' value='solid' />
            <_.fcp.IndividualControlFormatting.true...format attr='border-width' field='[federated.1nl426t13auwkc10rmst40m1iuwe].[none:Calculation_561261115181088768:ok]' value='2' />
            <format attr='border-width' value='3' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
            <encodings>
              <color column='[federated.1nl426t13auwkc10rmst40m1iuwe].[:Measure Names]' />
            </encodings>
            <style>
              <style-rule element='mark'>
                <format attr='mark-labels-show' value='true' />
                <format attr='mark-labels-cull' value='true' />
              </style-rule>
            </style>
          </pane>
          <pane id='1' selection-relaxation-option='selection-relaxation-allow' y-axis-name='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:SalesAmount:qk]'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Bar' />
            <encodings>
              <color column='[federated.1nl426t13auwkc10rmst40m1iuwe].[:Measure Names]' />
              <text column='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:SalesAmount:qk]' />
            </encodings>
            <style>
              <style-rule element='mark'>
                <format attr='mark-labels-show' value='true' />
                <format attr='mark-labels-cull' value='true' />
                <format attr='has-stroke' value='true' />
                <format attr='stroke-color' value='#000000' />
              </style-rule>
            </style>
          </pane>
          <pane id='2' selection-relaxation-option='selection-relaxation-allow' y-axis-name='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:TotalProductCost:qk]'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Area' />
            <encodings>
              <color column='[federated.1nl426t13auwkc10rmst40m1iuwe].[:Measure Names]' />
              <text column='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:TotalProductCost:qk]' />
            </encodings>
            <style>
              <style-rule element='mark'>
                <format attr='mark-labels-show' value='true' />
                <format attr='mark-labels-cull' value='true' />
                <format attr='has-stroke' value='true' />
                <format attr='stroke-color' value='#000000' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>([federated.1nl426t13auwkc10rmst40m1iuwe].[sum:SalesAmount:qk] + [federated.1nl426t13auwkc10rmst40m1iuwe].[sum:TotalProductCost:qk])</rows>
        <cols>[federated.1nl426t13auwkc10rmst40m1iuwe].[none:Calculation_561261115181088768:ok]</cols>
      </table>
      <simple-id uuid='{9EB60106-01A0-40E7-8023-238A77A03AAD}' />
    </worksheet>
  </worksheets>
  <dashboards>
    <dashboard _.fcp.AccessibleZoneTabOrder.true...enable-sort-zone-taborder='true' name='AdventureWorks'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true' fontalignment='1' fontcolor='#9d7660' fontname='Tableau Medium' fontsize='20' italic='true' underline='true'>Adventure Works Sales Dashboard</run>
          </formatted-text>
        </title>
      </layout-options>
      <style />
      <size sizing-mode='automatic' />
      <zones>
        <zone h='100000' id='4' type-v2='layout-basic' w='100000' x='0' y='0'>
          <zone h='98120' id='115' param='vert' type-v2='layout-flow' w='99034' x='483' y='940'>
            <zone fixed-size='63' h='8343' id='116' is-fixed='true' type-v2='title' w='99034' x='483' y='940'>
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='solid' />
                <format attr='border-width' value='1' />
                <format attr='margin' value='4' />
              </zone-style>
            </zone>
            <zone h='89777' id='106' param='horz' type-v2='layout-flow' w='99034' x='483' y='9283'>
              <zone h='89777' id='104' type-v2='layout-basic' w='99034' x='483' y='9283'>
                <zone h='80758' id='43' param='horz' type-v2='layout-flow' w='99034' x='483' y='18302'>
                  <zone h='80758' id='41' type-v2='layout-basic' w='99034' x='483' y='18302'>
                    <zone h='80758' id='29' param='horz' type-v2='layout-flow' w='99034' x='483' y='18302'>
                      <zone h='80758' id='27' type-v2='layout-basic' w='99034' x='483' y='18302'>
                        <zone h='80758' id='18' param='horz' type-v2='layout-flow' w='99034' x='483' y='18302'>
                          <zone h='80758' id='16' type-v2='layout-basic' w='99034' x='483' y='18302'>
                            <zone h='80758' id='11' param='horz' type-v2='layout-flow' w='99034' x='483' y='18302'>
                              <zone h='80758' id='9' type-v2='layout-basic' w='99034' x='483' y='18302'>
                                <zone h='80758' id='26' name='Sales&amp;Production Cost' w='49517' x='483' y='18302'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='solid' />
                                    <format attr='border-width' value='2' />
                                    <format attr='margin' value='4' />
                                  </zone-style>
                                </zone>
                                <zone h='80758' id='40' name=' Country wise Sales' w='49517' x='50000' y='18302'>
                                  <zone-style>
                                    <format attr='border-color' value='#000000' />
                                    <format attr='border-style' value='solid' />
                                    <format attr='border-width' value='2' />
                                    <format attr='margin' value='4' />
                                    <format attr='background-color' value='#f6eee3' />
                                  </zone-style>
                                </zone>
                              </zone>
                            </zone>
                          </zone>
                        </zone>
                      </zone>
                    </zone>
                  </zone>
                </zone>
                <zone h='9019' id='45' name='KPI' show-title='false' w='99034' x='483' y='9283'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                  </zone-style>
                </zone>
              </zone>
            </zone>
          </zone>
          <zone-style>
            <format attr='border-color' value='#000000' />
            <format attr='border-style' value='none' />
            <format attr='border-width' value='0' />
            <format attr='margin' value='8' />
          </zone-style>
        </zone>
      </zones>
      <devicelayouts>
        <devicelayout auto-generated='true' name='Phone'>
          <layout-options>
            <title>
              <formatted-text>
                <run bold='true' fontalignment='1' fontcolor='#9d7660' fontname='Tableau Medium' fontsize='20' italic='true' underline='true'>Adventure Works Sales Dashboard</run>
              </formatted-text>
            </title>
          </layout-options>
          <size maxheight='750' minheight='750' sizing-mode='vscroll' />
          <zones>
            <zone h='100000' id='154' type-v2='layout-basic' w='100000' x='0' y='0'>
              <zone h='98120' id='153' param='vert' type-v2='layout-flow' w='99034' x='483' y='940'>
                <zone fixed-size='63' h='8343' id='116' type-v2='title' w='99034' x='483' y='940'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='solid' />
                    <format attr='border-width' value='1' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone fixed-size='64' h='9019' id='45' is-fixed='true' name='KPI' show-title='false' w='99034' x='483' y='9283'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone fixed-size='280' h='80758' id='26' is-fixed='true' name='Sales&amp;Production Cost' w='49517' x='483' y='18302'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='solid' />
                    <format attr='border-width' value='2' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone fixed-size='280' h='80758' id='40' is-fixed='true' name=' Country wise Sales' w='49517' x='50000' y='18302'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='solid' />
                    <format attr='border-width' value='2' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                    <format attr='background-color' value='#f6eee3' />
                  </zone-style>
                </zone>
              </zone>
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='none' />
                <format attr='border-width' value='0' />
                <format attr='margin' value='8' />
              </zone-style>
            </zone>
          </zones>
        </devicelayout>
      </devicelayouts>
      <simple-id uuid='{5CA7A8AA-0B19-4C3D-BDD1-8F8F88AD34F6}' />
    </dashboard>
  </dashboards>
  <windows saved-dpi-scale-factor='1.25' source-height='37'>
    <window class='worksheet' name='Sales&amp;Production Cost'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='92'>
            <card type='rows' />
          </strip>
          <strip size='2147483647'>
            <card type='title' />
          </strip>
        </edge>
        <edge name='right'>
          <strip size='160'>
            <card param='[federated.1nl426t13auwkc10rmst40m1iuwe].[none:Calculation_561261115181088768:ok]' type='filter' />
            <card pane-specification-id='1' param='[federated.1nl426t13auwkc10rmst40m1iuwe].[:Measure Names]' type='color' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <zoom type='entire-view' />
        <highlight>
          <color-one-way>
            <field>[federated.1nl426t13auwkc10rmst40m1iuwe].[:Measure Names]</field>
            <field>[federated.1nl426t13auwkc10rmst40m1iuwe].[none:Calculation_561261115181088768:ok]</field>
            <field>[federated.1nl426t13auwkc10rmst40m1iuwe].[none:CalendarYear:qk]</field>
          </color-one-way>
        </highlight>
        <default-map-tool-selection tool='2' />
      </viewpoint>
      <simple-id uuid='{96EE2E51-EC5A-43A2-87B9-13F1FEF4C2DD}' />
    </window>
    <window class='worksheet' name=' Country wise Sales'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
        </edge>
        <edge name='right'>
          <strip size='160'>
            <card param='[federated.1nl426t13auwkc10rmst40m1iuwe].[none:SalesTerritoryCountry:nk]' type='filter' />
            <card pane-specification-id='0' param='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:SalesAmount:qk]' type='size' />
          </strip>
          <strip size='160'>
            <card type='title' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <highlight>
          <color-one-way>
            <field>[federated.1nl426t13auwkc10rmst40m1iuwe].[none:SalesTerritoryCountry:nk]</field>
            <field>[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:SalesAmount:qk]</field>
          </color-one-way>
        </highlight>
        <default-map-tool-selection tool='2' />
      </viewpoint>
      <simple-id uuid='{1D6E35D8-8807-470E-85C1-77480FCC3E6F}' />
    </window>
    <window class='worksheet' name='KPI'>
      <cards>
        <edge name='left'>
          <strip size='164'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
            <card type='measures' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='2147483647'>
            <card type='title' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <zoom type='fit-width' />
        <highlight>
          <color-one-way>
            <field>[federated.1nl426t13auwkc10rmst40m1iuwe].[none:Calculation_447545216406446083:ok]</field>
            <field>[federated.1nl426t13auwkc10rmst40m1iuwe].[none:Calculation_447545216416518153:nk]</field>
            <field>[federated.1nl426t13auwkc10rmst40m1iuwe].[none:Calculation_561261115181088768:ok]</field>
            <field>[federated.1nl426t13auwkc10rmst40m1iuwe].[none:SalesTerritoryCountry:nk]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{CC1DE685-BF77-4B0A-AF7C-E1EE27199076}' />
    </window>
    <window class='dashboard' maximized='true' name='AdventureWorks'>
      <viewpoints>
        <viewpoint name=' Country wise Sales'>
          <floating-toolbar-visibility value='1' />
          <default-map-tool-selection tool='2' />
        </viewpoint>
        <viewpoint name='KPI'>
          <zoom type='fit-width' />
        </viewpoint>
        <viewpoint name='Sales&amp;Production Cost'>
          <zoom type='entire-view' />
        </viewpoint>
      </viewpoints>
      <active id='116' />
      <simple-id uuid='{DF6D76A4-4DF0-4E63-AF57-74BF98439136}' />
    </window>
  </windows>
  <thumbnails>
    <thumbnail height='192' name=' Country wise Sales' width='192'>
      iVBORw0KGgoAAAANSUhEUgAAAMAAAADACAYAAABS3GwHAAAACXBIWXMAABJ0AAASdAHeZh94
      AAAgAElEQVR4nO19W48kyXXeF5fMrHt3T8/OziyXu1pyVxpREiFBpCzDN9gyIBkGBAjgsw0D
      hl8MP/s/+NX+A/aD/WIDlkRAgmHDEiVYNg1IolaiTFK75O6KO7Nz6e6qrmtmRhw/xCUjs7Kq
      q7qru2u28wN6pi5ZkZmR55w492Anz/6aBCM0aLAv0JpAROCcg7HrPZcUjBCJ6z1JgwabgIig
      NIEzgHFAcAK7Zg6Q1zp6gzsLou2Il4hAABhj1y71QzQM0GDnICIQAcDmTMAYA4hulPiBhgEa
      XAOMJDf/44KVwEt+4NrVnTo0DNBgJygRMgBt/SqOCQRnpWPtx/43uHnaB9AwQIMdwhE0Z+ZP
      WS4QnBVE7w8GwACO25H8Dg0DNNgZGDMuTKfIk32pK152bpeJ2yR8fy23fQENXn046e7IOVca
      ACAFg+DM6vfmjxtbd2/QMECDnYExBs4ZiIz64widMcsc9j3nbC+kP9AwQIMrwrk8nVDnngnI
      LwmaCntgXwjfoWGABrsBFaqQFByMMWhtXKCh2kP7pP+gMYIbXBFGopNfAVwEmFckfREXwI0H
      u9ahWQEaXBks8PpoAjQRuPX7a6qoPntE/EDDAA22ABH5TM0qzDoQGLwIglyV4zY5j1pxnl2j
      YYAGW0NTGM0tiJSzckqDU3so+P7isQn6BtWkhgEaLGGVpGdWvHt9HpbArd5fldcMNhgWHLvK
      C1RI/SKd4iY8Ro0R3KAWbEWkdp1KU0eujpD5BZmh7hj3+qbQMECDJRipDjDQkjfHoVBz2NJn
      5eNWf7dqzJvEjatAJnCyX77gBhW4CG740ZpnpivP1L3WQYDMHbdvuNEVgKyBA+DCJbHBLYIt
      qzPrcvarn2gyz5dbW0ETeXtg39ygN8oAmghKmaofYgyC719o/K6Bqn56oFbtWfeUqs/QeXu8
      wK9Egvfpmd+oCkTa/k/loInWtLSMNrgZVKdc67IqEzJI6Ppc96xCNyi32aDA7VV9rcO1rgDh
      JGlNQcC8yB33OeQwkyXFfk3Q5x2ht4cqNbnOH+/dmC6XwX1nj683fpl3p7pU6H0jfuCaGMDo
      +gTShqhDKc9QpMO6+eDcMIHWBM1Xex62O3858LKPk78PCIlfe+PXPANbp14Irkpxy0Uq/U13
      eLgMdsoATuLniqC1NhNERhKEwY2QGIsl1qpFmsCvuAoQAUppaGZWFE0AtN6rPPR9ge/gwMoC
      wz0zY8S6Z4elYNce2rVbYWcMoDUh17o0Q64guupBMCoPlboGuBzyXdBnUXfqzmyijKEeu69L
      8m3AlS3WRXLDN67eFyim9lWfwSszgFN30txYuGHY2/1fN0kqsA8YEbSyueRsF3Z5sWQzZoww
      bvVQpQlKawAMkeA+a/GuwgkFzgqdP/TesKBzQ5jKQGuCZK8SrsQALn/D1YACgb/YvncTF/7G
      EWXIJAAQS7GTSeWMgXjhzuOMgUvzWgqTq65voQnTPiH07jiiFqwcrGIAWCAgwun6PBA/sCMV
      qG7pLHkWUNSL5qowhsMpjCMBrQi0A9WEWd3fueuW/NScgb/yi/floW0NI2E5IOl0feBuqIhX
      0jccUYeDuD4vjvg0YP7IrBRVqQ+YEjqizYzfbWMFRNv/5vOOsIZXU9n371aETYj/NmM3uzpv
      7QqwLlqnNRn93brHwskDivTYOm9BHSJbP5rmCowx20HMtcdm1h1XGM9Ka0hhWG6Th+SYLpb8
      Tki0TVBVSy+9GFp9ty6aXAcdaANXgfNc7eJxLjGA96GjLBUcnCRfeXFbnFxwc5YsVwXjqIB5
      NBm7AEUEUmtCShoMpuOYEOsXMWFXlwYGbg7DxlQMmxNxCSwwkNcQZNg20b2/ChOEGsZVmWmJ
      AbQtSiBeBEIYq5fqV0Wulxmpeo40V9b1ZuSWhotAMihNEGv2NmCMmWN5GOlsVgFNFDgI3Gfl
      qO+mIG3m1wmwuvl1KlfgXAJ2kAy5i1WgxADOqwMApAhRJDynKUVg1rNScpVtiKVldwvoIAQf
      XutFXYeVMrEJJ32k4EVbPtwNI6+Kdc/NRX03bmkOQFFhSK76XUgvPo6AK8YQmAl2MnY11bak
      P5Qmx7oKlU1RIBhff6414nVidwV2uXoQjGEd3nedmqPDdh0AlNbIlMIiU57R7yKcpOcVj5t7
      5dJWLh6HQVzguHAqNbORZhdZvirM9V9dgPkVwEh/7ZdHzoE814bTqOjsZRhCXUmi7wq+BTcR
      MqURCY5cUyV5q0Do/XC68K4DYc4zEhrv+wTu9h9CmLVpyhGNGuPcowYX2QYupuKOXXnPQeTY
      Lehh+5RtYbJbrv78PAMoTcgdkROBE/ORUzNBRuo6cMZK0dzbgA5UNhPhVf67OgYNI5pKEzQp
      RBBLKt1l0ySICCq3Kpe4XCJYKDF9MHHHjFQ3WhGcLHtqCiN3DSMQSscCyyuyYy73HQGlPQO2
      BWcMTF49a8AzQPXGtCZIwZFrteJmblv+Gxj36fLnq64uVImIAg9UcIzkzLf32xZSbu6iLV2X
      TSnR2ql2RZeFVR65bcevq+OtIgxgmqsovC7OO1j9vXeWYHkVqFN3CsfK1YxYP/QVnBsFAwQf
      Oim5Sg/ct4X9KrxY59DVmoAaM6e2IVSoQ1/yIbhKuer4Lsbi1jXTcpBKacYXndONqTRtVYHn
      U6Otj9O9X8UE/rvKdYXpFQyBt4mtXiE3dcmWp+tyTOAZoI7YV0n5qsQM1Y2qqvQqwhlqjMiq
      BMxmTBq7QXCORa68Rwwwy/ll9H4iQp4b28vp3lTxehWBQB0wgznEXYMIVqxSUbpV6nNtPSYo
      CLFqBIfXVLxeo0puIXncSsLdjLLV97kN/DiXXE1kpjQ4Mw9Qk/GtEwznFp19V8cAHMG7iXWf
      OeyKGS4VqLkClCqIMoSJXJvXOqAOt79ttEVCn4tohrZU3Xyx4Do4AM65+wLzRQoAaLcSuJig
      C1SSNTTdb7NcW73bpoULDhCVGEETXbiiOuJ3amS4JZIGW1LZQt2fMfjU99AuMAxS/GYTOK+S
      y2S9DDiRcW9mSkPygnTNksms+2q52294g0DhmgyvY1dWgu9BXxlwl6xQHauO+AEgU2au6qAJ
      1sWqL5SO2nquFrlaexxQDiAJy4BO8rk/rQlZrpHbtHJfZ10ZJ9emWAlw82p+p4KKvVVw5627
      b+eVCVef6u9KWkNFBVqncq++Huaj0Je1SUteIKJyWrPxrJiBVzEABZNGwd8ucN2GdnjdoRp3
      0VnrYgjh7KS5Riw4+Dqdmwodv85jVbLJGAsCkLZ0FE6VKROty6EqV2ADwhmyMIKKVyS9CSox
      my4eql3kVwUnEKuqFq8Qd7hDJENBO6xGT6+u7Nuu9EoTlCryw7ZFKRJczQUXnCFXzm+72h5g
      uD69Pzxt1ed7VfYoPbQrjBOqf26cVGlIWu1N4pwhlgKp9UKtGs+oCYU0d++BMqGFhqbSy3Oz
      ZGADJo0BZfelthFGsqsEZ8yvwLmy8Q1eMK1hKgatjXrltkgCsMSY7v+wYN4wLC9Vmm3j29dW
      Zb8oLWYVVtYDSMG9PQCsJpBdSvx12MOYkseqlSPXBMZWe1+4dbdWVaq68ULCD2nZmCHFc3Kr
      Qt01Vq+g+pkmsg86MIJZeaXQxmgp+v9rAufGLil0+PLx1RiLA7fhYc7cbjLbPWc/HwBWW6nr
      UWIAL3msNyPbQD8NL2RXBmrdg94F6lSNXaFOijvJrDITUY+lKM0REVnvkUCWq5KQKfbUKvzw
      dfNRDeJVg5UMxsj2DORjAUDEufdsuXtwEvyiZ6Ar1+fu2XzGSsc5j1Xp2MAGcCucCLxom9IU
      Y8x7rC+T7FhOhrN/jMgHiFbhuvXzarBkF8x1EysVYPRt59lRgZ6S5RqRDZS5tPKiBiK4zoob
      EljtmTFemHpXpg5e+3mE1cvBwDiDgEt7UfZ6NfiauuxQPQn4qYQw79+rNmtcrrnS4Jz71axq
      m62CmTvz/2X7jkpgWTJuOlRIpO6XqwhVKY1MEdpJ5M+hNYGz1Wcr3Gfrx94nGE8NA9fGEHXp
      JYLbPXPtcm9KQwEQlYzqcnXWhqsg2fgBAo+R99wRFC0dblYVTbY7nw6YZb06Eero1cfhVipP
      T4GtUlXrQyZ3bt1wPOfh2QTV+oZtIC+jw1eX40JSl5ev4nuGRZrjxekYUSS9tNFa495hF/1u
      a+XYxef7bQe4h+5cxkwya+SZ3CDJeYlwBGfl4p81WKX6+O/t/5wxLzVduSpjgLDevFBKEgip
      okI1CX63/lrIP+uwKMUxtrseBiP1L7qv4nrdfW33kBmzqywBl3EEySrHr80Tsf+XuoOF+ufK
      aAShlcT4wsNjaNJwvE1Evnj9wnOzy+l4N4lIcC/lAatnS24TCovj3NINV5pvH6JRiaorXz3K
      AqHwsAAAWeKEzZZ0DcFyG9wDUKrqYyg6ZaAyx05X10urVKim1bc/ZPY6SRM0L2+SwezqVKWp
      dXsS1CFkvkvZAAw2s9M+pCoTLXkKUEz8pmqXV2FAMOnj/m4D4l9vV4RqVvjZZSXHrkEwRBz6
      o901hR0q3OfO5w44fb1e9fH3Z984j0noXSGgpO+jxAjw53ERUyfF3YrkDWQUqksxnwSyopxz
      tlLImd8bona13D6DwA7lVD7BQyYw4IEYDivWNoFrwHu5XCBWuM2IzLLlTk4wS0txa+uk/NWx
      jqFKS37NCuSkkDn2dpjBeTNWBw3N/1Xvh2sVUz3OvY78xtMm1u4CVXUBuVAo+NiBKlIgGKi0
      gjvVR8MY7+66HJwXJzR+VzGCZyynU8EGyTbQ551K5a5pGxh163LPXIYinrFiq3s3tNfr7GSu
      IsSrYJNr3+RcVQIDru76dCrKReeX3PYgrS6ZbhzGlu5Tk02EC44JiY8xu+s6GHKtfb6NO6Su
      12n1HM4bxInZRsXlGambH2fLuGdeBKnKqlbdPbposXvvzqF00Rh51RjuWrZ1ehiD+ZIqkDth
      KDUYFWF3ZpfNIj2Weamh1rjntsGuParVidgmQ9Utpf7h83pJ68dmzHeb22byXc1y9bPQsyY5
      BwjIKYjKMuPXl4IhkgIqz5HnZdchwnsg+ITGXDkdv37ONZkuHMrmC7k5c4l+wHoCdpHg+nmq
      3D9Qijd45sF6L9QqMKxP1V4FGVrgDkrr0oS65VkwI+Wcvrprwr0OlM219RCMIbL9gxZZDsG5
      8enr+oCg5Nz79a8CJ/mcYeiZADZLVxVEEkccShE06SK7U3BIwX1aQNneKFQhH21Fofdr57q0
      906M42SmcTZReHSUIGI5gCC6bCd0ebVaXwG3RJRkXOMAllrbXC4t2q0C7iI3G0dyxkyiFAI9
      v6KP+tUBhjtDVaPu9T5g2+WQAZ74ASASAmAmeBUe40pBncfnstcGOF0aAJVlXuFNMU8lJDbn
      gRHcnH+hGYgUlFJgdsUozlM8lzCyS8H3hd5NyBThv377U/z2//4QShOOBy38q1//OfzEsYST
      y6SLXJ7q899qBQzu1SfweZWrvMpsrArBNkKwmYKuS+E6cCcdvMSvIWLnWHDqQdmlt3/EfxlU
      0xQYs+WSlthjKZBEAnEkkEhx6ZJJB02FyuADQSg8ROF1APDnc/lD3K7GcRRBE2E4moAzhjzL
      oG2EedXluWdWvZ6MIvzWH33oVb6Xozn+/X//PjSThYcGxQoTPvNt5yKsHNPatrBRGlRRN7eh
      K6NG8VJx0EWQhCD3Hyh1TQ6lP1ElWzS4gV2nRYTnvAzC6wZsFJaVdzIX1mjNbGUXlX5vaiQ0
      GcITFWPzqp0IGGMQnrBtZLiSwclRBNWqKcpmjOLYQa+DvK3x2YtTxFJCE+Fg0AsOMiNLzk2f
      pBUr+Ml5upRS8NnpBLNUoy0LaexSsS/rtQEKd6zrjVakcRert1utLmPcbupGNakQbFn1cRIk
      5PQ6L8uu3aJuKd0VUwnGvBuxCg5CEoml73wHa2a8O9cNxoBEmpQuIlMo42yCdaWLxe+NQfzG
      g3vIlYZSGsPRGINBz39vHAGESAhkSvlVPcS9flRqcwIAD+910YoYSJefe/XpKKUBurhVpUNV
      8wCqtGTLTy+Z578pSo93KUNP13t53ORdh9pDVM00XF6yXYj/Iiav6vVL31fUDSP5Vallo6br
      jys41SaM2gqrFm0rYAjGJx5H0uf0s+DLdXEKrjP8xt/6srdt7h+08U//4U+BUb5Wx3fCUwUV
      YRdB8uUqQxcXyZQOyjq3G3dbsOdPfkRLCWm02hFVXT53of+v1lXrXW7Vz6vnZ8yoOFW9/iIo
      rf1ONw6x4NcuhRyca5RgVCOlilSGTWFKI43X6uRshHuHg2J8++cYwYAZ49mBCYxThtE0w+uH
      CSSy0vjG01IwVsl4h81xArydsg5K6aIVJ9GSq9qNBxiV5jqeg3TuI3eprKoPL71eXhVKS2PA
      FOICH3p5jLIkrqKq15dSfJfOzyDFdsQPFB6f0PtwU7a9SwtmjEG6VUBcwrjkDFJyZLlGu93C
      6fDc2wPVeeWcgXEJHvdLnx8mwKH/qI35fI4oiiCEgFYpkE3MNVfvASiaq2lly0KX4yO+RgDr
      9Xuj/4evd58LZuIAMBM9mc6Q5QqdVoI4NmnL4emIyOSX2PeC21Yh5CLIRaTSGJmbkU/VCDXn
      AjzLVWyQdcuhKTXkS4GXi0DBqucYS1r/+nWC7Py5diuhqnHZhy04h+ImAVFrjTxXkJFcElza
      9gr6gz/4AwwGAwghMBgM8NFHH+Hx48f4wQ9+gPfeew+/93u/h69//et4/vw5fvEXvnrh/bj5
      S5UG1za2Yr/3tiXM94CtT14xnlPZNJluhdgy0HUReK4UzsdTjMYT4z3odzGdz6FJ++U3z3PP
      yaFXIvTZ+p6i9r2qsR9WXXe5yKJsdF/EQ6E9QuSKvy8zFeXrvIl8ojDVQAq20/NKwRFZ5o2k
      qXuq2lSM2Q6AUuIrX/kKnj17hm9961t488038Rd/8Rf44IMP8Md//Md4/Pgxnj17hu9+97tI
      03TDeyt0+kWmMA8aEhvvW2BnoRC01bt3nklNRrXLlN6p40WSJvT7HcigojiSEp/8+BmiSKLV
      ijGfp3j0+rHZ0IJQtNVAWRrnikw0kfSSO9Wh6mUw2yMV368iXifJ3Mq0CpnSJsMPHJpMw1zH
      nBd5UpxOyxhDIsW17SAZZmNeJ5sRjItUKRPZd1Ffcw2WCTjD22+/je9///s4OjrCO++8gwcP
      HkBKiU6ng7feegsfffQRHj16BCklpJRgOi+dI0QRwFt3YbYYHoVdEvaWouB1eA4CAFtjEV+x
      Lbq/3pPPPibJy3voutzqNM0QxxE4Zzg9O8e9o4E/zh9LRW65a90R6v1V/dwZSU6fk5z7/p6s
      5gFpTRiOxqZzAGeGCSKJbrddGnfpxux4Lqmrzt1ZRZobKRWmRFwHdl1DveocuW0Xkivt4zhh
      rTERwIUA59LnekkeGFX+OTNPjUrlIJ1DMOYbJyj/zOrvy30uOTe9Uwmmsx4qNcwIDPWa+wnt
      y0ia4ORVIQuVY9nTkiSxPTnQ63YwGk8x6HX8DeqKe6qusRIRkKYZIikghPB1B47D06DwvgiA
      UMmGyPIcrx0f+eOGo/FKD1F4XmDZs7AOnDEo0LUSP3Az6hULvCZSGKPY2Hrl40grgDQ4bBeL
      SICh6NIAt1LAPBdlPUAasBmqrEQ6VVoKH48iKmpwsZyg6KLM1fsoXu/O8+jAgcLn7/7qIKVA
      luY4HY69q01RETDitubVpVO7AJJSCvPFAos0xcnZEFnmlk+2Uk1yeemMMSil0Gm1EKLdTjCb
      zU2uR821hg9ZVvJj1oEB12703iSc2ufiDIwxbxc4EBWE6FyO5XrdsMKN2Rwo7nuYVr18dc+z
      OBchzZRPgqt+F57XvXRC1r03tdXbOzlWYat9gvu9DqbzBU6HI5MmICW6HUOcDMB8vsBskYI0
      QUqBXrdjJ02g3+2g027hxckQnU4LeZZDSol2O/HjExmJZMYWYABmszkGg56fAM4ZWnGE4WiM
      frcDreozNZ30ksI09zLVWqtnjcjsdSVxMxL6pmHSLUz2aCSFVVuXHRVO/17ncuTMdpez4sep
      QOXj3e/LTg1VUcOqUf/S9ZDTFGwzLgafrr0r/zR7+fQjEms6M6wCEWF4PsF0tsDhoIvpbI5W
      kqBndfP5IsV4MgNnDL1eG3EUea/HYpGilcRYZBnOxzN0Oi1wxrBYpAAZ5hmeT9ButYy+32mZ
      m2YmS5OIMBpPkOcK/V63JPIFL3rZA0AnkZinCpwzJNFqnfEm9PLbhPPGONVTo+yPd4Toiver
      ZZPLwcdivtJcIc8VoqiQp86YrSbNhQgZwKk2DpLbFG+7MghRuFIXmbKBsct15C5dw2UZADA3
      dzYa43R4ji++8cC729bVjobwrq0sAwNDp51YtUdjMp1h0O/687jtdKRlAFfg/eLlGY7vHRq9
      UGtIIXwwhnOGVmTaD24bFf48gogwW2RIsxzj8RTC5x8VxzAGH48gMpLWu7trAo6AkeiLRYo4
      jjDo97yN59K9qbLShLp8HQO4uhMiY4854mfMFPaHnfRiKa6008xWKlCIcNn60ltv4Gw0BmfM
      E+0mCW0MsGnG5ctIswxpnuPl6RCtJEa73QJYkR/jXZYEdLttnJ2NIARHmuW4f3zox3H67r7u
      13XTYIwhiSUmszmO7x0sxXEcETo9e5UQq64KSpvcHeOxO4cUwifwtZIYQkqTp0/lMYpODuYz
      d34vwJjT94vVx9mNZNUjrenmGUBrjZenI0SRRKdldPjDQQ+j8QRZliOK5EYEt+qYditBu5Xg
      fDzF8HwCIQVacVw6hnMOaIV+p43DfhdaE569PPOT7NKYAfhltGECQ1SDXsdIWOeRc4RsmUBr
      0yxrVUGJYQwGLgqfvzG0gaODPuJI+Dym+XyB0fkE3W67NFad3s8YcHI6QprleHD/yPcV8moX
      4F2f1c4Yl4Uswj/LWOVuOh9PcXx0ACl5yV0phYC0LkRzr8XY21woEeHFyRD9XgePHhz7z10j
      VjeRrtZVKY1Fmnq3rbkW7leh6DJtgz+nMHPHQKRL+Tilx1Oh+bDVoWYSH3w2w19+MsR4luGw
      F+Nn3jrEF+/FAHJoTZin1jGhgCiO0SJgOBybVYeWCddkEACpUuCco9tuQWsNzgXMBi6myx5n
      jumsd2oHhrB01vqqBLQQSmm8ODkDYwwH1lDKld0hhDO0kgiT6QyccbRaMZI4AgXtwTSVPQPO
      U0MAsizH8HwCzozhc3w0KHZCCVA2xkxQ7eXJGTjn6Fv1K4lEqd22IkJkxyon0d3NFcH79W2c
      pFzbWxxnGkW77xiejgj/4X98F3/58ctSaxXBGX7h3Qf4J//gXRwk2qo75A1ZSiIcRRKTyRSD
      ftfq8GaA6XQGWFfsIs1w77APgOHsbITX7h+aajFrBFdVnZ1Egp/9+IfEmS6lQqyCM3qPDvq1
      3/t8dgDjyQxE5G0C9/vqhbvPxtM5tCYkSVybRusI1jGr392RgMlkil6v4yOEYQGM0oRFlvut
      i3wQ7o4SPwDM03ypW51LUVk1Ly/GwL/5z9/B8+Fs5bhvPejjX3/jq+hGypZ7cihtUp4lZzgf
      T9HrdowxPl9gvkjRbrcgZaGJi6DkM80VohUZpbsCz7IcZ8PzkhRYLFKMpzOcnI1wOjzH2fAc
      w/MxZvN0qWbTwakc3jjttJErjdH5BOPJDJPpHPNFVnsjiyzHZDpHu5WsySEPDCHf/QA4OR36
      CXIJYD5STYRcFX1IgcYgdiqIgxcqKNqpV9ddDYH/+PsfrCV+APj42Tn+y//6GIwJm4Zi1RvY
      gJtt1XhyNkKSxDgc9Ky9aH4vrKtaWqJPInGtxA8AMmnF0DrDZLZAt51gPJlheD7Bg/tHiKRA
      HEV+kp69OEWSuPfFIE7q+61w7HdHBz0opX2UL1cKL06Gxptjj9G2y8CqVcWMx7xv2pX8pVmO
      4fkY3XaC4yJ5vTRZSrvuCW57n7tL+FXUqYA+CFY59tPTDO//8OVG4/7RXz7Bb/zNt3DUKVLc
      GWMYT6bQWuNsOEan0zbfeYFpswkq13MTz0tyxtDptDEcjnE2PEeaZgADXpyc4fXX7iHPFRZp
      BqUVjo8GhmNRTBZqLjy8ARkkLMWI0Gm3oIxz2Pj8gyLtVWOEu5Es0gyj8ymiSOLe4QA9Gzuo
      A2cA6WLz6gbOhewcF3WbXhvJnQcr/QdPz0vNdNdhtsjx0fMpjt7ulKL3g37Xp0AoKvv9XfDt
      urJv10EyBjACDgddEGlMZ3PMZnMcHfQxOp+YpSiOcNAxuvxFBdpV1On9zp9fVzPgwILVhAfe
      qDRNrYFsMjbXTRqRSbYTopH+Dl7aVnKxwu/ziktousixDWb2eF2xMwiFI8Rcg0GdgXtTkMPz
      CdqRAJgJTLz95kNMZ3NEUmA6m+PB8SGElD6TcFviN9VOJtvbeYsYc71g6qWKI2pvtFp3USQ5
      7h8NzFalmbKqzWrp3vj+l+FspVxpcLClOarKIyLCvV6CbXDUS6CdtA9WEuf+dNm+nhFu8RHx
      XreD8+kM5+MpXFnkfJGi123j6KCPs3NT/3lZOnKdJVx1v2s5sq5cMjTMgOVVhDRhOpvX1hu7
      6KLbheW6U5tfRQhu6jA2k7qE997oo9PaLGZ6PGjhi/db3u5zqnLo+2esyPs3m+vd3vOR5+Mp
      2q0EDMBikaHdThBFEj/65CmiSCKSAotFinYrtjdQnzQWtjJx3xOAySxFFEUALNdTNe21PF51
      LnQwac59GUUC/V5nKcJLTODDHz9FJCU458hzhXsHXRxsKcE+73BRW62Mr15dIIyOOgx/7+fe
      xO/83x9dOPY/+vpPIBFmExRlA2hF9+tlurlN6Q8AUkqBdisCA9Bpm9TmLDehaM45WkmMTz97
      DsE5jo8GABjaLVcoExCyy/XwBEuYzDIscsLJaOgJsp1IvHbUK67ARcOKD5YusiCSlIAAABgC
      SURBVM7uCPNYAFOVNlukOOh1MFtkJou0HaOVXDrd6XMNQ4wAsBzuXxJK0Pj1v/EFPD2d4E/+
      6nn9eAD+zle/gL//s/chmCrlEZUCbbDPbof3chUsZYMqrXE6PMf9owO8PBsFYWmG+SLFvcO+
      17udL96pHHVIM7VkqPrCCxSdgcn6iOuWwyoDOHXKeyxshqBjiLAwB7jbQa91MI3AbIVXzars
      4OI7qZb4b3/6Gf7nn36C0/HcdwN57bCNX/va2/jbP30PMbNxF/fbytgFU8FUCd7yElBiAMaA
      J89O0Ot20O+2cXI2Qr/XQZ7laLUivDwd4dGDY0+AqxpTrYNrgMRQ3gsXMAZtXUUWQ30/Th8X
      sN4Gwbk/tiH6zeCeoauH9uokjFAK3dxmbgXmOcePT+eYzHMMOhHeOEwgWG7yi2C3cVpTscLZ
      cjPi24I0RKMAMMzmC0gp0LFVWgf9Ln789AXeeHgf48kcFBiYl2lN4YyiqoRxbwVnqBZpAEVh
      xUp/P1/d/7PBerg5m80WyJVG3Oqi1T1Y+5tWDHypXaS916GaWKPzBSifmoq0PXpWkjETiHj6
      /BRRJHHQ73mXZ5rlePjgHoTgaLdbJq1hOkcSxysT6FbBSX7AnE/YDR1yXfR5cVFhU/JG4IKD
      yPayrJkwY8zxWuOqwXYY9DrIlIKMWvju974Hxhg6nQ7SNIWUEu+//z5+6Zd+Cc+fP8fbb7+N
      b37zm/jGN76BP/zDP8Rrr72GH/zgB/jlX/5lvHz5Es+fP8eXvvQlnJ2d4ctf/jI++OAD/MxP
      vwcAe0X8ACCnszm0ytBpt9BuJWbLekt409kc9+9ZaSA4Xn9wjE+fvoCUpsa322mtvBnnoanm
      3mhNkFGg5jiDC7DpDvZjGPsBgA1kCdSV9G4bmGtQDydENBFGoxE453jy5AlaLZOs9s477+B7
      3/sePv30U3z66af48pe/DADo9/s4Pz/He++9h/F4jG9/+9v42te+hjRN8ed//uc4OTnBbDbD
      45/80t4YviFku91CxBOvj3MY4/Hliekp6VpUcwDETauN43sH0Erj2cszDHodtJLY5uRn6LQT
      zOYpMmWiypwBB4MuIlsTzBiQ59pu2FbU7roooQmOme5zznesNQHCJ1+U0BD/bmCaF3Ck5Gpw
      Bd59912vep6enuL1118HEeH+/ft48uQJANP1o9/vYz6fQymFL37xi+h2u8jzHI8ePcIXvvAF
      PH361Nh2ylZwrWlOcNNgw+efkOSEzBLl8HwM0oRet+ObUVU9MKfDsWmXKEznhsl0jtl8gfv3
      DnE2OsfhQR+RlJgvUkync0SRxPG9A6/F8EC3D+MHYUpuWEwdCd4EtG4ImeYgJu2z0CXDOESe
      mz3U5IbGLCMFnS8AFJnD+wDvJPdLoCZ02q0S8Yd+YcaYLVownR+ms4VZITjDfGGYIIkjLNIM
      gnO8/uCebWRVnNT5gatlceRd0sFWoGx9j/8Gu4VkCpnKwDiHACHPlN8c3ae7A+BaYzicoNNO
      MOh1Nhuc85XpL7cFDhTGJJEh/uFoUhB/cLCr1AfM/0kc4eigh/tHA/Q6bbSSGJPpDLnWmM0X
      6HRamM0XSJLYE7RjKBWoP3583yveHQvEkQDBxCcaXD8YM4XoynZ9c61klCr6+Zjnw3B0aJ77
      pjBNgPdLmPkVgDNgNJkhzzX6vaKAeTbPMJrMbYWORiw5jgbmpovUhAhRJHB8NMD/+6uPoYkC
      TxEwOh8jiSNb4mgmcT43bTRcQlQo8TXBu8s4Y8j8TucNbgLceCR8K5pYCnz67Ayj8QTdbtc0
      yIVxWhz0EvTa8UVDeuwR7QMoqUCmW0A1wSySAu1WgjTN0GnFpfbYRlsxRrLrCfTo9WO8OBni
      /r1DCM7Q7bQgJcfZ8BxxHOH83PSj6bRN14fX7h/6bE/OGLTWSKQo7fQXtl1vcP1gzGziR+Sy
      R4Hjoz46nRbMc+JWXVZLLW3q4LKCXYIct60N92ElKF29Wf6KBDStTTZln0dA22zHqTuR98Vo
      FAUUhwcmftBpxej3urani/Hpx1GEs3yM88kUB/0epBQY9DrotBOQVkji2GYGMjAsG1V3rZpr
      X7rUFQmKDK1Y+Pwh17iKMVm7Mvud5gmIpevNBCv+zfG5InBOpu3hLTLDkmuWsWLLen8QL0og
      XZW+RnlndefvbyUxXj8+AIGQa0KW5zg5HVl7YYDDgz7yXCHLcvTaCTqtBIqKzTXqJuK2CeGm
      wRgr2Vv7AEe/RCh1+E7z5cS3LFelvdac8yT8q/ZpzZXGaDyzFYI3d98rYxOG5spJTEWHLqOW
      RLJ+IzSXl0NEmE7nGPS7ODzoYzZf4PRsBKUUxtN5cTxWb+oM2M3U9sx7cN3YFxXBQQiOViT9
      /g5uXwCCIV6Xk2UIeYqTsxFAeuU9EBxjmNd5nkPb8lgXEwrb418X2PD5J1TXM9Zwsi7FAXxr
      OlxszbvenUqZ7NJ7hwMs0gwt6yKdzIxh/eD4YO04RGbbTE2EZE8SqO46tCZkSpX6frreofN5
      itl8AcYYHt5f/2xD3Jbat2YFMDcUVmVpbQooog0I0SWoJZFEmuZgAFrW65PEkWmIRcD5dH7h
      kkdk8oca4t8PmJYlEq1YmM4hUqAVm75Lk+kMUSShlMJskWGRKSzS3LenWQWnGt001prwfmeR
      cBvZDa+xUI3ItM1mRcOsNM8xnc+hlPa7F64a19kkLiWiSXzbH5j0ieJZxJLjoN9FlptNUWbz
      BEqbTt/9bhv3jwZrRrsdrGWAUL+/rF3CGMNBv4v5fIFOu4VFmuHosI9Bz0xUmqUoKkdXjGH/
      8bucN8XuewnGGPrdFpTSOOi3MRxNEEmBRw+O1zYvAPZQBXIwO6xoaL3aoLlwDMmhlEIkuXF9
      AlgsFhhPpui0W1i3VyJRUSnGGLONti51GQ1uCMJuo3R00EO3nSDL8pUrvFOtzydzzBbp/niB
      /AGBbnYBE69EniscHpg64EGvDZBGlivcPxqgHUdrJ8cFY9wx4esG+w3j7hTIlcJsvqgl7Fxp
      zLMc09kCnAukuTZ2Q6ZMqWuuTLWauh7GuDCMZ/KEGEDMpyVsW9QQx5HpC2kbVLXiCMlRdKE+
      H9oGDAVDZErvZIvMBtcPxoDDfgfD8QzD8RTddhuAcZcKzqDI9foviihdBaBW5JPvFAgs10is
      I2VX2EimS14ELlyfn20gbUwg1PMuqtt1tQOFCxY+KW6XO4U3uF441bXfaaHXbnkX+ngyw3SR
      eRVXa8J4MlvyFhHZ+nGl8eJ0vHGLxk2xUc+QMA7AOVtnr9aCc4bxdIGTszEePTgybtA1cL5/
      blMznNojGAPR7fSQbHA1iEqzg3uHA8zTzL8/OhpA5RrTyRzGcxgVG54QcDYao9drI82NAJSS
      ++2R6vaR2BRbNc1hjG2leqSZgoYJYPW6bYBxKL2+wH2+SPHidIRWq4VICvTaSWlXmM/TPr53
      FYtMYTRJMZunZismxnwXijhJMJ9OcWx7VOW5aaujlMLLkyFeOz7C6dkYR4cDDEdj02JdEqJL
      qsTX2jVKSo40M3khkeBIYlnbzhAIOkbA7CwImFqAqqrUuD9ffURCQApmn29hC+a5AhcMSRJh
      MU8hu228eHmGOJY4HPQwny9ApEsaSK41hqeTraLOIa6VARgMEaeZwiK3u4YwsyFGK1CDxpMZ
      kiTBZLaA262qlcTgfJmr3eqhrVHQqEOvHggaSSQQR6JUC4JEYjqdg7TC+TxFt9tGFAn0eh1I
      KaH0HLnSSNMcnz17CTBTXNXrdmwf2H1jAHt3kRRYZDlya9XroLprPF1gNMuRDmfIsgxxHKPV
      amO6yMEWGrFk6LbjwiiG6RKhNTU1Aq8giAhppvD85MxsgdtKIILtuU6HI7zx8DWcj6d48fIM
      vV4HcWSE5aDfxXgyw2vHh0t6v9IakrZPp7j2xpmMMXCQ6TiQG1dXHEh/IoKMYuSKIMHAucB4
      OofgHFEUYTJN0esmgC7vIuJy07XNQASMysWC8zbYP2gb13nt+BBZpvDks5c4GHTR75lGW11b
      YhnuLefAGEN/Rf2xtm0eXVvMTVNmbqxzrOlAp336LIggBUevk6BDwDwJObqNWJqkuzRXmC8y
      RIJDSu6r0ELpz7nZUI1rc/MuEzWSrlXisuG8L0Undw1ks4mJCIs0xb2jAUDA8xenEEJsHOR0
      x/k+UgS7IZ957/aLK/9mefAbYQBX8hhHhqBNb1FYF6fJfW/F0vr3qdyjkkwcghDsVWv1f5ea
      7bg+yxXAmI8TqFRhdD7BvcO+3XlweRLCPXAbXD+yLIciMnuFtVve1dluJ5jO5jg5G+Ho8OKk
      ubpYVNhMQWmC0soX8nDGalP4b2wFcFVADEBqffyhAStY0TVaBm7SSHDbP8iU2AlW5J7zwB2Q
      RAJExisAhB0myBdsSFF247rjI9sJr2GC68fC7gd90Dc7RALmGQ3tRizHh+v7kq5DmC7jYKLK
      rsDG0ECY3nPjzfOF4Eg4A2mCsgl2VbIruz1t28QL/P+u3UokOCJhfM1Vl2uuCERm71mzeUPR
      qFVpAl+x+UeD3WHQa9s2OUVGQWb3ce5u0WKlDhc9N2MnuJqWW2IAwEhw4vAldG4TPB/xXUPr
      4bY71Rt2ho+LWIct3Ln93HWeiysnKa6hsQ+uE5wZoZMr7XemGU9nuLeB2rMO2zwqQiEYby2s
      yqxOlkSmqiiS3O/m6AqvjTFrd5OEUWuevRyuzAUKCVZyjnYskcQRBDddDdqJhOAci0z55r3h
      b5mtgHPL5j4VpX+eEHpqAKDdSjAeT3d+nrrnx1BWnVfWBO8LXINct3eAUmrjfpTAsjR3eUag
      1dtzhstzE2i7Hiitkecaygqa8WSK4WiCNx7eR5blmM7mmC9SPHxwXA6WWYSfnZxNICMBKQSE
      EFikOQ56sT2u/PzCTibALalA28AZvIC56ajSiEnrYkELaTW88Vxpnyvi8pnWSfeiW12zAlwX
      nCH65LMTACb9odPp4vnJCLEUOBj0wMdTKKW8e3TV41BEgCKcj8dot9srhSPDcp/ZvV8BLoKy
      Pv9wcqQoNtDW2vSpMWH3RprvE5TSGM9zUzHGudmg/bCP4XDkC6hG5xPkuUKrlaCVxIXk5szv
      Zr/JKu2Iv1qaufcrQBVePbELg4ss+tJJ1BtESpNPrW6wH+CcoduS0LGRwL2WxGyx8G02ARMR
      1rbB2vl4gsMD05lcY3v1tC515pXLLdYU+naLSfBtNao3yeALLlZlom6C6saA645rsBlcx48i
      vYUhTTMkSVSK8DLGEEcR8lxBXdBeZVu8MgzgCLD0GQACx1xLvJwynM4YFKQvs3OrhdvrgOhi
      Al4F1wN13fXphvi3BmMMUpoNUNwKDlBJ53cyrdtp4+XpqJRM6cepGbtahViHV0YFqpIWAXg6
      0vjNP/oRvvPhc0zmGQRneHivi1/5+Tfxd3/mPmJenihXVnlZJWgT9alRsS4HZqv/ZvMFWu3E
      p8+EMiVJYkSRxGQyQz9IlmPB36qCyVUluHtvBLvAF1AQLoHhzz6Z4t/+5vuYzLPa3/3Cuw/w
      L//xT6EldfE7KjJKG0LdTyit8ezFGQ5tYMzFfIbnY4BMn9Bup7UUNeYobME6JkikqLUZXgkV
      iCEgWsbwfEz4d7+1mvgB4E/+6hn+07d+hDDsYYieIVfbqSvX1ZKjwTK4DYSGpDpfpEjiGAeD
      Hrqd9nJMAEGcp2ZMwVe30tl7BljuGcnw29/+BOPZauJ3+P0/+2s8OcvBOavECOA3/d4Ezohu
      mOD6wRhDK4lwPp7ibHiOl6dDnJ2dI44kGAOkFJjPF2bfORSqj9P36+h8XV/ZvWeAKlLN8Z0P
      nm90bK4If/LDk6IVd/CdabGyGVEzxko71jS4XvS6beR5jqODPg76Pdy/f4jR+QRKaUTS7DgK
      mC1aq8+0CIoyxJKvVH0cXjkGyHLgdLzY+PjnZzMArCi6J/J/zjXaSPb9wngyxZENhElpUhwG
      gx7GkxnOhudQSqPX7eB0eA6g2E/A5YwlkUBsg14XxQr2jgEuIkbOi213NkErLju6CpXKTEze
      9BrdOzDG7MbqhQuUM4ZBv4tBv4vpbA4hOEgTpvOFyQSAObYVia2cHHvIAOuZIJHAOw83L5p4
      /OYBjF+53F+Uc1b4nRvNZm+gten6oJRpf1KkyJvnNZ5MQUQ4OR1hMit2GTKG8/bevb1jAMbK
      +xFXwaHwq7/4xY3GevO1Hn72rf5K+nanaFot7ge0JpyOJjg87HsvnfMAcmvDHR30cTToAUR4
      /f4R+t12UAS//Tn3kAFc2nI5kue/B/CLXxrgH/z8eiZoxxL/4te+gljUh0bcuKrRf/YCrnTV
      Re/Dp+JTJezr8XSGfq+Dbqdt2qnbz5NLBLT2MhJsYoBBKNwWxGsyKc+cafyzX3kHR70E3/w/
      P8QiK+eHvP36AP/8Vx/j3dfjUhDMMVVYGwDUJ0k1uHnkyrZBDz5zteOOFvJcYTKZQUqJOJIg
      m6IixeWyffcyEmykAS1Z8GYHeeE5hBjD6RT4zg9P8PR0gkQK/OSbR/ipN7qIeLXLsIZWyhvB
      WptdDTUR2rFsXJy3DCLC2WiC4XiGw8NDaJWDc+4FH2Aaoi0WKWLJEccRzs7O0bP7TV92V829
      ZYBM6aXcbc1bIB5vNMZsNkOr1fKTwtQMTKf+vbI1qbHgS52LG9w83K6kT1+eQ2vTM0gIgVaS
      wDgxOAiEyXiCNx8eIZLSMs34wp1G12EvVSClC199yXsD4Hd+93fx1a9+FXEcQ2uNDz/8ENzu
      P9DpdNButzEajQAA/X4fURRhsVjg8btv1ZbHXYX4m1Yqu0cigcFggEyZ/qGyIgTTgekarm3v
      qMNB90rPYC8ZwORuCF/c4m5wli7w5MkTKKXw8OFDzOdzDIdDPHjwAO+88w6+853v4OHDh3j/
      /ffx+PFjnJ2dAQA++OADPH73LQBF2jIBS7uVbwsi47ZrVpCrQ2nCYpFCSgmAEAmjplJFtXEp
      EcLXgVxNfdlLFWgVRjOFnCQmkwmePHmCwWCA58+f4/Hjx/j444/x6NEjzOdzfPbZZ3jrLSPx
      X758Ca01vvKTPwGmU9MgySqVyRXLJF2NAg+KOhpsD6f+nA7P0em0IYPmZXJFNPeyOn8VrxQD
      aAjQFp5bAvmlklQGImVKKGFWliS6+gLodk2PhDHOG0bYHkqbjfHOhmMcHfZL3zGUN0VxRF/X
      zeMy2EsVaBU4FIDNS+KIyGSBEkCMQIyD09VVn3B8twoonSOWAnxHY98luDTFukov1w+KMwa2
      pub7snilGGBb+AxQ+0+1EdYu4FssyvoeQw02g9a00pYi0LVl494Z680Yvy5zcEfjadOLlPHV
      JXcNLoYiMqWQybKLmwX/Xgc+1ytAFZlSpiMcZyagdgUoTUhzhVgKxEI0uv8VUGyJuyyP48h5
      A69nfu/MCsBY4am5KrHqIFDnshQbXA5+8/M0Qxwvy2O1432Bq7gzDADAtk6/uEhiFYzBa3pa
      Sl60Vm9wNQjO0G63MJnOSp8zhtpNLXaJO8UAVyltJCLk2virAdNYt5H8u0FuE+DGkzIDiBsQ
      LnfKBrgKCOZBOYnRkP5u4Op4IynRbiX+81jyG2lf0zDAhmAwKpQmWttloMF2UMr0bcqzvNL5
      +2bm906pQFeB62N5FRuiwTIEN5J+kaboBCtAmqsbaTVZWgGarYHWo5mX3cKlvbu+PmGIV9xQ
      ftXSCrCuHrdBg+vAfJGWgmBO3bwJgVNiALfMN5KuwU3AddzOshxS2B18UE5+u240NkCDWwVn
      DN1uC2eu1eEaIaxdLccONZSGARrcLhgDYxztdoLhaIzhaIxFutz31eVe7bqFTeMGbXCrMF0+
      gJ5td86YaX5VTYwz2+ruXjVvGKDBrYMzBrK0TYGac+NGcIMGNw3XpjKspVDKVIjdREv6hgEa
      3DocE5jtbTmSOMJsNve9Qa8TjQrUYC/g2t9wznDQ7/jPrhsNAzTYO9xkHKpRgRrcaTQM0OBO
      o2GABncaDQM0uNNoGKDBnUbDAA3uNBoGaHCn0TBAgzuNhgEa3Gk0DNDgTqNhgAZ3Gg0DNLjT
      aBigwZ1GwwAN7jQaBmhwp9EwQIM7jYYBGtxpNAzQ4E6jYYAGdxoNAzS402gYoMGdRsMADe40
      GgZocKfRMECDO42GARrcaTQM0OBOo2GABncaDQM0uNOQihigml0hG9xN/H/Ta45Up2dgzQAA
      AABJRU5ErkJggg==
    </thumbnail>
    <thumbnail height='192' name='AdventureWorks' width='192'>
      iVBORw0KGgoAAAANSUhEUgAAAMAAAADACAYAAABS3GwHAAAACXBIWXMAABJ0AAASdAHeZh94
      AAAgAElEQVR4nO3dd3Bd133g8e+9r/f3gIfeARJgp0SZNiVbXndb7uuS2InXySabmczueic7
      mf0zs56UrbOb7CSTyCW24yLZli1ZsgolUhJFUWIBRYokSAIE0evrvd62f4CkCLGDAB7Adz4z
      nEMA9537I8/54d577jn3SoZhGAhClZIrHYAgVJL58l8Mw0DX9UrGIgir7koCqKrK9773PbZv
      317JeARhVZmv/mLHjh184AMfqFQsgrDqxDWAUNVEAghVTSSAsLwMA01VWS+j6yIBhDtm6AoD
      rzzFW/v3or3rZ1o5w1P/88+JJXJLrn/i5AFmJ6fvLkjg/GtPEQ3HMQyVs68+xfToOIahM3x4
      L5FQFBAJINwpw2D86POMD51n4MibSAvfRC3mmB0+y9SZw0g1G7EoceLhMGCQnBsjGYth6CrR
      yWHCM9MYhoFazDA7epF8MsLsyAU0TSc2cY7+535FeGqcTDpDdGKIXDaPWswyOzqMbuiERs5S
      yOcJT41jGAb5RIiZC+dQFPWqOIuc3vcMZU0mdPYQpw6+QiqVJTNznhOHjuCtCaAppcWjQIJw
      K6XULG8dOsb7P/cF9j+zDxlIzw6z7yffxVHTQHxymC2f+mMuHnqakncL79mznVd+8h3e//v/
      gVd+8B0KikRiZoIP/+lfoUy9yRv7D1LXVMfcxWE+/q2/YejFJygaNsJDx2nqaOO1nzzKx7/1
      N0TPHuDshTgf8VvY95PvU99aj+TtprP5bc70n8QoZ6jZ9CE+/IXPA6Bno+QVGx6/nTefOULb
      hh48NUFOvvQ9tn38qyiJSfb+9HviCCDcPsPQ6X/mx/S8//PIWhGL1Yqhqxx56kdseeSP+eS/
      +RNsNgedW7ejlEs4XB7O7HuC9j2fI/T2ixTkADs//AhenwNdUxg7/Ra7v/SnPPy5L2MPNBOs
      r8NXW8PGD3yBT/zRn6HHR7A29OLzOhg9fYyune9leqAfi8vLto9/gz0P7+Do/tfY8dEv0NHX
      h6YqV2LNJqJI3nqip14iuO1DlLMJlOQYsYKbvu19HP7V9+n7xB+IBBBul8HcmQOMDI5y5oUf
      svdnP8ZstVGKjzM/V6Bjcx/hC29hrt9EwO9ELRcpxseZDpXZuWcX42cH8AWDJEOzdO7+FAFH
      iVCsTFtPF5MD/dT3PYDFKDA+OEzHlu0YhsHY6WN0bt9NOTHJfKhA+8YuRk8d54Ev/BHNHW3M
      nD2Bq66ZQiKExdvMtj0PXYk2m4jicts5d+I823bfTzpdZviN59n1yFcxsrPMzaRp3bhRJIBw
      e9R8gsPPP8cnvvXf+eZfPcrDn/0MZqsdwzAwtDQvPfo/eP2Zp+jasRsJA6VUZLj/EO/53Nex
      mM04vR6mTh9h7HQ/ZcNG+MJJarrvw2E1GBs4Rdf296AW05SKBQ49/o9ksmnCk9MMH3qavT/6
      RwLd9yFnp4lmzLR1tQJg9/hIz40yeuoYExdGqG1suBJvNhElM3uRjj2fxlyOkoqGUF3ddHS3
      Its8OB06L/z9XyBdng2qKApHjx4Vd4KF65ofGSAeiRGoW+hkpWyCQknDXxskG5tDtnlQcgkc
      gUasVguZyAyqYSZQv7C9rhRJRkKYHV68gQC5RAjJ4sLptBMPzeFraMEky+QSIcqKgb++kUIi
      jCZZMVHGMDuxmg1y2SL+YN2lqAwykTkUzcBX14jJZLoSbyEdJZ/J4W/uQNKKJELzOAMNOJwO
      AMr5NJlUWiSAUN3EKZBQ1UQCCFXtyn0AWZaZnJzkwIEDFQxHEFaXJJZECtVs0Z3gXFFBAgwM
      JKSFUpIwjEulbiDLEvq7Sk03MN2iNJtkVE1fKFUds1lGUXUsZhlF1bCYTdctrWYT5XeVJVXD
      ZjFRUu790izLqLpe8Ths12mH8k3abaFcaN/L7X2l/TUdkyyj6fpN+827+5muG0jyVf3RuKqf
      ImHApakZ17r8s3f3bdO3v/3tb1/e6ODbE/g9doplDUXTKZZVVPWdslBSUfWFUtN18ldKBU2H
      fLGMbkCusFBmC2WMK6VBNl/GwCCTLwESmXwJ6aoynSshyQulLEukLpXJXBFZlklmi5hMMslM
      9ZSnRkI4bZaKx3G5lGX5Unu80z6pq9pNkha3K1dKFtrfMN7pD5f6x+X+kiuUF/pRqYymG1f1
      r3f6nfqufnm5nyqaRqmsUVY1SsriP+WryoW+rV2pY9Ep0AtHhnlkz8ZlPcQsla4plIslrA4n
      snz5Wl0nPDlBXXvXDTNdU0qomoHNbl/SftVSEdliQ5ZvtIfVVYk2MTSVUqEAgNnmwGxZOFEw
      DB2lrGC12a5sqxUzJFMFahvqUYp5NE3DYndhMl1/fEVXy8RCIepa2q7dr66hqhoWq3VhW02l
      XCxidbiu2x5quYhsvru2WnQECCdytDf4llzZ8jE4+uT3yWaz6LIdNRMmMjuPp8ZH/3NP09bb
      y9TQWZz+OjKhMcKz83hq65Akg4GXHuPUsRP07Lyf8MQFkvMzKKU8uWwRu83EzNAAZoePTHgS
      s81OIhyhkAyTis5hSGb6n/wOBcNFbVPjDZNsNVWiTdRCkrMHfsPAW6cItnZRiE2RThcwstO8
      8qtfU9+5kdTcCOlUDque5OyZEdq6u3jrqX8iEkkxdvYcXq+dfCaNyWxlbngAk90LWoHp828z
      PDCAx+PEbLWRCIcxSSozFy+Qj45wbN8r1Hf1YjXpHPnNj8ll0th89eQi42TSBZwuO9ODp9El
      mbd/8z3Sqov6pgYkaWmttegaIJUrLct/4HKwO+wUckXcvgCh84eYGDiB4fgGAKf2/hxPazcn
      XjtAZvwoHbs+hqaDJGnEU2VaghbisTRnD71MfYOPmKuDyNggDUEbrrb76H/hNzikFL3/6rMM
      vXUKU3Gaxs07GTs8gdXhwh2oXROdHyrTJhZXDd1bd6BO5HBKKfpPnsVpxGnctgerw4PNbmZ6
      YoTJ86d44NNffueDkomWvh0MHNzH6NG9lGwtuOQsrtatHH/hSVyWIvWb3wsGXDz2Mr3v/yRD
      b72FkQux4cFPYdJUbC4fVpuVfGwMXK1se/gT5EIXGDh1HrsWI17fwtxMgi3uWmwOB+6auhv/
      Q27DouOUz72004blp9O09f00N7o53/86Y2cvEKirQysvzPbTygWic1H8tX42PfQxZs68TjZX
      ohgbI5vKkMtmmb5wHovDg8fnx13bhEkGXdOwOpwYhgYY5FOJhYsjk5VAUzMY4HA6MVmsFf3X
      X63SbWLoKrLFhsViwuxwI0syemaO+XAat9eFpl71KB1DY35slPd+9qtIZis7PvI5TJKB1eEC
      XUNVNOyXpiJI6ORTCXTDwDDA7nJhd/uQZRMmWcbmCVKMT5OOhynk8pgsNkwmGWddN20d9Qz2
      H8HudmMy392M/kXXAM8dHuYzD66FawCD+QunSKcLtG/bRWz0NLpsxdfURSY8S7CpjvGzZ6jt
      2EI5MUlBNdO1bSeF2Axl2Y3PbWZuah5Z0nG7bRhWP5nILPWtLUycPUXdhp0YuTCJRBq7uxaT
      XsDf3EI8lMDntTI7OUvXzl1r4i5hpdqklI6QyqrUNzcyN3QSRXLRtrGX2fP92GvbSc+OYna4
      qGlqI5Mp0NjaQnh0AF/rFmxWmcj4IP7WTUhqhvGBtwl2b8diZAnNhLC5fbjtEvF4Cru7Bp/P
      zvTIKB07HiA0eAJf21Z8AS/ZyBQzY+O0bdtNauociuymoSnIxPmFtnfZDKbGJunZ+QDyEk+B
      FiXAi8cu8tC2NgwDJIl3lRK6cfXwlYym6ZhM15aqpmMxyShXl1cPeyo6FotMWbk8vKdis5iv
      X1rNlMoLZbGk4rCZKVRR2T84w+5NLRWP43Jpt5kpllXsl9vlRu1mMVNSNKyWd9r7nWHva/uH
      +Tr96Eqpv9PvZEm+NAz67v55nfJyJ+fG2yy6CN579CIOm4VQIkcyUyQUz5HKlZiLZ0jnSszG
      sqTzZWYiGbKFMjORNLlCmalwmnxJYTKcolBSmQilKJZVJuZTlBSNsbkEZUVjdDaBoumMzCZQ
      NZ2LM3E0XWd4Oo6uG1yYigEwOBUDSeL8RBRZkjg/HsEkS5wbj2CWZQbGwpjNpqooR2YSFMpK
      xeMwm0ycvdQO58cjC+0yEUWSJAYnFtbXDk3FMAwYnn6nXTXN4OKl9h6ZSaCoC/2hpGqMzScp
      ljUm5pMUSuqV/jMVTpMrlpmOpC/1swyZfJnZaIZMvsRcLEMqV2I+niWZLRJK5EhkioQTOeLp
      IpFUnlg6TyyVJ5rME0sXiCTzxDMFwpe2vdy3Fx0BzoyG2d5dv6RDyWqam5sjHA7j8/koFots
      2rSJ6elpcrmFhdgul4tcLofZbKa7u5vh4WF8Ph/JZBJFUZBlGYfDQVdXV4X/Jbe2XtpE0zRO
      nz5NX18fIyMj1NbW4vV6GR0dJRAIEA6HCQaDhMNhtmzZQj6fZ2xsjGAwSKFQoKenh3PnzrFr
      165VjXvRae7AWHhVd75UiqLgdrtRVZWpqSlKpRKNjY3EYjECgQDFYpFMJkMsFqNQKDA1NUU0
      GsVkMtHc3IwkSdTV3d3owWpZL21iMpkwDAO7fWGRTCAQQFVVLBYLfr8fSZJoaWlBlmUsFgs+
      nw+LxYIkSYRCIfr7+5mamkLT3v2ciZW1Lo8Ak5OTlMtlPB4Puq6TTCbxeDxXbpHLsoyiKJhM
      JvL5PA6HA/ulG2PBYJBoNLpuEmC9tImqqpw/f56mpiZg4ZdUqVSiUCjQ0tKCpmk4nU6KxSLh
      cJj6+nrMZjPFYpF8Pk9rayuxWIxgMLiqcS8aQxqZiRNL5VG0S3NuFA271UyhpOCwWcgXFVwO
      K9lCCY/TRjpXwueyk8wWCXjsxNMFan1Ooqk8dX4n4USOhoCb+XiWplo3s7EMLUEP05EMbfVe
      JkMpOhp8jM+n6GryMzqXoKc5wMhMgg0tNQxPx+htq2VoMsamjiDnJyJs6azj7FiarV119A9E
      2NZVx8CsxrYuCwNjl76+XF5cKN+6EGFbl/XS9/V3lXVruqzzOxl4OVzROM6ORdjaVce58Qib
      O+oYnIjS117L0NRC+wxPx9nQEmAkbkKxKYzNJels9DERKtDe4OPlU7O01nmZiYRoDnqYi+nE
      lSyhRJb6gItIssB0eoZYKk/NfIFEpoDf4yCVLeJz2UjnS3gcNrKFMi67hfyl/lgsq9gsJsrK
      wtyjhTlGEvqlX4Sw8NRzWXpnPpqiLWxbVjRsVrOYDSpUt7Uw1C0IFSMSQKhqIgGEqiYSQKhq
      IgGEqrboJXkjIyOVjEUQVt2iB2N99StfIl8orZubRIJwtxbdCNu+dSu79zzE7t27KxWPIKwq
      cQ2wDLRykXKxeIefMijl0iz/bUiDYjbDzao1DI3CLbapFuIFGcDIoWeo3fJBooNHad7xftAU
      HF4/ajGHUlZxeDyUCgUkScLmsFPMFzCZJJCt6EqBUjoKjhq0Ug6TzYXZLFPI5XB6/UgSTJ86
      RCFfxFHXSX1zI6qq4/C4GHz9JbZ//IvkM2mcvsDC/hQNh8eHoRUZOXqATCLO1k/8LjarzIUD
      T6JhxuRppb1vI7IsY7bZKGQyOH0BdKVIPjnPxPkRNjzwPiw2C5ouY5I0SsUyanqGmckYPTu3
      kY4lsVjMlMsKDreLUi6Hrmk4LsU8f/4Y6WQWT2M3wcY6NF3C5nRSTMeRbS6yU6eJZy303Hc/
      a+T5AUsiEgBw+70MvfYsGx78CFMnXkMrZWm472NkJ88wN3qRjbv3cKH/GO33f5DG5gAX+t/C
      lJ/B07mLQjZPOTFDoKGGbNFMbVMT0fEhtEKK1ge/jN8DM+fP0vnQI9S3tTJx/ABzFy+y5cMf
      wV7byoVDezGUAoHuHcyeP0Wws4+2LTtRslGiMzMEO3uRJdBKaXJFM12bO5mfS3Hytz8j2LMF
      NZ8BLY+nbQep6WEop/F17uDiiWP4nRqSt4XU/DSB5k7iF4/jbNnJzJnD2Oq6uXDsdUrxWdp3
      7WFqaBQ1Ocu2z/9b1NgwibTBpj0fITt3gZG3j5OZm6Klt4NU2iDY1sn0wCmCWx+udNPdNXEK
      BJitNoK9D2BRoiTjKbB4SI2/jaKDbPWQnBlj00e/QsuGDailHMXYLGZPA9GpMZo7GzF5GnDX
      tZOfHyYXmyObziPb/DidVkqFMrs+/3uMH97H5IkDaCYbstNPfOQ8LpeJZCKFYXZg9/jxB1zk
      82UwVIb7j9HQWkcmlUGSJZKTg+i6SjprUOu30rTrYwRrnDgaejFbbORCFwhseADDZEMqRfG3
      9hCPpSmGLtK07UEaurpQDAcdW7eSSRUozA3RuPk9mB0eEhMX6Lj/vVi89dgsMsmZcbwNbeiq
      wtSZk7Tv2IXV48UZaKYYGcFkd4IjSOvGvnX92x+qPAF0XSMTC5GKp/H6PWiGFYtZx+z0IWkK
      pWyaQGsHqm7BpBfIxEJkk0lsvnqcbie+hjYy4Rm8NX4mT/djq2nHHajBRBmrN0gpHWHu3BEG
      DrxA49bdqKUipXSc2rZuSpoFl8eL1WRgcvqJj5wknS5Q01BHNh5BQqGkO/D63RSSEVKxJO07
      30egsYlMKo3H68IwO0mMHMdS04E3UEt06CiyI4CmqCTGTmH11eOqbWD8yF4S83PIKMycP4U9
      0IAzUMfEsf0Ee3agGRb09Bzu+may8TDOunbCAweYPD+AqzbIhUP7aO7dwuTpfiz+Vkx6AZQc
      2VSm0k141xYNg/7lf/2LqhoFMgydciFf6TDWLYvNgXzVSynWo0XXALJJJp2Mc/KtY5WKRxBW
      1aIEMMkmPv/ZT1cqFkFYdYtHgSTQJMuyVKxrKrpaXpa6BGGlXDMMGggElqViXS2jlQvLUpcg
      rJSqHgUSBJEAQlUTCSBUNZEAQlUTCSBUNZEAwh0bGxvnL779lxSLRfbtfwVd19n74j5ePXAQ
      gJdfeZWfPvZzxsYn+L9/9/cMDJzlt88+z3woxL6XX6lw9IuJBBDu2MFDb+Bxu0mmUjz97LPE
      43G+/4MfkssvPJxYlmWOHD3GwdcPEYvHKJZK/OrJp3jt4CGOHuuvcPSLiQQQ7tjmTX0MXRjm
      woWL2Kw2Zufm6e3dyJuHjzI1NY3VasXtdtHd1UlrayvH+o+zqa+Pc+fO43a7Kx3+ImI9gHDH
      2lpb+fjHPsqWzZvI5/PYbFa+8Xtfx+V0ki8U8Ho9/M5XvkxHezuGYXDfzh1kczmKxdKS3+Sy
      UkQCCHesqamRr/3OVwD41Cc/ftNtP/jwBwDwer0rHtdSiAQQbuiNNw8T+l9/u+RXkN6JcKHA
      Z//5UVpamld8X1dbWgIYBplMBo/XQyISQra7KWWzBBvriYYi1DU2LHOYQiXousH7AjWrctoy
      bs5gVGCZ/pIugmdHz/H6G8eZOHeCN4+cIBWZ5flnX2R6bJBDh99a7hgFYcUsKQFsVjM9m7Yw
      PDqNRcszOTVHc3sTc1OzbOjbzNDgIIODg8sdqyAsuzs+BSqkIpw8O0J92wba25opWwP4nBbU
      Yh6r04m/vgWXzSymQwvrwh0ngMNXx8c+JVaNCfcGcSNMqGoiAYSqJhJAqGoiAYSqJhJAqGoi
      AYSqJhJAqGoiAYSqJhJAqGoiAYSqJhJAqGoiAYSqtrQEMAw0TQNA1zQ0XUdVVQzDWCiXM0JB
      WEFLWxAzPsjeF19FKxd46dc/49zQMD/+7o+IzE/wxBNPL3eMgrBilrQk0uNy0N7VzcGX9pI3
      bBi5BI1dHUxcHKF381aGBgfR1TIbuzuWO15BWFZLWhDz+hvHqe/awqad9zExFcJs99AaNLC7
      PNS1dxFw28SCGGFdWNKCmE//669c+bqprWtZAxKE1SRGgYSqJhJAqGoiAYSqJhJAqGoiAYSq
      JhJAqGoiAYSqJhJAqGoiAYSqJhJAqGoiAYSqJhJAqGpLXhBTLpcxDIN8LouqauRzuUtf58WC
      GGHdWNqCmLFB9r38Otl4iGMH93P8+El+8ZNfMD8zwm+ffWm5YxSEFbOkBTEup43Onl4S8QT+
      ph7sRp7GzjamxibYuHmLWBAjrBtLWhBz+Nhpgq2dzEyN4WnswB3w09Fowu50U9vSic9lFQti
      hHVhSQtiPvX5Ly58seu+5Y5HEFaVGAUSqppIAKGqiQQQqppIAKGqiQQQqppIAKGqiQQQqppI
      AKGqiQQQqppIAKGqiQQQqtpNEsCg8MbLFMfDqLH46kUkCKvopkcAU02AwuFXyRw4vFrxCOuE
      YRicjEUpaRqhQoGCqgKQLJcYy2Qo6xozuRyqrpMql9ENg3ipVOGor3WTBJCQnTa0TBFrW+ui
      n4Qmh3lp32vEZkZ54aknODUwyGM/+AmJ2Cy/euJpsSKsCoxlM0xks0SKRcYyGY5GwgAci0TI
      qyonYzES5RJvx2Lsn51hNp/ntbm5Ckd9rZseASSbA9lhQ3I6Fn3f6bBR39TM4PAEO7b1Ep+f
      xl3fwOjQEG1d3QwNDjI4OLiigQuV1eH2YJFlosXild/yAA/W15MolXCazEQKRbKqgsdiYTyT
      ofFd/WgtuPl6AJMNk9eJUSxe+VYhFeHAq4eo37iTci7FRNRNbU0NrmwSq91Na3cPDX6nWBBz
      j0uVy2QVhVqbjZKucV9tLbFikXBxoc2DdjtppcxWf4CCphEtFnFbLBWO+lo3TwBJwuTzU74w
      jOs9O4CFBTGf+53fW/j5/VtXOj5hjaqx2fhyZxdmWabN7b7y/Vq7/crfm5xOAPxX/X2tuekp
      kOz2oCeiWHt7VyseYR0xy9d2H1XXiRTXz5H/pgmgzY2hm9yo8/OrFY+wzu0PzfEv0+OVDuO2
      3XwYtLETMiEkp2uVwhHWs6lclpjbTF9XM2cTiUqHc1tumgB6bAZdtSKtwYsXYW1RdJ0XEhHu
      a2+mq7aG/myy0iHdlpsmgFEuo6+j8zmhMgzD4Lm5abZvbEOSJGRZwhxwMpfPVzq0W7r5RXCg
      EUutB8kkpgwJNzaSyVAKOPE53hkB2tbcyOvxSAWjuj037NmGmif5xG/QCznUZGY1YxLWkbKm
      8Uo2zraWhkXft5pNpO0mcopSochuz41/tZsc+L70BSQlj+ytWcWQhPVCNwx+OjvJg1u6kSTp
      mp8/0N3K86HZCkR2+26YAJIkIdc24PvDP8HeWbuaMQnrxP75WTb0tGC6zv0AAJvZTGqNHwVu
      fh9g9BSJx35FeVZMhxYWm8nlCDlNBN03v8O7rb2JA+G1ex/p5vcBmruQTGbU6bHVikdYB1Rd
      55nYPLs6W265rdduY9qkoen6KkR2525xJ3gCZWYOU13jasUjrAPPz81wX1/Hdc/7r2dDSwOH
      I2tzROjGo0CGhlTbirXRjTIfXc2YhDVsNJOmEHAsGvK8lUavm3NKbgWjWrobHwEMncLRI0hO
      H/YNXdfdJDQ+yHNP/pLjJ07xL4/+gGhkip8//qRYEHOPUnSd/ZlrhzxvRZIkGhprOZ9ce3eH
      bzwKJFuwb+6mPD2PEgpdu4FhEJoPk82XsFOmtr2N8aFhevr6xIKYe5BhGDw5M8mu3ts/9bla
      dzDAsczamx904/UAhoHs8mHr7kCNX2cUyNCIxuK0dW8Cs5Vmvw2720t9Zw9Br10siLnHDCQT
      WBp8OK1LmxdmkmVMfifz+TyNa2htwA0TwFCLZF57C5PDwL59+7UbyGY+8pkvrmRswhpRUFUO
      lzJ8oKfnrurZ1tLI6+fG+apz7bw77sZHAJMViTRKPoA8fA62b1zFsCrvxMm3eebZ5/jUJz7O
      qwcO8ulHPsmO7dt4/BdPsKmvl2g0RjKVoqW5iZpAgNm5OTb09NDR0V7p0Jfdk3NT7N56/evA
      O2E1m0jaZXKKgmuNzDC+YQJIsgnPF78OukbpwshqxrQmvHnkKPV1dbzngV1MTU3jdDiYnp6h
      VCqx96V9FAoFMpksmzf1ATB04QJf+Pzn7rkEOBaN4G8NYjMv6YWi19je3sRrF+f5dHPrrTde
      BTd/KoQkIZnM2Df3rVY8a8bmTX0ceO11pqanmZmdpbW1hUQyyeTUFKqqUS4rWK1WdF0nk80S
      DAYrHfKy0w2dU0aRzhr/stXptduZltQ1c2NsedL6HrRxQw9/9IffpL6+nt//+tcACNbW8rtf
      /TJ1wTpy+TzFYgGP24Nu6JjNZqxWa4WjXl4HX36ZPRuXNupzM10t9Rydj/BQ/Z0Np64EkQA3
      0N7WRntbGwCuS6MWDoeDZpoAqK29t2fIvvzKK3gpXnfh+91q9nl4Y3Keh5a95jtX1Qnws7/+
      HziO9K/KvsKtTfzpo/+wKvu6W+FwhNNvHqSzuRGGln8tiCRJ1DfUMJhKssm3fKdXS1HVCRC0
      29hRX78q+zrhWh8PFtB1nZ/+6Afs2d5HKBResf1sqKvh6JmRiieAWOsoLPL88y/QU+dd9vP+
      d5NlGdnnJFSo7M1SkQDCFRMTk4ycOk59bWBV9rettYGDsZU7ytwOkQACAJqm8fOf/Ijd21bv
      KYA2s5mkTSZ36dHqlSASYB1Jp9MrVvcTv3yC7Z1NK37q827b2ps4WMEVY1V9EbyenB8c4v/8
      7f/jv/z5f+YHP/oxD3/gIT776Uf4wY9+zMzsLL//9a/xD//4KA9/4CFkWSYWi9HX28v7H3rw
      lnVfHBkhOTVCx6a7m+uzFD6HnTOSRq9RmUn0d3kEMNA1DV3X0TQNwzAWyuWJTbiK2WzG5XJR
      KpVQFIVUKo1hGMzPh5ifD/Hscy8Q8PvJZrKMjU0wMTlFMpW6Zb2lUpmnHv8ZO/q6V+FfcX2d
      LUH6Y5VZdHVXCRCdGeWV/fsYuXiBf/nOD4mEJvjlL36zXLEJV0mn06TTGQaHLlBbU0M8HueV
      V18DiUtTMjROnjpFKp3GYjHT3NSESTbdtE7DMHj8sce4f0Pbqp/6XK3V72OglK3Ivu/iFMjg
      zNtnyKSzJDweGrs6mBgeoXfLFoYGB9HVMhu718601/XugV3389/++ts0NS5en1FuABwAAAit
      SURBVP3Rj3zoyt//7D/9xzuq8+1Tp9GTIVy1lW+n7Z0txGIxWltuvdB+Od3VNYDb48YRbMPj
      tmO3SNhdXoLt3dS4bWJBzBJEIhHOnj5z022Gzp5bln1Json+N1/joW1rY5p7RzDAS88/z84d
      O1Z1v3eRABK7P/ix5YtE4Ozbp+l7/Fcrvh/DMHh0ZoIP/buvVfTU52qSJKGk48zNz19zlFtJ
      Yhi0Cr0Vj2FvC+Kw2yodyiI7+7p5ae+Lq7pPkQBVJl0uc0LL0xNce7NZbVYL4ckR8qv4WHWR
      AFXEMAx+PT/N+zZW/qL3RrZ2tbF//8urtj+RAFXkzWiY1s5GLKabD49WUsDnYej0CdRVmh4h
      EqBKRItFhk0aLX5vpUO5pfagj6PHVmedhkiAKqAZBs9E5tjVvTYWot9Ke3MjRw4ewFiF6REi
      AW5Cq9D8lOX2amiOnu7mFVneuFK8ZoPhixdXfD/r539klUWKRZ4YGyWjKExms4xemol5Lpng
      cDjERDbDwfk5JrNZBpNJEqUSb8diFY76WlO5HPNOmTrP+liRdtnmDZ3se+GFFd+PSIAbyCkK
      FklGYuH9tymlDMAGjxdF1wlYbeRVlVipyFw+z0Q2S1ZdW29CUXWdFxJh7mtf3ekFy8Eky5RT
      USLRlZ0kJxLgBjwWCz6rlSPhMCVNZzKbpaxpHAqF6PX6KGkaDQ4niVIZt8VCTlUwr5G7qpft
      nZ9lS08Lsry24rpd923awAvPPbei+xDrAW7gcgLsCgaRgJKmUdI1erwesqpC0O7Ab7Wyxe9H
      YuHR4WtlWgHASCZNwW+n27V2HkR7p+w2K7MDAxQKBRwOx4rsQxwBbsBqMrG7rg6TJCFLEg6z
      GY/FSofbwwavD7/VSpfHg81kwmoy4bJYcC7T4wPvVknTeDkTZ8sdPsd/LdqxoX1Fb4ytjRZb
      JwzD4LVIiIvlAjbNoNlqp8ftodHpwG5aG/+VhmHw27lpdvRWdo7/cgn4vBw8eZxHPv0I5hW4
      gXdXrWboKkf2P0+gewdnjx7lY5/5MAcO9PP5L35mueJbMwzD4MX5WYxGLw/VtmIYBplSmePJ
      FNl0BqOoYNcM3MhsdHtpdblwmc2r3gnPJpPIdR68a2yi291oD/roP9bPgw/uWfa6l5wAhmEw
      cPwoqSIECnFMHj8j58/R0Nx6zy2Iudz5afLRcelBsZIk4bXb2NK4+MFauVKZ86k0R7JRtGIZ
      pwY+yURGqyeRSODz+ZBXaDw+pyr0K1ne29i5IvVXSmdrE4cPHmDPnvct+y+UpSeArqIZOrmi
      QipTot4NkslKe08PzbXue2ZBzNWdv/02npLsslnpqw/CVXlRUlVShSLf/7v/jWyx4/D6CNQ1
      0N7Rwaa+PrxeD5a7fF6+YRg8Mz/Ljs3t98Spz7u5ZI2RkVE2bFjehftLTgDZZOG+9z3Mfe9b
      znDWFsMweDUSQmry01bjW3I9NrOZltoA7Q+8s9pJVVXmzh7n+Kt7yZdUHF4/4XCUWDhEj8dL
      jc2G5Q6OFCfiMdxNfhxr5MUTy23bxi5efOF5NnzrW8ta79q4cluDLnd+mny0+TzLXr/ZbKal
      oY6Whror35t1yqjpCK+mMuRiUYySil0zaLE56HZ7aHQ4sF3nQlDTdQalMvffw+9zlmWZUiJC
      LBZf1idziwS4DsMwOBANYTT5aFqBzn8jkgRBt4ug+51pC7phkCmWOJ7KkE6l0IsKdtWgxmSh
      2+2mxenicCzChu71P+R5K/dv3sBzzz7LN//gm8tWp0iAdzEMg5cjIUzNPpq8q9f5b0SWJHwO
      +6IXUxuGQbZc5nwqw5FsFF+Ng5Z7aNTnRhZujJ2hWCxit9/+i7pvRtwIu4phGLwSCSE3+WhY
      A53/RiRJwmOz0VsfZHd3G72Ndbf+0D1i+4YO9u3fv2z1iQS4RL/0m19q8tG4iqc9y6GoKBwe
      myJTLHFmNkS+vDApr6AozKUyZIolork8oXSWgqIQzeZIFUsVjnppanxezp04jqZpy1KfSAAW
      Ov9vZ6fXZecHmEtnsZhkrGYTTquF+UwWwzAYicSZz2TJKwrzqSyJQoGJeIqxWJJodvUWni+3
      tqCX/uNvLUtdVZ8A+qWpA+724Kpe8C6nZp+HWK5AqrDwTi9F1ciXFdLFEnOpDDVOB7IEJkmm
      rKpYzWt3TfDt6Gpt5s3XXl2WFWNVfRGsG/Db2fXd+QHKqobXbsNqMhHK5Ois8aMbBg91t5PI
      F5hMpCiqGk0+D7phYDObiKzhhfG3w4nC6NgYPd1391Dfqk0AXdc5NjNFzzrv/AAeu40Huxbe
      aOl3Lp42HHA6CDivnUpcNK3vg//23m5efO45/v1d3hhb3/8LS6TrOo//7DFqKK77zl+tZFmm
      EA+TSCTurp5limfd0HWdxx97DHspSeMafDqacPt2bdnIb5/57V3VUVUJoOs6v37iCdxKhub6
      YKXDEe6S3WZlZmSIUrm85DqqJgEud357Mblqb0EUVt6OjR3se2nfkj9/VwmQic2x/9knOfH2
      AI//8Kck4nP8+lfPrLlXJGmadqXzBwNLn9UprD01Pi8DJ/rRdX1Jn7+rBFBUDX9dC07yuOrq
      GR0cpLWzi6HBQQYHB++m6mWjaRpP/OLnuMpp0fnvUe21S78xtuQE0JQ8B/a9TMkwo+gmGr0m
      LDY3nT09bNq0iU2bNi216mWz0Pl/gVfPExCjPfesrrZm3jjwypJujC35PoDJ4uRL3/iDpX58
      xamqyi9//nP8FAis4YltwvKw6yXGJybo6uy8o8/dkxfBmqbxg+9/H5+eF52/SuzctIEXnn32
      jj93zyWApmn88/e+R4tTpmYdPApcWB6yLJOLzpO6jXcjL/rcCsVTEZqm8f3vfld0/iq1e1sv
      Tz/99B195p5JAE3T+M4//RPtHrPo/FXKZrUyPTyIotz+22XuiQS43Pm7Anb84py/qu3s7eTF
      l1667e3/P+tybi9nDmdPAAAAAElFTkSuQmCC
    </thumbnail>
    <thumbnail height='51' name='KPI' width='192'>
      iVBORw0KGgoAAAANSUhEUgAAAMAAAAAzCAYAAADB2gewAAAACXBIWXMAABJ0AAASdAHeZh94
      AAALCklEQVR4nO2ce1BU5xmHnz27yALKsuICQmABiVyWGBRFE6wmjmlrxFw0GrVp0kwSTToa
      K01rajNt2mnNpK1jZ6pW4xhvbQ1xtBgCJJMWNVGxIDdFRLnfWYSw3JZ18Sz9Y/GCLGKTODae
      7/nr7Pd+tzP7/c77fpdzVP39/f0IBApFutsdEAjuJkIAAkUjBCBQNEIAAkUjBCBQNEIAAkUj
      BCBQNJqRMjy5/gOX6Yc3LBV2Yf/W21UjbYRdreBqAYHgXkKEQAJFM6IHEAjuZYQHECiaEQXw
      5PoPhp1ICATfdoQHECiaEZdBBYIRKX6P9/dlAxD+w108EnuX+/M/IASgIBxdFyj5JJWL5yux
      9NgBNaO8Axg7cQaRCYmEGvWo73Ynvw61B0jZkkHPtQRPol7ZwsMRwxcRAlAIly/uI213Fp3y
      jaky9s4Gmk8fpPl0I+p3VxB6l/r3TWApP3vD4AewUnexEiLChy0jBKAE5PPkpQwM/tGTSVyx
      gvv9tUiyjd6WSipzUzl76m538uvSRv35OgCk+NU8Im8lq1Cm50IJlsfD8Rmm1IgCEDvA9wAN
      xdR1Oy/1iYuJ9Nc6f6i1eIyPwfREDKZ5fdzoHOSOCsqzP+ZCQQUWSxdXANzG4BM8g7jFzxI+
      9jaDJWsZxakfcKakElsfSFp//B5azqOPTcJjUBV9dBQe4MRnJ2hpteIAJK0P3oGTCJ+9gElR
      4269YtNZRE2t8zJwQjR+lyOg8AI0F1FvScJnkAKaKNy0nvxmsQqkOOw93ThcGdzcBsX/dRm/
      48SRQlqvDn6Avi4slZ9xdMtOqq230Zg1n6MbN5BT5Bz8AA6bmeYjmzjw/jG6b8hqObqBf+7/
      jOaBwe/Ma8FS+Tn5mafoHOm+ys5gBiAYv2APPMOj0QFQTk2JZdhyt30YTniCbzFBMQR5ZHCx
      F3qO/4nUyz9g5mMP4adzG76Mm4GAxCSmPhyP7zgv1PTRkfNX0g8WYOvOpuDUAkLnjL9Fo72U
      H9hKZTegDiXutXXEBavpLdzF4f3Z2Mr3kV2QwGOTPYDzlBytdj71g55i/sokDO5qZKuZtou5
      nCsZNcIN9lJbXOy89HmAQD+AiYz3gI5eMF84h/3hRFzVIjyAElCbmPbMTDzVAHYsubv4eMNr
      /O2P7/LF8RJ6+oYWCX3mDzz+xCz8xnkNeAY3dAlJRAyEEu3mplu32ZnNhRJnUDVm1o+YEqxF
      wg2vuEVMCgGQaThTiB2AXuy9zmKasYF4uztbVHv64xeXxKPLvztsDA+AfJ76C862pNAJjHPe
      AQbjgL3sHI2y66JiEqwQ3GNfYskvZlKStp/C4hrssoy9tZSytFLKPgki5rmfkRClu/5ElDto
      zUsn54tcvmyzYB9mAA1LQxWXBi4N9xlvMPjiF+YLtW04GuuxAH6EExChprJcxn52K/t/bSRw
      ylSMpgcJNQbjfgtHBUDVOeoH+hc4MXrgHjzwDwuF0mqQi6ivkAmdOHTeIgSgIKQxkcQuf5tY
      2UZXfREVJ9I4U9TAlb4GSnZtRPvT3xLnB9BE8ba3yam1f/XGZPlaLF+570UqXeWxtOOcSvgw
      cenrWHZsocRsx2Grof5kDfUnD3LCzUDY0z9ndvzwk+DGs6cGPIkz/r+Kd3gMXlTTg5XqcxeZ
      OTF6SFkhACWi1jLGOJ0443SiY7dy4O+52KmjpqSFOD8/KP8XhbXOjTLv+Fd4NGkKvp5u3Lh6
      8k0jjZnEjOStxLdX01R2npqC49TUmLH3XaLqw+0YJvySWJdxUCX1pVdn5HXkb3yRfBe57KXF
      tBCN303pQgBKoKWMJo9wxo8ZGgK4+wXhQS52wOFwxhGW2rKBJ+oUpiyaju9X2R4eH4wv2bQB
      gYv+wvcTRt8icx9ynxtqNzVu+gmEJEwgJCGJxObDHN6USjvltNaDy4lASwkNwy/yXMdylsaW
      xfjdpACxD6AEWo6Q+Y8/MzZ2HpMfnUWgnzduapCt5ZxPy6IDAG/GhzlXdTx1Y4E64BzVZzsI
      jdPR31FB+bEUSi8N38wgfBO4P+wgbVUyjam/5/NRP2a6yRnPOy530l73H8oKRhG1eDY+nOWL
      3xyif84CTJNir6069Tabcc6N1UjDiNBSnEO7s9cujz04it9j775sHNRRVdxE3E0rV8IDKAXZ
      ypdFB/l30UEXRjXeM1YwOcz5a5RpOsHqIupkK9X7f8Lu/c50aVwMhjFgvZ0nLr5EPbWIis0f
      cqmvmfL9v6L85iwBi4i6et3XQNWn26j6dGhNUtBTPDDRVRsWGi86d3/xmEGoizM/UoSJQLKp
      B9ovnsc6ZzyeN9pHug3xPsA9QOxLLFv9KlOmRuHj7Xn9T3cbg0/4LBJWbmTh0ybcr6ZrH2LO
      quUEj3PmlbT+BH/vTZ5Nfo4g7e03KwXMY/66N0l4MBxP7fVHuMYriICpi5i9+Dt4A/Agicmv
      D+mfxiuIgMRXefq1JPSuPIDtHPVVA21Fmghw1QmtifsGhE3VGRpsg83ipXiBohEbYQJFIwQg
      UDRCAAJFIwQgUDTiu0ACRSO+DSrsiraLEEigaMQ+gEDRCA8gUDRCAAJFIwQgUDRiGVRwR3A4
      HOTn55OWlobZbEatVmMymVi2bBl6vR6Hw8HRo0fJyMigu7sbf39/nn/+eSZMmOCyvtLSUg4d
      OkRDQwOyLGMwGFi4cCFxcXGoVCpKS0vZvHkzfX3XX3DW6/WsW7cOvV5PY2Mje/bsoba2FkmS
      rvVFCEBwR6isrCQlJYUFCxYQExNDV1cX27dvZ/To0axcuZK8vDzS0tJ4+eWXCQoKIj09nZMn
      T7J27VoCAoae68zMzCQsLIyIiAgkSSI7O5vU1FRWr15NSEgIOTk5HDlyhDVr1qDVDj6y2tHR
      waZNm5g8eTLz58/Hbrezd+9ebDabOA4tuDMEBQXxxhtvEBsbiyRJ6HQ6IiMj6erqwm63k5+f
      z9SpUzEajWg0GubOnYu3tze5ubku65s3bx5RUVFoNBokSSIqKgqtVovV6nwd0mw2o9PpcHd3
      H1K2ra0Nh8NBYmIiGo0GT09Ppk2bRnNzs5gDCO4M7u7uuLld/5xDRUUF2dnZxMTEoNFo6O3t
      HZTfy8uLgIAAmppu/bkVh8NBTU0Ne/fuveYRAJqamvDy8kKlUg0pExgYiE6n49ixY9jtdqxW
      K7m5uU5BfQP3KhC4pL29nR07dmCxWFi+fDnvvPPOtQG6evVqDh06xKpVq7hy5QoGgwGVSoXR
      aLxlnSqVCm9vb/R6PRUVFbS3t2MwGFixYsW1PD09PWRkZJCXl0dycjJqtZquri50Oh2SJCFJ
      EhqNhtbWVuEBBHeGy5cvs2fPHgDWr19PbGzsoKezRqNhyZIlbN68mW3btvHWW2/h4+NDdPTQ
      T5fciEqlQq/Xs2TJEjw9PV2GTF5eXsTHxwPQ3d1NdXU1NpuN+Ph4NBoNWq2WOXPmCAEI7hxV
      VVU0NTWxdOlSRo8e+kUIs9lMTU0NDocDm83GRx99RGdnJyaTCYD6+nqSk5PJy8ujq6uL9PR0
      Wlpa6O/vx+FwUFpaSltbG0ajkY6ODg4fPnzNbrPZyMvLQ6vV4uvry9ixY5FlmYKCAq5cuYLN
      ZuP06dO4u7uLVSDBnSEnJ4edO3cOSQ8PD2fNmjU0NDSwe/duLl26NGSJFOD48eNkZmaydu1a
      fHx8yMrKIisrC4vF+Ua+wWAgKSmJhIQEZFkeZFepVISEhPDCCy8QGBgIQElJCSkpKZjN5kF2
      IQDB/x2yLLNt2zYiIyOZO3fuHW3rv7u9tSCp1r3WAAAAAElFTkSuQmCC
    </thumbnail>
    <thumbnail height='192' name='Sales&amp;Production Cost' width='192'>
      iVBORw0KGgoAAAANSUhEUgAAAMAAAADACAYAAABS3GwHAAAACXBIWXMAABJ0AAASdAHeZh94
      AAAWA0lEQVR4nO3deXAc53nn8W9Pzz2DwQADgLhBAAQJ3pdIiqSog5RtaZ1s1lVb8SYpJ3Fi
      J1VbtVVbtbWp1G7V1lZpk03iWmdVtpPNxqvLiaPD1mXSkiObkiiJlMRTlGhJvEDcxwAYDObu
      6e53/yCtXSaAJRLDwXD6+fynaUzjGYG/ebvf7qdfTSmlEMKh3ABmIYuyreWuRYiycwMo22I2
      MUc2l78lv0QGGVGpNKWUKuZS6B4fLre35L/AtkysQqbk+xWiFFzLXYAQy0kCIBxNAiAcTQIg
      HE0CIBxNAiAcTQIgHE0CIBxNAiAcTQIgHE0CIBxNAiAcTQIgHE0CIBxNAiAczX0zbypmk7xy
      6CCrdz9A2Jjk+OlztK/ZytjpV+jc/SW0yTMMF+p5cP/OUtcrREnd1Ahgax4C4SitMR+HDx/B
      MKE56qKohymk4lwenmbVmtUcPXqUt98+VuqahSiZGx4BlFUkPjnMzFyO7Mw4gYYuatwGVy5e
      pq2zjeGxacKRerqaI/S17ZGOMFHRbjgAtlkkMZti1Zpegg2drF+Vpa6tB5VP4ffq9Lj8GPk8
      Hl1OL0Tlk55g4WjyNS0cTQIgHE0CIBxNAiAcTQIgHE0CIBxNAiAcTQIgHE0CIBxNAiAcTQIg
      HE0CIBxNAiAcTQIgHE0CIBztpnqC4erCerbS0HUXlllEaS6wLTTdjaYUlq3weG5690KUxU2N
      APn5aZ77+8cYGE9SzM/zwhN/y3vnR3jxse/w/sUJTh1+gZ8cOVniUoUovZsKgDsQoa6hmfbG
      ACfePkW0pYvWcBF3pJn83ATjswU2bFwnTfGi4t14U3wxz0fvn2ZkIsHYwHnGxgaZnM7j8Xjo
      6GxmaDZNKBKlvSHMyiZpiheV7YZ7gq1igbHRUWx0Wto78FAkmTXRbAOvW8dyeTANg2i0FpCe
      YFHZpCleOJpMgwpHkwAIR5MACEeTAAhHkwAIR5MACEeTAAhHkwAIR5MACEeTAAhHkwAIR5MA
      CEeTAAhHkwAIR5MACEe7ua51pZgYuoQWXkHYnefMqbN0rdtGduICkfa1eI1pxpOwvr+7xOUK
      UVo3NQJkEpMcee0NNI+PdCpLNOxmZGiIsyfeZXI6wYk3jzBfsEtdqxAld1MBCNU1Ut/UQixg
      Mzx4BSvQQkekSF1bN2MDl7B8daxb0yVN8aLi3fAhkJmb55WXDhGfMzl78l0+ujBIa28A/DY9
      nc3EjSAqG6DG52bPHmmKF5VNeoKFo8kskHA0CYBwNAmAcDQJgHA0CYBwNAmAcDQJgHA0CYBw
      NAmAcDQJgHA0CYBwNAmAcDQJgHA0CYBwtEX6ARSFs+9QmNEJ39EH3jAun6z5K6rP4iOAppE/
      /gYzf/VX5AemyliSEOWzyNe6hqdnNd72y3jXbybQ13zdVjOX5JVDh+i78wu4k1c4fvI9eu44
      wOTpl+i880tok2cYKtTx4H07y/ARhLh5i48AVgG7CCo5hTGeuH4TbvyhWpqjOudHU9x713ay
      M8MYBMnPx7k0FGfV6j7pCRYVb9EDe81Xg7ephsLAMO5Vmz55XVlFpqfGmE3mScdHKBgWp88N
      UhcN09bVzvBYnHCknq7mWvrapCdYVLZFA6DMAsWpOK5gHS6f55PXbbPITDxB96qV1KzooTdZ
      pLV3L4XUDAGfm27Nj5HP4dFlgklUvkWb4u1MgszR05CdQl+/n+Cqppv6BTICiEq26Ne05vVj
      jV4gf2UWd7j0T4sQohIsfgiUnsWya/C12ChNL2dNQpTNoiOAK9qMrzWMMTyBnc2XsyYhymbR
      ANjZJMWpOQJ3fx5fR305axKibBY/B9AAPYBWnMeMJ8tYkhDls2gAlJHHnBilcOkKylLlrEmI
      sll8st4bxB0NgduPK+QrY0lClM+CAVDFDInvPoZtgzl6heLkXLnrEqIsFh4BdB+BnTsIbFyD
      0mrwr2opc1lClMeC1wE0l5vgnXtRxQIql8MYm8Xf2Vju2oS45Ra/EGZkMYYn8d+xF1c4VM6a
      hCibxWeBMglSB58l/hd/SWFMpkFFdVr8duhoM8HtW7ESc9jpHBAtY1lClMcvGQHmyJ35AOWL
      4W2LlbMmIcpm8SvBuo5WE6F49m0KAxPlrEmIslnwEEiZOZLPPI/LH4CV/fi6mxf6MQBmxwc4
      dvQEfdv3kRo8Q8PqXXjSwwyn3Ozatu6WFS5EKSw4AmjuALVf+QqBTevQVB5rLrXI2xWn3nkH
      fyiAmUtw5fIgibkEZ06dxhuqZWRkhNHR0VtYvhBLs/g5QHKKuSefwZzLo7kX7geYn7iMu76H
      YjaLlUnQ2LGS4ctX0EMxVnc3Y9s2tm3dsuKFWKqFD4GUAs2Nf30/VjqHlSku/GZfkNTUSbrW
      3YFXpemrr2PODmJmUgQ9OqHOTmmJFBVt4WlQM0fimZcI71jN/M/exV2/8IWwYF0Lv/qvf/26
      1+SmCXE7WeReIC+6p8Dci68R6G/HnJVv8NvVpUuXr47oYkELnwS73ES+/BvUf/330cMBcGnl
      rkuUwIsHD/Hv/8MfcfjV1/lvf/rnPPArv8ZPf3YYpRQP/cmf8Vu//VWOvf0uv/3Vr/GH//bf
      8fQPnuV3f/8PGBkdY8fufRQKheX+CLfc4j3B/jCe5kYCdz9IoLuhnDWJEolGozQ0xGhuXsF/
      +uP/yNYtm7l7313Ytk0mm+E///EfcfL0aQyjiGmagKK2tpbXXj/CmtWrccK4IU+vqmJm0aSj
      o4Mnn3qGM++dZcO6ddi2zcWLl0inM7xw8BA+r5fm5hX4fX7S6Qw77tjOKz87TEdH+3KXXxaL
      PhirVGQWaPnEp6f5/j88xe985beYnpmlrfXqFMXAlSvEYjHOn7/A3j27OXnqNH6/n4ZYDK/X
      y/jEBLqus7pvFbpe3Y/EkQAIR5NVLypQJpPlqW98k1pvdTyRr2nndvbdf2C5y1iQBKACGUaB
      xpNnuKOxOrrw3qmPQoUGQE6ChaNJAISjSQCEo0kAhKNJAISjSQCEo4xPTPDmW0exLIuLly7L
      NKhwDtM0+S//9SHq6+tpaWnhoT/5s6WPAEopjEKeYtGkaBQwLRvbMjGMhZtohFguuq6zaeNG
      0uk0+VwOv9+39BFg4NwJzl+ZoH3Ves6/8QI993yZ4uAxplwdfPGALJQtKkehUGBycpLRsTHG
      xifo7l65tABY+SRvv3MGdA/9/Xnctc3kEuNMzxbZtH8dR48eBWWxY+umT9+ZELeYz+fj61/7
      KvH4NFu3bKZ/zeqlBSCfSqAHo0RrAowNjdLZ0cJgIkMoEqUtFqKrURbKFpVD0zS6Ojvp6uwE
      oKOjfWkBCDV0ct89bkJ1TVhGFq/bRZ/mpVg0cEsXmSihF7//JLk3j5V8v0s7B9BcNLVea5wI
      /P93LvqXtFsh/ilrNsHdeaPk+5XrAMLRJADC0SQAwtEkAMLRJADC0SQAwtEkAMLRJADC0SQA
      VUwpxZVUipxpAmDaNpZtf7Ita5pYto1hWWRNE6UUBevqeg5F28ZywEN1JQBVLJ7P8/LIMB/O
      zWErxQuDg5yenQFgJJPh5PQ0r46P81FyjiMT43ycTPLtn5/Dsm2eG7zCB7Ozy/wJbj0JQBWL
      eL34dTemsvlwbo61dVF+cYdWeyiEX3fRHgrR5A8Qz+eJer30RiKcn08SdrvRHHA7lwSgihUt
      i85wiPFsDsO2OJ9McmF+HsOyOD0zQ8jtoTMcxrBtdjU2MZhO0xwIcjw+TU9NZLnLLwtpiaxi
      QY8HBexobKA1GGJjXT15yyJZLNIcDDKWyTCVy6G7NAqWxfaGBnKWRW+khlqPl+K184VqJgGo
      YrqmcaC17ZP/drtchF0uwh4PAK3B4CfbOq6tglXjclFzbbu3yp8MDRIAx8qaJm9MThB0u2ny
      +WkKBPDrOj5dx+WEg/9rShKAzHwCy+UHI407GMWtDFJ5k1hdbSl2L0pIKcWZ2VleS82wsbeD
      rG1zIpMlmZrHNEwwbTTTJqy5CLt0IrqHjlCIOp+Pep8PX5WNCksOQH5+ioNP/YB19/1Lzh9+
      hlX3fZn0x0fI1Pbz+X1bSlGjKJF5w+B7g5eJttTzud61uK517XUs8EVVtCzyRZOMYfDzTI5E
      JkF2roBZNPHa4LXBZ8MKf4D2UIjWYJCY//ZrhFpSAIrZJEfeOEldcyt1riT+hg6SE4Mk8zrb
      71ojTfEVwlKKN6cmOV5IsX/bGvzXjvF/GY+u49F1avw+miM11237xaqTtlLM5fJ8NJ/mSDpO
      ejqP27oaDN2yqXV7iHm8JEZXMDY2TigUJBQK4XZXzpH3kipJJWbQyTEwkcHvH6GzrYHhNATC
      EVZE/LTukab45TaWzfLs+DBtnU082NSGVoLj+1/sQ9c0YqEgsVCQ9Qv8XL5YJJkrkEmM872/
      fpiCaWMp8PgD+INhItEotdE62tvbaF6xgqamRmpqahbY062zpADUt/Wwv6WLoqkAC5em0a+5
      sG0bXZril5Vp2/x4dIRBj8W+rf143eU/dvd7PPg9HrTGGCvX9l+3zbJtCoZBPj/DxeOXOT6f
      Zj6TxVSgub2EI7W4fX4am1awsquTiak4hm2hay5cUJIgQwnOATSXztWVfCpnWHMypRTn55O8
      NDvFhlXtHKitzAtaustF0O8n6PdTH118siSVyXL55FsMjFzgW5NTULTw2OCxwaegweujweuj
      NRikxuMh7PHc0Im6/KutIulikWdHhihGA9y/9f+d5N7OakJBakJBPJk0TfXR67YZpkW6YJDI
      57mYyZJNGxQLReyihW7ZRHQ3AVys8PtZ4Q8Q8/uJ+XzXjR4SgCqglOLEzDSvpxPsXddDbeD2
      m425GV63Tr07QH0oQHes7rptSimKlk3BMklm85zO5kik42SnC+imjU9puCxbAnC7m87neXZs
      mPCKKA/2rS3ZsfHtTtM0vG4dr1unxuejfZFrUhKA25RSip+Oj/EhBXZv7CXkq44lVctNAnCb
      UUoxlsvyw/ERVnY1c6CxU771b0DOKJLI5WmJhMkXTQnA7aRgWbw4Msx00MW929fidsnd7DdC
      KcUblwZxuTTCPi+XZxISgNvFB4lZ/nEuzuZVnayvLe/FompSHwoQT2ev3iqulASg0qUMg2dG
      hnA1hPnCtnVyuLMECogG/ExnsgwlknREayUAlUoBb0xOcKKQYtf6bqIOmdq8lTTA53bTGqmh
      oy7CwMycBKASzadS/P3wAHetX8Xnm/vlW79ENE2jo672k7tfVzfFJACVxDAMDh48xKm3XudX
      d2+mJRRa7pKqnkwjVIiLly7zpw89hDE5yP13bsWty5+mHGQEWGa5XI4nnvgeiZEB9m9dj8ft
      plAoLHdZjiEBWCZKKU6cPMXzTz/Jng19bNqxeblLciQJwDKYnU3w6COP4DXS/Is926rirs3b
      1ZIDMDt+hWNHj9O3fR/pofeIrd6FNz3M8LybndvWlqDE6qGU4vDh13j15YPs27qOSLjt098k
      bqklB8DlCdLb3Uo2GWfg0hVcjX2Mnz1F8+b7GR8fR1kmjfWV2ZRRTsMjIzz+yCN01Ab44r4d
      y12OuGZpTfG5ec69/wGtvWsJzA/S0LGS4YEreEMx+nqaiY8OgW2VqtbbkmEYPPvs81x8/yR3
      bVmPT+7arChLCsDE4EXGJyYpuMO01QXo644wZwcxMylCHp1wd7djm+KVUnz00cd8/4nH2dLT
      yv6dW+SCVgVaUgA6+rfR0b/tutdal1ROdUgm53n6qaeYHbnMA7s24pK7NiuWzAKV2LFjb/Pj
      559l59oeNtwhU5uVTgJQAkoppmdm+N7jj+MvZnhgz1ZHPV/zdiYBWCLLsnj55Z9w7NWfct+O
      TQQDMrV5O5EALMHg4BBPPPoIK2Nhvnj3zuUuR9wECcBNyOfzPP30Dxj++H3u2b4RTwU961Lc
      GPnL3aCTp07z4g+fYdPKFvbv2rrc5YglkgB8RtlslkcffQxjdpzP7diALlObVUEC8CmUUvzs
      8Ku8+tK1+3fapS+3mkgAfompqTiPP/ootXqRX5GT3KokAViAaZo89/yLnDt+lLu2bSAkDelV
      q6oCYJomCm56VkYpxcWLl/i7Jx5jfXsjX9h7R2kLFBWnagJg2zb/63//H/L5PH/49d/jb777
      CAfuu5etWzaTyWT4m799hLv37aV/zWqefPoH3HvP3bz8k1f4zd/4dd49foKA38/5j88zNzrA
      ga39MrXpEEv+K9uWwaWPL9DS3Udqagh/fRs+O0N83qSro7kUNX4m2WyWN956i6//3u/y3As/
      Yv3afp74u++zedNGXjz4Y/rXrObhb32HWKyeyckpxsYneO31I3R1dvAPTz/Dhx+8zwP7dnHP
      zq28e+YcoWAAj8dNLFpLXaSGt06dZf9uGRGqzdLm8pTFm//4Mon4MMdPfcBbPznE0PgM7xx+
      iSvjiRKV+Nn4/X5+8998me8+8jjxeJz2tjY8bjdKKQqFAq2tLbhcLs6fv0ggEMC2LRpi9fz3
      P/8LCnPT3LNjC+t6V1I0Tbo7WklncxRNi6HxSYbHJ8kXjLJ+HlEeSxoBsrOjqEgn9YF5rGyK
      cMtKpkcGyBNm59qeUtX4meRyeX508BCNjQ186V/9Gt/4H3/J5w7s54fPvcDePbt5+Fvf4XP3
      H8AwDH7+4YeMjY0zPTFGb2sje7dtYmhsAjQN1NX1q0zTxKVpaMBcKk00Ei7r5xHlsaQAKKW4
      cu4Eqn8jQTJ0tdYzng8RDBvEwuXtfKqpCfM/v/kNNCAUCvHX3374uu3ffvibHD32Nj869GOy
      83M0+d3037ubLWtXky8YmJbF1MwstTVhBkcnCIeCRCNhgn4/8dkEkbA8pKoaLSkAoYYufucP
      vnbdazfTBj8xMUlyPrmUUj6TgYEBUuPDfGHPdrzeq2vlFgp5NKC3o+WTn9vQt/K697U01gMw
      l/z0GjU0ait0YTrxz2lKKVXMpUjMJcnkSv9AJsuyPvVBT5NTcVKpVMl/9z/l1l0EbnFPrqZp
      S17rVimFlcuhV8kVZ0vXcXuX9v/dNAx0q/T95Z8EAGWXfOcA2VyekYnpW7JvIZZK7ugSjlal
      V3sUwxd+ztDQCJ3rtpMcvchMqsiunZv44NQZerfeSW5qgMsDQ/Ru3k1LQ/WvuDI3OczFi5fw
      1rYTcWcZGp5gw869xAfex13XTVu9l/dOnWX9nXcR9pV/Vfnyszl/5mR1jgBKQWxFK3WxJnLx
      IRp7NxIJesDlxhMIEXLliKcUq3vaMG213OWWRSgaIxaLEXIb5PUoXa0xTBuU0mloqkdze/CG
      wgS9VflP4p9RCpraO6ozAMnJId4/d57mlavIpdPk58bwRxpIJaYxbJ356QmUpjE6naM5Vv3z
      +8VsklPvnsATWYFfK4IymMm58JNjPlPAo4rMxSewXb7lLrVMFCMXzjEwNFmdATCVRn20BqNg
      0LNmDZoepHdlM3PJDNFoDZGmTiJ+F2s2bsLjgOfwF4tF6hsaMAoFGjt78Lo01m/awNx0nLpY
      PflslvmMQW1NcLlLLQ+lcHn8hAMe/i+PmorFMMudgAAAAABJRU5ErkJggg==
    </thumbnail>
  </thumbnails>
</workbook>
