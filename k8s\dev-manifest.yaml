apiVersion: apps/v1
kind: Deployment
metadata:
  name: dev-biport-api
  namespace: dev-biport
  labels:
    app: dev-biport-api
    version: v1
spec: 
  replicas: 1
  selector:
    matchLabels:
      app: dev-biport-api
  template:
    metadata:
      labels:
        app: dev-biport-api
        version: v1
    spec:
      nodeSelector: 
        eks.amazonaws.com/nodegroup: "production-node-group" 
      containers:
      - name: dev-biport-api
        image: 211125524251.dkr.ecr.ap-south-1.amazonaws.com/dev-biport:backend-latest
        imagePullPolicy: Always
        ports:
        - containerPort: 9090
        envFrom:
          - configMapRef:
              name: dev-biport

---
apiVersion: v1
kind: Service
metadata:
  name: dev-biport-api
  namespace: dev-biport
spec:   
  selector:
    app: dev-biport-api
  ports:
    - name: http
      protocol: TCP
      port: 9090
      targetPort: 9090
