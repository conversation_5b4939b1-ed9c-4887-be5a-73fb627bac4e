import os
import re
import json
import xml.etree.ElementTree as ET
from app.core import logger
from app.core.enums import General<PERSON>eys as GS, WorkSheet as WS
from app.core.regex_enums import Regex as RE

def extract_columns_mapping(root):
    logger.info("Extracting columns mapping.")
    columns_mapping = {}
    for map_tag in root.findall(WS.COLS_MAP.value):
        key = map_tag.get(GS.KEY.value)
        value = map_tag.get(GS.VALUE.value)
        if key and value:
            columns_mapping[key] = value
    logger.info("Columns mapping extraction completed.")
    return columns_mapping


def clean_column_name(column_name):
    logger.debug(f"Cleaning column name: {column_name}")
    return re.sub(RE.BETWEEN_SQUARE_BRACKETS_FOLLOWED_BY_DOT.value, RE.EMPTY.value, column_name)


def get_column_name(column_name):
    logger.debug(f"Getting column name: {column_name}")
    matches = re.findall(RE.COLUMNS_IN_WS.value, column_name)
    columns = []

    for match in matches:
        data = match.strip(RE.SQUARE_BRACKETS.value).split(RE.COLON.value)
        if len(data) == 1:
            columns.append(data[0])
        elif len(data) == 2 or len(data) == 3:
            columns.append(data[1])
        else:
            columns.append(match)
    
    return columns


def extract_worksheet_details(root):
    logger.info("Extracting worksheet details.")
    worksheets = []
    columns_mapping = extract_columns_mapping(root)

    for worksheet in root.findall(WS.WORKSHEETS.value):
        worksheet_data = {
            GS.NAME.value: worksheet.get(GS.NAME.value),
            GS.COLUMN_INSTANCES.value: [],
            GS.FILTERS.value: [],
            GS.SLICES.value: [],
            GS.STYLES.value: [],
            GS.PANES.value: [],
            GS.ROWS.value: None,
            GS.COLS.value: None
        }
        logger.debug(f"Processing worksheet: {worksheet_data[GS.NAME.value]}")
        
        # Process column instances
        for column_instance in worksheet.findall(WS.COLUMN_INSTANCE.value):
            column_name = column_instance.get(GS.COLUMN.value)
            if column_name:
                mapped_column_name = columns_mapping.get(column_name, column_name)
                column_instance_data = {key: value for key, value in column_instance.attrib.items()}
                column_instance_data[GS.COLUMN.value] = mapped_column_name
                worksheet_data[GS.COLUMN_INSTANCES.value].append(column_instance_data)
        
        # Process filters
        for filter_tag in worksheet.findall(WS.FILTER.value):
            filter_data = {}
            for key, value in filter_tag.attrib.items():
                if key == GS.COLUMN.value:
                    value = clean_column_name(value)
                filter_data[key] = value
            
            groupfilter = filter_tag.find(GS.GROUP_FILTER.value)
            if groupfilter is not None:
                filter_data[GS.GROUP_FILTER.value] = {k: v for k, v in groupfilter.attrib.items()}
            
            worksheet_data[GS.FILTERS.value].append(filter_data)
        
        # Process slices
        for slice_tag in worksheet.findall(WS.SLICES_COLUMN.value):
            slice_value = slice_tag.text.strip() if slice_tag.text else RE.EMPTY.value
            cleaned_slice_value = clean_column_name(slice_value)
            worksheet_data[GS.SLICES.value].append(cleaned_slice_value)
        
        # Process styles
        styles = worksheet.findall(WS.STYLE_RULE_FORMAT.value)
        style_data = {style.get(GS.ATTR.value): style.get(GS.VALUE.value) for style in styles}
        worksheet_data[GS.STYLES.value].append(style_data)
        
        # Process panes
        for pane in worksheet.findall(WS.PANE.value):
            pane_data = {key: value for key, value in pane.attrib.items()}
            mark = pane.find(GS.MARK.value)
            pane_data[GS.MARK.value] = mark.attrib if mark is not None else {}
            encodings = pane.findall(WS.ENCODINGS_IN_ALL.value)
            pane_data[GS.ENCODINGS.value] = [
                {
                    key: clean_column_name(value) if key == GS.COLUMN.value else value
                    for key, value in encoding.attrib.items()
                } for encoding in encodings
            ]
            pane_styles = pane.findall(WS.STYLE_RULE_FORMAT.value)
            pane_style_data = {style.get(GS.ATTR.value): style.get(GS.VALUE.value) for style in pane_styles}
            pane_data[GS.STYLE.value] = pane_style_data
            worksheet_data[GS.PANES.value].append(pane_data)
        
        # Process rows and columns
        for row in worksheet.findall(WS.ROWS.value):
            row_value = row.text.strip() if row.text else RE.EMPTY.value
            worksheet_data[GS.ROWS.value] = get_column_name(row_value)

        for col in worksheet.findall(WS.COLS.value):
            col_value = col.text.strip() if col.text else RE.EMPTY.value
            worksheet_data[GS.COLS.value] = get_column_name(col_value)
        
        worksheets.append(worksheet_data)
    
    logger.info("Worksheet details extraction completed.")
    return {"Worksheets":worksheets}


def extract_semantic_model(xml_file_path, output_file_path):
    try:
        logger.info(f"Processing file: {xml_file_path}")
        
        tree = ET.parse(xml_file_path)
        root = tree.getroot()

        worksheets = extract_worksheet_details(root)

        data = {
            WS.WORKSHEETS.value: worksheets
        }

        with open(output_file_path, 'w') as f:
            json.dump(data, f, indent=4)

        logger.info(f"Successfully processed {xml_file_path} and saved output to {output_file_path}")

    except ET.ParseError as e:
        logger.error(f"Error parsing XML file {xml_file_path}: {e}")
    except FileNotFoundError as e:
        logger.error(f"File not found: {e}")
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}")


def process_worksheet(input_dir, output_dir, file_extension=".txt"):
    logger.info("Starting worksheet processing.")
    if not os.path.exists(input_dir):
        logger.error(f"Input directory '{input_dir}' does not exist.")
        return {"error": f"Directory '{input_dir}' does not exist."}
    
    if not os.path.exists(output_dir):
        logger.error(f"Output directory '{output_dir}' does not exist.")
        return {"error": f"Directory '{output_dir}' does not exist."}

    for filename in os.listdir(input_dir):
        if filename.endswith(file_extension):
            folder_name = os.path.splitext(filename)[0]
            folder_path = os.path.join(output_dir, folder_name)
            os.makedirs(folder_path, exist_ok=True)

            output_file_path = os.path.join(folder_path, "worksheet.json")
            xml_file_path = os.path.join(input_dir, filename)

            extract_semantic_model(xml_file_path, output_file_path)

    logger.info("Worksheet processing completed.")
