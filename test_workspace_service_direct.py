#!/usr/bin/env python3
"""
Test the workspace service directly to isolate the issue
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from app.services.workspace.workspace_service import WorkspaceService
    from app.models.users import UserManager
    from app.core.session import scoped_context
    print("✅ All imports successful")
except Exception as e:
    print(f"❌ Import error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

class MockUser:
    """Mock user object"""
    def __init__(self, user_id):
        self.id = user_id

def test_user_manager():
    """Test UserManager methods"""
    print("🧪 Testing UserManager")
    print("=" * 40)
    
    try:
        with scoped_context() as session:
            # Test getting user by email
            user = UserManager.get_user_by_email("<EMAIL>", load_role=True)
            if user:
                print(f"✅ Found user: {user.name} ({user.email})")
                print(f"   Role: {user.role.name.value if user.role else 'No role'}")
                print(f"   Organization: {user.organization_id}")
                
                # Test get_user_by_id_with_relations
                session_user = UserManager.get_user_by_id_with_relations(str(user.id), session)
                if session_user:
                    print(f"✅ Session user loaded: {session_user.email}")
                    print(f"   Role: {session_user.role.name.value if session_user.role else 'No role'}")
                else:
                    print(f"❌ Could not load session user")
                    
                return user.id
            else:
                print(f"❌ User not found")
                return None
                
    except Exception as e:
        print(f"❌ Error in UserManager test: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_workspace_service_direct(user_id):
    """Test workspace service directly"""
    print(f"\n🧪 Testing WorkspaceService directly")
    print("=" * 40)
    
    if not user_id:
        print("❌ No user ID provided")
        return
    
    try:
        service = WorkspaceService()
        mock_user = MockUser(user_id)
        
        print(f"Calling get_reports_by_user_role with user ID: {user_id}")
        response = service.get_reports_by_user_role(mock_user)
        
        print(f"Response success: {response.success}")
        print(f"Response error: {response.error}")
        if response.success:
            print(f"Response data count: {len(response.data) if response.data else 0}")
            if response.data:
                print(f"Sample report: {response.data[0]}")
        
    except Exception as e:
        print(f"❌ Error in WorkspaceService test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🔍 Direct Workspace Service Testing")
    print("=" * 50)
    
    user_id = test_user_manager()
    test_workspace_service_direct(user_id)
