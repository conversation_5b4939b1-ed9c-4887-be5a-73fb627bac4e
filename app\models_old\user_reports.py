from datetime import datetime
from sqlalchemy import Column, DateTime, String, Integer, UUID, ForeignKey, func
from uuid import uuid4
from app.core import Base, scoped_context
from .organization import OrganizationDetailsManager, OrganizationDetails
from app.core.exceptions import BadRequestError


class UserReports(Base):
    __tablename__ = "user_reports"
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    user_email = Column(String(50), nullable=False)
    organization_id = Column(UUID(as_uuid=True), ForeignKey('organization_details.id'))

    migrated_files = Column(Integer, default=0)
    analyzed_files = Column(Integer, default=0)
    dax_files = Column(Integer, default=0)

    report_count = Column(Integer, default=0)
    project_count = Column(Integer, default=0)
    dashboard_count = Column(Integer, default=0)
    worksheet_count = Column(Integer, default=0)
    calc_count = Column(Integer, default=0)
    datasource_count = Column(Integer, default=0)

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class UserReportsManager:
    def __init__(self, user_email, organization_name, allowed_reports):
        self.user_email = user_email
        self.organization_name = organization_name
        self.allowed_reports = allowed_reports
        self.organization = OrganizationDetailsManager.get_or_create(organization_name, allowed_reports)

    def _get_user_report(self, session):
        """Fetch the UserReports entry for this user and organization."""

        return session.query(UserReports).filter_by(
            user_email=self.user_email,
            organization_id=self.organization.id
        ).first()

    def add_or_update(self, migrated_files=0, dax_files=0, analyzed_files=0):
        """Add or update user reports."""

        with scoped_context() as session:
            user_report = self._get_user_report(session)

            if user_report:
                user_report.migrated_files += migrated_files
                user_report.dax_files += dax_files
                user_report.analyzed_files += analyzed_files
            else:
                user_report = UserReports(
                    user_email=self.user_email,
                    organization_id=self.organization.id,
                    migrated_files=migrated_files,
                    dax_files=dax_files,
                    analyzed_files=analyzed_files
                )
                session.add(user_report)

            session.commit()
    @staticmethod
    def add_or_update_user_reports_details(
        num_datasources=0,
        num_worksheets=0,
        num_dashboards=0,
        num_calculated_fields=0,
        user=None
    ):
        """Add or update user reports with file counts and Tableau metadata counts."""

        with scoped_context() as session:
            email = user.email
            organization_name = user.organization_name


            organization = session.query(OrganizationDetails).filter_by(
                organization_name=organization_name
            ).first()

            user_report = session.query(UserReports).filter_by(
                user_email=email).first()

            if user_report:
                if user_report.datasource_count is None:
                    if num_datasources != 0:
                        user_report.datasource_count = 0 + num_datasources
                else:
                    user_report.datasource_count += num_datasources

                if user_report.worksheet_count is None:
                    if num_worksheets != 0:
                        user_report.worksheet_count = 0 + num_worksheets
                else:
                    user_report.worksheet_count += num_worksheets

                if user_report.dashboard_count is None:
                    if num_dashboards != 0:
                        user_report.dashboard_count = 0 + num_dashboards
                else:
                    user_report.dashboard_count += num_dashboards

                if user_report.calc_count is None:
                    if num_calculated_fields != 0:
                        user_report.calc_count = 0 + num_calculated_fields
                else:
                    user_report.calc_count += num_calculated_fields

                user_report.organization_id = organization.id

            else:
                user_report = UserReports(
                    user_email=email,
                    organization_id=organization.id,
                    datasource_count=num_datasources,
                    worksheet_count=num_worksheets,
                    dashboard_count=num_dashboards,
                    calc_count=num_calculated_fields
                )
                session.add(user_report)
            session.commit()

    @staticmethod
    def calculate_remaining_reports(org_id, org_allowed_reports, report_type):
        """Calculate the remaining allowed reports for an organization."""

        with scoped_context() as session:
            total_files = (
                session.query(func.sum(getattr(UserReports, report_type)))
                .filter_by(organization_id=org_id)
                .group_by(UserReports.organization_id)
                .scalar() or 0
            )
            remaining_files = org_allowed_reports - total_files
            return max(remaining_files, 0)  

    @staticmethod
    def get_user_reports_details(user_id: int):
        """Fetches projects associated with a user."""
        with scoped_context() as session:
            project_counts = session.query(UserReports).all()
            # If you want to filter by user_id, uncomment the line below
            # project_counts = session.query(UserReports).filter(UserReports.user_id == user_id).all()
            if not project_counts:
                raise BadRequestError("No reports for this user.")
            
            return project_counts
