#!/usr/bin/env python3
"""
Script to verify project and report data specifically
"""
import psycopg2
import sys

# Database connection parameters
DB_CONFIG = {
    'host': 'localhost',
    'port': '5432',
    'user': 'postgres',
    'password': 'postgres',
    'database': 'test'
}

def get_connection():
    """Get database connection"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except psycopg2.Error as e:
        print(f"Error connecting to database: {e}")
        return None

def verify_project_report_data():
    """Verify project and report data"""
    conn = get_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        print("=== PROJECT AND REPORT DATA VERIFICATION ===\n")
        
        # Project Details
        print("📁 PROJECT DETAILS:")
        cursor.execute("SELECT COUNT(*) FROM biport_dev.project_details")
        project_count = cursor.fetchone()[0]
        print(f"Total Projects: {project_count}")
        
        # Show sample projects
        cursor.execute("""
            SELECT p.id, p.name, p.is_upload, u.name as user_name, a.name as assigned_to_name
            FROM biport_dev.project_details p
            LEFT JOIN biport_dev.users u ON p.user_id = u.id
            LEFT JOIN biport_dev.users a ON p.assigned_to = a.id
            LIMIT 5
        """)
        projects = cursor.fetchall()
        print("\nSample Projects:")
        for project in projects:
            upload_status = "Upload" if project[2] else "Regular"
            print(f"  - {project[1]} ({upload_status}) - User: {project[3]}, Assigned: {project[4]}")
        
        # Project upload statistics
        cursor.execute("SELECT is_upload, COUNT(*) FROM biport_dev.project_details GROUP BY is_upload")
        upload_stats = cursor.fetchall()
        print("\nProject Types:")
        for stat in upload_stats:
            project_type = "Upload Projects" if stat[0] else "Regular Projects"
            print(f"  - {project_type}: {stat[1]}")
        
        # Report Details
        print(f"\n📊 REPORT DETAILS:")
        cursor.execute("SELECT COUNT(*) FROM biport_dev.report_details")
        report_count = cursor.fetchone()[0]
        print(f"Total Reports: {report_count}")
        
        # Show sample reports
        cursor.execute("""
            SELECT r.id, r.name, r.semantic_type, r.view_count, p.name as project_name
            FROM biport_dev.report_details r
            LEFT JOIN biport_dev.project_details p ON r.project_id = p.id
            LIMIT 5
        """)
        reports = cursor.fetchall()
        print("\nSample Reports:")
        for report in reports:
            print(f"  - {report[1]} ({report[2]}) - Views: {report[3]}, Project: {report[4]}")
        
        # Report status statistics
        cursor.execute("""
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN is_analyzed THEN 1 ELSE 0 END) as analyzed,
                SUM(CASE WHEN is_converted THEN 1 ELSE 0 END) as converted,
                SUM(CASE WHEN is_migrated THEN 1 ELSE 0 END) as migrated,
                SUM(CASE WHEN deployed THEN 1 ELSE 0 END) as deployed
            FROM biport_dev.report_details
        """)
        stats = cursor.fetchone()
        print(f"\nReport Status Statistics:")
        print(f"  - Total Reports: {stats[0]}")
        print(f"  - Analyzed: {stats[1]}")
        print(f"  - Converted: {stats[2]}")
        print(f"  - Migrated: {stats[3]}")
        print(f"  - Deployed: {stats[4]}")
        
        # Semantic type distribution
        cursor.execute("""
            SELECT semantic_type, COUNT(*) 
            FROM biport_dev.report_details 
            WHERE semantic_type IS NOT NULL
            GROUP BY semantic_type
            ORDER BY COUNT(*) DESC
        """)
        semantic_types = cursor.fetchall()
        print(f"\nSemantic Type Distribution:")
        for sem_type in semantic_types:
            print(f"  - {sem_type[0]}: {sem_type[1]} reports")
        
        # Project-Report relationship
        cursor.execute("""
            SELECT p.name, COUNT(r.id) as report_count
            FROM biport_dev.project_details p
            LEFT JOIN biport_dev.report_details r ON p.id = r.project_id
            GROUP BY p.id, p.name
            HAVING COUNT(r.id) > 0
            ORDER BY COUNT(r.id) DESC
            LIMIT 5
        """)
        project_reports = cursor.fetchall()
        print(f"\nProjects with Most Reports:")
        for proj_rep in project_reports:
            print(f"  - {proj_rep[0]}: {proj_rep[1]} reports")
        
        # Foreign key integrity check
        cursor.execute("""
            SELECT COUNT(*) 
            FROM biport_dev.report_details r
            LEFT JOIN biport_dev.project_details p ON r.project_id = p.id
            WHERE p.id IS NULL
        """)
        orphaned_reports = cursor.fetchone()[0]
        
        cursor.execute("""
            SELECT COUNT(*) 
            FROM biport_dev.project_details p
            LEFT JOIN biport_dev.users u ON p.user_id = u.id
            WHERE u.id IS NULL AND p.user_id IS NOT NULL
        """)
        orphaned_projects = cursor.fetchone()[0]
        
        print(f"\n🔗 FOREIGN KEY INTEGRITY:")
        print(f"  - Orphaned Reports (no project): {orphaned_reports}")
        print(f"  - Orphaned Projects (no user): {orphaned_projects}")
        
        if orphaned_reports == 0 and orphaned_projects == 0:
            print("  ✅ All foreign key relationships are valid!")
        
        print(f"\n=== VERIFICATION COMPLETE ===")
        print(f"✅ Project data: {project_count} records imported successfully")
        print(f"✅ Report data: {report_count} records imported successfully")
        print(f"✅ All relationships properly maintained")
        
        return True
        
    except Exception as e:
        print(f"Error during verification: {e}")
        return False
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    if verify_project_report_data():
        print(f"\n🎉 Project and Report data verification completed successfully!")
    else:
        print(f"\n❌ Project and Report data verification failed!")
        sys.exit(1)
