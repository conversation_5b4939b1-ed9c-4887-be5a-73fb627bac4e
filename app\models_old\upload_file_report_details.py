import uuid
from sqlalchemy import <PERSON>um<PERSON>, <PERSON>, <PERSON><PERSON>an, Integer, text, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from app.core import Base, scoped_context, logger


class UploadFilesReportDetails(Base):
    """
    SQLAlchemy ORM model representing the details of a report.

    Attributes:
        id (str): Primary key, a UUID string uniquely identifying each report.
        report_name (str): The name of the report.
        is_analised (bool): Flag indicating whether the report has been analyzed.
        is_converted (bool): Flag indicating whether the report has been converted.
        is_migrated (bool): Flag indicating whether the report has been migrated.
        serverid (str): Foreign key linking to the associated server.
        server (ServerDetails): Relationship to the ServerDetails model.
    """
    __tablename__ = "upload_files_report_details"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), index=True)
    folder_id = Column(UUID(as_uuid=True), ForeignKey("folders.id"), nullable=False)
    is_analyzed = Column(Boolean, default=False)
    is_converted = Column(Boolean, default=False)
    is_migrated = Column(Boolean, default=False)
    upload_file_path = Column(String, nullable=False)
    dashboard_count = Column(Integer, nullable=False, server_default=text('0'))
    worksheet_count = Column(Integer,nullable=False, server_default=text('0'))
    calc_count = Column(Integer, nullable=False, server_default=text('0'))
    datasource_count = Column(Integer, nullable=False, server_default=text('0'))


class UploadFilesReportManager:
 
    @staticmethod
    def get_by_s3_path(upload_file_path: str) -> UploadFilesReportDetails | None:
        """
        Retrieve an UploadFilesReportDetails record by its S3 upload file path.
 
        Parameters
        ----------
        upload_file_path (str): The S3 path of the file to retrieve.
 
        Returns
        -------
        UploadFilesReportDetails: The UploadFilesReportDetails instance if found, otherwise None.
        """
        try:
            with scoped_context() as session:
                return session.query(UploadFilesReportDetails).filter(
                    UploadFilesReportDetails.upload_file_path == upload_file_path
                ).first()
        except Exception as e:
            logger.exception(f"File with S3 path '{upload_file_path}' not found or database error: {e}")
            return None
    
    @staticmethod
    def mark_analysed(upload_file_path: str) -> None:
        """
        Mark an UploadFilesReportDetails record as analyzed by setting its `is_analyzed` flag to True.
 
        Parameters
        ----------
        upload_file_path (str): The S3 path of the file to update.
        """
        try:
            with scoped_context() as session:
                db_report = session.query(UploadFilesReportDetails).filter(
                    UploadFilesReportDetails.upload_file_path == upload_file_path
                ).first()
                if db_report:
                    db_report.is_analyzed = True
                    session.commit()
        except Exception as e:
            logger.exception(f"Failed to mark S3 path '{upload_file_path}' as analyzed: {e}")

    @staticmethod
    def mark_update_counts(upload_file_path: str, dashboard_count: int, calc_count: int, datasource_count: int, worksheet_count: int ) -> None:
        """
        Update the dashboard_count for the given workbook_id in the database.

        Parameters
        ----------
        workbook_id : str
            The ID of the workbook to update.

        count : int
            The number of dashboards to set.
        """
        try:
            with scoped_context() as session:
                db_report = session.query(UploadFilesReportDetails).filter(
                    UploadFilesReportDetails.upload_file_path == upload_file_path
                ).first()
                if db_report:
                    db_report.dashboard_count = dashboard_count
                    db_report.calc_count = calc_count
                    db_report.datasource_count = datasource_count
                    db_report.worksheet_count = worksheet_count

                    session.commit()

        except Exception as e:
            logger.exception(f"Failed to update count values for {upload_file_path}: {e}")
    
 
    @staticmethod
    def mark_converted(upload_file_path: str) -> None:
        """
        Mark an UploadFilesReportDetails record as converted by setting its `is_converted` flag to True.
 
        Parameters
        ----------
        upload_file_path (str): The S3 path of the file to update.
        """
        try:
            with scoped_context() as session:
                db_report = session.query(UploadFilesReportDetails).filter(
                    UploadFilesReportDetails.upload_file_path == upload_file_path
                ).first()
                if db_report:
                    db_report.is_converted = True
                    session.commit()
        except Exception as e:
            logger.exception(f"Failed to mark S3 path '{upload_file_path}' as converted: {e}")
    
    @staticmethod
    def mark_migrated(upload_file_path: str) -> None:
        """
        Mark an UploadFilesReportDetails record as migrated by setting its `is_migrated` flag to True.
 
        Parameters
        ----------
        upload_file_path (str): The S3 path of the file to update.
        """
        try:
            with scoped_context() as session:
                db_report = session.query(UploadFilesReportDetails).filter(
                    UploadFilesReportDetails.upload_file_path == upload_file_path
                ).first()
                if db_report:
                    db_report.is_migrated = True
                    session.commit()
        except Exception as e:
            logger.exception(f"Failed to mark S3 path '{upload_file_path}' as migrated: {e}")

    @staticmethod
    def get_upload_file_reports_details(user_id: int):
        """
        Retrieve an UploadFilesReportDetails record by its S3 upload file path.
        Parameters
        ----------
        upload_file_path (str): The S3 path of the file to retrieve.
        Returns
        -------
        UploadFilesReportDetails: The UploadFilesReportDetails instance if found.
        """
        with scoped_context() as session:
            upload_files_count = session.query(UploadFilesReportDetails.dashboard_count,
                                    UploadFilesReportDetails.worksheet_count).all()
        return upload_files_count

    @staticmethod
    def delete_by_folder_id(folder_id: str, db) -> None:
        """
        Delete UploadFilesReportDetails records by folder ID.
        Parameters
        ----------
        folder_id (str): The ID of the folder whose records to delete.
        """
        try:
            with scoped_context() as session:   
                session.query(UploadFilesReportDetails).filter(
                    UploadFilesReportDetails.folder_id == folder_id
                ).delete()
                session.commit()
        except Exception as e:
            logger.exception(f"Failed to delete records for folder ID '{folder_id}': {e}")
