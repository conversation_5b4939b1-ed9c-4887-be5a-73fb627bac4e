import xml.etree.ElementTree as ET
from app.core.enums import TableauXMLTags
from app.services.migrate.Tableau_Analyzer.report import convert_xml_to_dict
from ...core import get_tables_data, find_zones_by_name, calculate_dimensions, get_border_config
from ...visuals.text_box import get_text_box_json_only_in_dashboards
from ...visuals import get_title_textbox, get_slicer_report
from .worksheet import process_worksheets
from app.core import logger


def get_worksheet_names(worksheets):
    """
    Extracts worksheet names from a list of XML <worksheet> elements.

    Args:
        worksheets (list): List of ElementTree <worksheet> elements.

    Returns:
        list: List of worksheet names.
    """
    worksheet_names = [worksheet.attrib.get("name") for worksheet in worksheets]
    return worksheet_names



def process_dashboard_data(root, chart_types):
    """
    Parses Tableau XML root and constructs dashboard visual container JSONs.

    Args:
        root (Element): Parsed XML root from Tableau workbook.
        chart_types (list): List of chart types to process.
        logger_id (str): Logger identifier.

    Returns:
        list: List of dashboard visual container configurations.
    """
    logger.info(f"===Started dashboard migration=======")
    dashboard_result = []
    dashboards = root.findall(".//dashboard")
    table_column_data = get_tables_data(root)

    for dashboard in dashboards:
        dashboard_visual = []
        dashboard_name = dashboard.attrib.get("name")
        dashboard_style = dashboard.find("style")
        dashboard_title_layout = dashboard.findtext("./layout-options/title/formatted-text/run")

        if dashboard_title_layout:
            dashboard_title_result = get_title_textbox(dashboard_title_layout, dashboard_style, is_dashboard=True)
            if dashboard_title_result:
                dashboard_visual.extend(dashboard_title_result)

        slicer_result = get_slicer_report(dashboard, table_column_data)
        if slicer_result:
            dashboard_visual.extend(slicer_result)

        worksheet_data = process_worksheets(root, chart_types)
        worksheets = root.findall(".//worksheet")
        worksheet_names = get_worksheet_names(worksheets)
        zones = dashboard.find("zones")
        dashboard_reports = find_zones_by_name(zones, worksheet_names)

        for visual in dashboard_reports:
            worksheet_name = visual.attrib.get("name")
            visual_content = worksheet_data.get(worksheet_name)
            dimensions = calculate_dimensions(
                visual.attrib.get("x"), visual.attrib.get("y"),
                visual.attrib.get("h"), visual.attrib.get("w")
            )
            zones_format = visual.find("zone-style/format")

            if visual_content:
                if isinstance(visual_content, list):
                    for item in visual_content:
                        template = item.get("template")
                        config = item.get("config")
                        border_list = get_border_config(zones_format)
                        config["border_list"] = border_list
                        result = template.format(**config, **dimensions)
                        dashboard_visual.append({"config": result, "filters": "[]", **dimensions})
                else:
                    card_visual = worksheet_data.get(worksheet_name).get("visual_content")
                    no_of_cards = len(card_visual)
                    base_x = dimensions.get("x", 0)
                    base_y = dimensions.get("y", 0)
                    base_width = dimensions.get("width", 1000)
                    base_height = dimensions.get("height", 80)
                    margin = 10
                    card_width = (base_width - (margin * (no_of_cards - 1))) / no_of_cards
                    card_config_list = []
                    card_title = worksheet_data.get(worksheet_name).get("card_title")

                    if card_title:
                        card_title_template = card_title.get("template")
                        card_title_config = card_title.get("config")
                        card_title_dimensions = {
                            "x": base_x, "y": base_y,
                            "width": base_width, "height": 50, "z": 0.0
                        }
                        card_title_result = card_title_template.format(**card_title_config, **card_title_dimensions)
                        card_config_list.append({"config": card_title_result, "filters": "[]", **card_title_dimensions})

                    for idx, card_content in enumerate(card_visual):
                        card_template = card_content.get("template")
                        card_config = card_content.get("config")
                        card_x = base_x + idx * (card_width + margin)
                        card_y = base_y + 50
                        card_dimensions = {
                            "x": card_x, "y": card_y,
                            "width": card_width, "height": base_height, "z": 0.0
                        }
                        card_result = card_template.format(**card_config, **card_dimensions)
                        card_config_list.append({"config": card_result, "filters": "[]", **card_dimensions})

                    dashboard_visual.extend(card_config_list)

            for text_box_zone in get_text_box_json_only_in_dashboards(zones.findall("zone")):
                dashboard_visual.append(text_box_zone.get("visual_container"))

        dashboard_result.append({
            "dashboard_name": dashboard_name,
            "visual_container": dashboard_visual
        })

    return dashboard_result


