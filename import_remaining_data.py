#!/usr/bin/env python3
"""
Script to import remaining CSV data into PostgreSQL tables
"""
import psycopg2
import csv
import sys

# Database connection parameters
DB_CONFIG = {
    'host': 'localhost',
    'port': '5432',
    'user': 'postgres',
    'password': 'postgres',
    'database': 'test'
}

def get_connection():
    """Get database connection"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except psycopg2.Error as e:
        print(f"Error connecting to database: {e}")
        return None

def safe_value(value, data_type='str'):
    """Convert CSV value to appropriate type, handling NULL strings"""
    if value is None or value == '' or value.upper() == 'NULL':
        return None
    
    if data_type == 'int':
        try:
            return int(value)
        except (ValueError, TypeError):
            return None
    elif data_type == 'bool':
        return value.lower() == 'true'
    else:
        return value

def import_remaining_data():
    """Import remaining CSV data"""
    conn = get_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        # Import tableau_server_details
        print("Importing tableau_server_details...")
        with open('data/tableau_server_details.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                cursor.execute("""
                    INSERT INTO biport_dev.tableau_server_details 
                    (id, user_id, name, server_url, status, type, report_count, project_count, 
                     site_count, created_by, updated_by, created_at, updated_at, is_deleted, organization_id)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    safe_value(row['id']), safe_value(row['user_id']), safe_value(row['name']), 
                    safe_value(row['server_url']), safe_value(row['status']), safe_value(row['type']), 
                    safe_value(row['report_count'], 'int') or 0,
                    safe_value(row['project_count'], 'int') or 0,
                    safe_value(row['site_count'], 'int') or 0,
                    safe_value(row['created_by']), safe_value(row['updated_by']),
                    safe_value(row['created_at']), safe_value(row['updated_at']), 
                    safe_value(row['is_deleted'], 'bool'),
                    safe_value(row['organization_id'])
                ))
        
        # Import tableau_server_credentials
        print("Importing tableau_server_credentials...")
        with open('data/tableau_server_credentials.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                cursor.execute("""
                    INSERT INTO biport_dev.tableau_server_credentials 
                    (id, server_id, pat_name, pat_secret, username, password, server_auth_type,
                     created_by, updated_by, created_at, updated_at, is_deleted)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    safe_value(row['id']), safe_value(row['server_id']), 
                    safe_value(row['pat_name']), safe_value(row['pat_secret']),
                    safe_value(row['username']), safe_value(row['password']),
                    safe_value(row['server_auth_type']), safe_value(row['created_by']), 
                    safe_value(row['updated_by']), safe_value(row['created_at']), 
                    safe_value(row['updated_at']), safe_value(row['is_deleted'], 'bool')
                ))
        
        # Import tableau_site_details
        print("Importing tableau_site_details...")
        with open('data/tableau_site_details.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                cursor.execute("""
                    INSERT INTO biport_dev.tableau_site_details 
                    (id, credentials_id, site_name, site_id, created_by, updated_by, 
                     created_at, updated_at, is_deleted)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    safe_value(row['id']), safe_value(row['credentials_id']), 
                    safe_value(row['site_name']), safe_value(row['site_id']),
                    safe_value(row['created_by']), safe_value(row['updated_by']),
                    safe_value(row['created_at']), safe_value(row['updated_at']), 
                    safe_value(row['is_deleted'], 'bool')
                ))
        
        # Import final_project_details (project_details table)
        print("Importing project_details...")
        with open('data/final_project_details.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                try:
                    cursor.execute("""
                        INSERT INTO biport_dev.project_details
                        (id, name, is_upload, site_id, server_id, parent_id, user_id, assigned_to,
                         created_at, updated_at, is_deleted)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        safe_value(row['id']), safe_value(row['name']),
                        safe_value(row['is_upload'], 'bool'),
                        safe_value(row['site_id']), safe_value(row['server_id']),
                        safe_value(row['parent_id']), safe_value(row['user_id']),
                        safe_value(row['assigned_to']), safe_value(row['created_at']),
                        safe_value(row['updated_at']), safe_value(row['is_deleted'], 'bool')
                    ))
                except psycopg2.IntegrityError as e:
                    print(f"Skipping project {row['id']} due to foreign key constraint: {e}")
                    conn.rollback()
                    continue
        
        # Import report_details
        print("Importing report_details...")
        with open('data/report_details.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                try:
                    cursor.execute("""
                        INSERT INTO biport_dev.report_details
                        (id, name, report_id, project_id, is_analyzed, analyzed_status, is_converted,
                         converted_status, is_migrated, migrated_status, unit_tested, uat_tested,
                         deployed, is_scoped, semantic_type, has_semantic_model, view_count)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        safe_value(row['id']), safe_value(row['name']), safe_value(row['report_id']),
                        safe_value(row['project_id']), safe_value(row['is_analyzed'], 'bool'),
                        safe_value(row['analyzed_status']), safe_value(row['is_converted'], 'bool'),
                        safe_value(row['converted_status']), safe_value(row['is_migrated'], 'bool'),
                        safe_value(row['migrated_status']), safe_value(row['unit_tested'], 'bool'),
                        safe_value(row['uat_tested'], 'bool'), safe_value(row['deployed'], 'bool'),
                        safe_value(row['is_scoped'], 'bool'), safe_value(row['semantic_type']),
                        safe_value(row['has_semantic_model'], 'bool'),
                        safe_value(row['view_count'], 'int') or 0
                    ))
                except psycopg2.IntegrityError as e:
                    print(f"Skipping report {row['id']} due to foreign key constraint: {e}")
                    conn.rollback()
                    continue
        
        conn.commit()
        print("All remaining data imported successfully.")
        return True
        
    except Exception as e:
        print(f"Error importing data: {e}")
        conn.rollback()
        return False
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    if import_remaining_data():
        print("Remaining data import completed successfully!")
    else:
        print("Remaining data import failed!")
        sys.exit(1)
