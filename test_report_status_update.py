#!/usr/bin/env python3
"""
Test the report status update API implementation
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from app.services.workspace.workspace_service import WorkspaceService
    from app.services.workspace.workspace_procssor import WorkspaceProcessor
    from app.schemas.workspace import ReportStatusUpdateRequest
    from app.models.users import UserManager
    from app.models.report_details import ReportDetail
    from app.core.session import scoped_context
    print("✅ All imports successful")
except Exception as e:
    print(f"❌ Import error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

class MockUser:
    """Mock user object"""
    def __init__(self, user_id):
        self.id = user_id

def test_get_reports_with_status_fields():
    """Test that the get reports API includes the new status fields"""
    print("🧪 Testing Get Reports with Status Fields")
    print("=" * 50)
    
    try:
        # Get a test user
        user = UserManager.get_user_by_email("<EMAIL>", load_role=True)
        if not user:
            print("❌ Test user not found")
            return False
        
        print(f"✅ Test user found: {user.name} ({user.email})")
        
        # Test workspace service
        service = WorkspaceService()
        response = service.get_reports_by_user_role(user)
        
        if response.success:
            print(f"✅ Get reports successful: {len(response.data)} reports found")
            
            if response.data:
                sample_report = response.data[0]
                print(f"📋 Sample report fields:")
                for key, value in sample_report.items():
                    print(f"   {key}: {value}")
                
                # Check if new status fields are present
                required_fields = ['unit_tested', 'uat_tested', 'deployed']
                missing_fields = [field for field in required_fields if field not in sample_report]
                
                if missing_fields:
                    print(f"❌ Missing status fields: {missing_fields}")
                    return False
                else:
                    print(f"✅ All status fields present: {required_fields}")
                    return True
            else:
                print("📭 No reports found for this user")
                return True
        else:
            print(f"❌ Get reports failed: {response.error}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing get reports: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_update_report_status():
    """Test the update report status functionality"""
    print(f"\n🧪 Testing Update Report Status")
    print("=" * 50)
    
    try:
        # Get a test user
        user = UserManager.get_user_by_email("<EMAIL>", load_role=True)
        if not user:
            print("❌ Test user not found")
            return False
        
        # Get a report ID that this user has access to
        service = WorkspaceService()
        reports_response = service.get_reports_by_user_role(user)
        
        if not reports_response.success or not reports_response.data:
            print("❌ No reports available for testing")
            return False
        
        test_report = reports_response.data[0]
        report_id = test_report['report_id']
        
        print(f"✅ Using test report: {test_report['report_name']} (ID: {report_id})")
        print(f"   Current status: unit_tested={test_report['unit_tested']}, uat_tested={test_report['uat_tested']}, deployed={test_report['deployed']}")
        
        # Test 1: Update unit_tested to True
        print(f"\n📝 Test 1: Update unit_tested to True")
        status_update = ReportStatusUpdateRequest(unit_tested=True)
        
        response = service.update_report_status(report_id, status_update, user)
        if response.success:
            print(f"✅ Update successful: {response.data['message']}")
        else:
            print(f"❌ Update failed: {response.error}")
            return False
        
        # Test 2: Update multiple fields
        print(f"\n📝 Test 2: Update multiple fields")
        status_update = ReportStatusUpdateRequest(
            unit_tested=True,
            uat_tested=True,
            deployed=False
        )
        
        response = service.update_report_status(report_id, status_update, user)
        if response.success:
            print(f"✅ Multiple field update successful: {response.data['message']}")
        else:
            print(f"❌ Multiple field update failed: {response.error}")
            return False
        
        # Test 3: Verify the changes were persisted
        print(f"\n📝 Test 3: Verify changes were persisted")
        updated_reports_response = service.get_reports_by_user_role(user)
        
        if updated_reports_response.success:
            updated_report = next((r for r in updated_reports_response.data if r['report_id'] == report_id), None)
            if updated_report:
                print(f"✅ Updated report found")
                print(f"   New status: unit_tested={updated_report['unit_tested']}, uat_tested={updated_report['uat_tested']}, deployed={updated_report['deployed']}")
                
                # Verify the expected values
                if (updated_report['unit_tested'] == True and 
                    updated_report['uat_tested'] == True and 
                    updated_report['deployed'] == False):
                    print(f"✅ Status values match expected results")
                    return True
                else:
                    print(f"❌ Status values don't match expected results")
                    return False
            else:
                print(f"❌ Updated report not found")
                return False
        else:
            print(f"❌ Failed to fetch updated reports: {updated_reports_response.error}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing update report status: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_processor():
    """Test the workspace processor"""
    print(f"\n🧪 Testing Workspace Processor")
    print("=" * 50)
    
    try:
        # Get a test user
        user = UserManager.get_user_by_email("<EMAIL>", load_role=True)
        if not user:
            print("❌ Test user not found")
            return False
        
        # Get a report ID
        service = WorkspaceService()
        reports_response = service.get_reports_by_user_role(user)
        
        if not reports_response.success or not reports_response.data:
            print("❌ No reports available for testing")
            return False
        
        report_id = reports_response.data[0]['report_id']
        
        # Test processor
        status_update = ReportStatusUpdateRequest(unit_tested=False, uat_tested=True)
        
        response = WorkspaceProcessor.process_update_report_status(report_id, status_update, user)
        
        if response.success:
            print(f"✅ Processor test successful: {response.data['message']}")
            return True
        else:
            print(f"❌ Processor test failed: {response.error}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing processor: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🧪 Report Status Update API Testing")
    print("=" * 60)
    
    tests = [
        ("Get Reports with Status Fields", test_get_reports_with_status_fields),
        ("Update Report Status", test_update_report_status),
        ("Workspace Processor", test_processor)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        result = test_func()
        results.append((test_name, result))
    
    # Summary
    print(f"\n{'='*60}")
    print("🎯 Test Summary:")
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {status}: {test_name}")
    
    all_passed = all(result for _, result in results)
    if all_passed:
        print(f"\n🎉 All tests passed! The report status update API is working correctly.")
    else:
        print(f"\n❌ Some tests failed. Check the details above.")
    
    return all_passed

if __name__ == "__main__":
    main()
