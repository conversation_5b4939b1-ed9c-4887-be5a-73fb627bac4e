import uuid, json
from ..core import process_title_layout, to_list, create_expr, calculate_dimensions
from app.core.templates import title_textbox, text_box_json
from app.core.logger_setup import logger


def get_textbox_report(rows,cols,worksheet_name, ws="", textbox_type = "custom"):
    #work_sheet_text_box_json
    try:
        text_box_result=[]
        text_box_values={}
        if textbox_type == "textbox":
            text_box_values["visual_config_name"]=f'{str(uuid.uuid4()).replace("-","")[:20]}'
            text_value=ws.get("table",{}).get("view",{}).get("datasource-dependencies",{}).get("column",{}).get("calculation").get("@formula","This Visual is not supported").replace("\"","")
            styles=ws.get("table",{}).get("style",{}).get("style-rule")
            for_color = [d.get("format") for d in styles if d['@element'] == "header"][0]
            background_color=[d.get("@value") for d in for_color if d.get('@attr')=="background-color"][0]
            for_label=for_color = [d.get("format") for d in styles if d['@element'] == "label"][0]
            fs=[d.get("@value") for d in for_label if d.get('@attr')=="font-style"][0]
            fsize=[d.get("@value") for d in for_label if d.get('@attr')=="font-size"][0]
            fdec=[d.get("@value") for d in for_label if d.get('@attr')=="text-decoration"][0]
            fw=[d.get("@value") for d in for_label if d.get('@attr')=="font-weight"][0]
            tplace="center"
            text_box_values["objects"]=get_objects(text_value,fsize,fdec,fw,fs,tplace)
            text_box_values["vc_objects"]=get_vc_objects_of_text_box(background_color)
            
            text_box_result.append({"config":text_box_values,"template":text_box_json})
            return text_box_result
        else:
            text_box_values["visual_config_name"]=f'{str(uuid.uuid4()).replace("-","")[:20]}'
            text_box_values["objects"]=get_objects("This visual migration is not supported at this moment. It will be considered for later release","14pt","underline","bold","italic","center")
            text_box_values["vc_objects"]=get_vc_objects_of_text_box(border_color="#000000",bg_color="#FFFFFF")
            text_box_result.append({"config":text_box_values,"template":text_box_json})
            return text_box_result
        
    except Exception as e:
        logger.error(f"---Error in generating text box visual for {worksheet_name}--- {str(e)}")
        raise ValueError(f"Error in generating text box visual for {worksheet_name} - {str(e)}")

def process_style_format_data(format_data):
    background_color, border_color = None, None
    attr_list = []
    for format in format_data:
        if not format:
            continue
        format_attr = format.get("@attr")
        attr_list.append(format_attr)
        if format_attr == "background-color":
            background_color = format.get("@value")
            background_color = background_color[:7] if len(background_color) > 7 else background_color
        if format_attr == "border-color":
            border_color = format.get("@value")
        if "border-style" in attr_list and "border-color" not in attr_list:
            border_color = "#000000"

    return border_color, background_color

def process_style_data(style_data= None, style_type = None, zone_style = None):
    background_color, border_color = None, None
    if style_data:
        style_rule = to_list(style_data.get("style-rule"))
        for rule in style_rule:
            if rule.get("@element") == style_type:
                format_list = to_list(rule.get("format"))
                border_color, background_color = process_style_format_data(format_list)
    if zone_style:
        border_color, background_color = process_style_format_data(zone_style)

    if border_color:
        border_config = [{"properties": {"color": {"solid": {"color": create_expr(f"'{border_color}'")}},"show": create_expr("true")}}]
    else:
        border_config = [{"properties": {"show": create_expr("false")}}]
    if background_color:
        background_config = [{"properties": {"color": {"solid": {"color": create_expr(f"'{background_color}'")}},"show": create_expr("true")}}]
    else:
        background_config = [{"properties": {"show": create_expr("false")}}]

    return border_config, background_config

def get_title_textbox(worksheet_title_layout, style_data = None, zone_style = None, is_dashboard= False):
    overall_result = []
    alignment = "left"
    text_paragraphs = []
    border_config = [{"properties": {"show": create_expr("false")}}]
    background_config = [{"properties": {"show": create_expr("false")}}]
    if isinstance(worksheet_title_layout, str):
        text_value = worksheet_title_layout
        fontSize = "18pt" if is_dashboard else "14pt"
        text_runs = [{"value": text_value, "textStyle": {"fontSize": fontSize, "fontFamily": "Calibri"}}]
        text_paragraphs.append({"textRuns": text_runs, "horizontalTextAlignment": alignment})

    else:
        worksheet_title_layout = to_list(worksheet_title_layout)
        for title_data in worksheet_title_layout:
            if title_data == "\u00c6":
                continue

            elif isinstance(title_data, str):
                text_value = title_data
                font_size = "18pt" if is_dashboard else "14pt"
                text_runs = [{"value": text_value, "textStyle": {"fontSize": font_size, "fontFamily": "Calibri"}}]
                text_paragraphs.append({"textRuns": text_runs, "horizontalTextAlignment": alignment})
            
            else:
                if title_data.get('#text') == "\u00c6":
                    continue
                text_value = title_data.get('#text')
                font_size = title_data.get('@fontsize')
                text_color = title_data.get('@fontcolor')
                font_name = title_data.get('@fontname')
                font_weight = title_data.get('@bold')
                font_style = title_data.get('@italic')
                font_decoration = title_data.get('@underline')
                font_alignment = title_data.get('@fontalignment')

                text_runs = [{"value": text_value, "textStyle": {}}]
                if font_size:
                    text_runs[0]["textStyle"]["fontSize"] = f"{font_size}pt"
                else:
                    text_runs[0]["textStyle"]["fontSize"] = "18pt" if is_dashboard else "15pt"
                if text_color:
                    text_runs[0]["textStyle"]["color"] = text_color
                if font_name:
                    text_runs[0]["textStyle"]["fontFamily"] = font_name if "Tableau" not in font_name else "Calibri"
                if font_weight and font_weight == "true":
                    text_runs[0]["textStyle"]["fontWeight"] = "bold"
                if font_style and font_style == "true":
                    text_runs[0]["textStyle"]["fontStyle"] = "italic"
                if font_decoration and font_decoration == "true":
                    text_runs[0]["textStyle"]["textDecoration"] = "underline"
                if font_alignment:
                    if font_alignment == "1":
                        alignment = "center"
                    elif font_alignment == "2":
                        alignment = "right"
                    else:
                        alignment = "left"
                text_paragraphs.append({"textRuns": text_runs, "horizontalTextAlignment": alignment})

    style_type = "dash-title" if is_dashboard else "title"

    if style_data:
        border_config, background_config = process_style_data(style_data = style_data, style_type = style_type, zone_style = None)

    if zone_style:
        border_config, background_config = process_style_data(style_data = None, style_type = None, zone_style = zone_style)
    
    title_json = {
        "visual_config_name" : f'{str(uuid.uuid4()).replace("-","")[:20]}', 
        "text_paragraphs":json.dumps(text_paragraphs),
        "background_list" : json.dumps(background_config),
        "border_list" : json.dumps(border_config)
        }
    if is_dashboard:
        dimension_json = {"height": 50, "width": 1277, "x":0.00, "y" : 0.00, "z": 0}
        result = title_textbox.format(**title_json, **dimension_json)
        overall_result.append({"config":result, **dimension_json})
    else:
        return {"config":title_json, "template":title_textbox}
    return overall_result
 
def get_text_box_json_only_in_dashboards(zones):
    result_json=[]
    zones=zones if isinstance(zones,list) else [zones]
    for zone in zones:
        if zone.get("@type-v2")=="text":
            zone_details={}
            zone_details["visual_config_name"]=f'{str(uuid.uuid4()).replace("-","")[:20]}'
            zone_details["x"]=f'{(float(zone.get("@x"))* 1280.00 / 100000.00)}'
            zone_details["y"]=f'{(float(zone.get("@y"))* 720.00 / 100000.00)}'
            zone_details["width"]=f'{(float(zone.get("@w"))* 1280.00 / 100000.00)}'
            zone_details["height"]=f'{(float(zone.get("@h"))* 720.00 / 100000.00)}'
            zone_run_details = zone.get("formatted-text",{}).get("run")
            if not zone_run_details:
                continue
            processed_title_data = process_title_layout(zone_run_details)
            text_value = processed_title_data.get("#text",{})
            fw = "bold" if processed_title_data.get("@bold",{})=="true" else ""
            fsize = processed_title_data.get("@fontsize","14")+"pt"
            tplace = processed_title_data.get("@fontalignment",{})
            tplace_value = "right" if tplace=="2" else "center" if tplace=="1" else "left"
            fdec = processed_title_data.get("@underline",{})
            fs=""
            zone_details["objects"]=get_objects(text_value,fsize,fdec,fw,fs,tplace_value)
            zone_details["vc_objects"]=get_vc_objects_of_text_box()
            dash_board_filters={"filters": "[]","height": float(zone_details["height"]),"width":float(zone_details["width"]),"x":float(zone_details["x"]),"y": float(zone_details["y"]),"z": 0.00}
            dash_board_text_box_json=text_box_json.format(**zone_details)
            result_json.append({"dashboard_name":text_value,"visual_container":{"config":dash_board_text_box_json,**dash_board_filters}})
    return result_json

def get_objects(text_value, fsize, fdec, fw, fs, tplace):
    data = {"objects": {"general": [{"properties": {"paragraphs": [{"textRuns":[{"value": text_value,"textStyle": {"fontFamily": "Calibri","fontSize": f"{fsize}"}}]}]}}]}}
    text_style = data["objects"]["general"][0]["properties"]["paragraphs"][0]["textRuns"][0]["textStyle"]
    paragraph = data["objects"]["general"][0]["properties"]["paragraphs"][0]
    if fdec:
        text_style["textDecoration"] = fdec
    if fw:
        text_style["fontWeight"] = fw
    if fs:
        text_style["fontStyle"] = fs
    if tplace != "left":
        paragraph["horizontalTextAlignment"] = tplace
    data=json.dumps(data)
    return data[1:-1]

def get_vc_objects_of_text_box(bg_color=None, border_color=None):

    if bg_color:
        backgorung_config = [{"properties":{"show":{"expr":{"Literal":{"Value":"true"}}},"color":{"solid":{"color":{"expr":{"Literal":{"Value":f"'{bg_color}'"}}}}}}}]
    else:
        backgorung_config = [{"properties":{"show":{"expr":{"Literal":{"Value":"false"}}}}}]

    if border_color:
        border_config = [{"properties":{"show":{"expr":{"Literal":{"Value":"true"}}},"color":{"solid":{"color":{"expr":{"Literal":{"Value":f"'{border_color}'"}}}}}}}]
    else:
        border_config = [{"properties":{"show":{"expr":{"Literal":{"Value":"false"}}}}}]
    
    data={"vcObjects":{"border":border_config,"background":backgorung_config,"title":[{"properties":{"show":{"expr":{"Literal":{"Value":"false"}}}}}]}}
    data=json.dumps(data)
    return data[1:-1]

def get_dashboard_textbox(zones):
    try:
        overall_result = []
        textbox_data = []
        zones = to_list(zones)
        for zone in zones:
            if zone.get("@type-v2") == "text":
                x = zone.get("@x")
                y = zone.get("@y")
                width = zone.get("@w")
                height = zone.get("@h")
                dimensions = calculate_dimensions(x, y, height, width)
                formatted_run = zone.get("formatted-text", {}).get("run")
                if not formatted_run:
                    continue
                formatted_run = to_list(formatted_run)
                zone_style_format = zone.get("zone-style", {}).get("format")
                zone_style_format = to_list(zone_style_format)
                textbox_result = get_title_textbox(formatted_run, style_data= None, zone_style = zone_style_format, is_dashboard=False)
                textbox_data.append({"config_result": textbox_result["config"], "dimensions": dimensions})

        for result in textbox_data:
            config_result = result["config_result"]
            dimensions = result["dimensions"]
            json_result = title_textbox.format(**config_result, **dimensions)
            overall_result.append({"config": json_result, "filters":"[]", **dimensions})
            
        return overall_result
    except Exception as e:
        logger_migrate.error(f"-Error in generating dashboard textbox - {str(e)}")
        raise ValueError(f"Error in generating dashboard textbox - {str(e)}")
