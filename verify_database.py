#!/usr/bin/env python3
"""
Script to verify the database tables and data
"""
import psycopg2
import sys

# Database connection parameters
DB_CONFIG = {
    'host': 'localhost',
    'port': '5432',
    'user': 'postgres',
    'password': 'postgres',
    'database': 'test'
}

def get_connection():
    """Get database connection"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except psycopg2.Error as e:
        print(f"Error connecting to database: {e}")
        return None

def verify_database():
    """Verify database tables and data"""
    conn = get_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        print("=== DATABASE VERIFICATION ===\n")
        
        # Check if schema exists
        cursor.execute("""
            SELECT schema_name 
            FROM information_schema.schemata 
            WHERE schema_name = 'biport_dev'
        """)
        schema_exists = cursor.fetchone()
        print(f"Schema 'biport_dev' exists: {bool(schema_exists)}")
        
        # List all tables in biport_dev schema
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'biport_dev'
            ORDER BY table_name
        """)
        tables = cursor.fetchall()
        
        print(f"\nTables in biport_dev schema ({len(tables)} total):")
        for table in tables:
            print(f"  - {table[0]}")
        
        # Check data counts for each table
        print(f"\nData counts per table:")
        for table in tables:
            table_name = table[0]
            cursor.execute(f"SELECT COUNT(*) FROM biport_dev.{table_name}")
            count = cursor.fetchone()[0]
            print(f"  - {table_name}: {count} records")
        
        # Show sample data from key tables
        print(f"\n=== SAMPLE DATA ===")
        
        # Organization details
        print(f"\nOrganization Details (first 3 records):")
        cursor.execute("SELECT id, name, credits FROM biport_dev.organization_details LIMIT 3")
        orgs = cursor.fetchall()
        for org in orgs:
            print(f"  - {org[0]}: {org[1]} (Credits: {org[2]})")
        
        # Roles
        print(f"\nRoles:")
        cursor.execute("SELECT id, name FROM biport_dev.roles")
        roles = cursor.fetchall()
        for role in roles:
            print(f"  - {role[0]}: {role[1]}")
        
        # Users (first 5)
        print(f"\nUsers (first 5 records):")
        cursor.execute("SELECT id, name, email FROM biport_dev.users LIMIT 5")
        users = cursor.fetchall()
        for user in users:
            print(f"  - {user[0]}: {user[1]} ({user[2]})")
        
        # Tableau servers
        print(f"\nTableau Servers:")
        cursor.execute("SELECT id, name, server_url, status FROM biport_dev.tableau_server_details")
        servers = cursor.fetchall()
        for server in servers:
            print(f"  - {server[0]}: {server[1]} ({server[2]}) - {server[3]}")
        
        # Check foreign key relationships
        print(f"\n=== FOREIGN KEY VERIFICATION ===")
        
        # Users with organizations
        cursor.execute("""
            SELECT u.name, o.name as org_name 
            FROM biport_dev.users u 
            JOIN biport_dev.organization_details o ON u.organization_id = o.id 
            LIMIT 3
        """)
        user_orgs = cursor.fetchall()
        print(f"\nUsers with Organizations (first 3):")
        for user_org in user_orgs:
            print(f"  - {user_org[0]} works at {user_org[1]}")
        
        # Users with roles
        cursor.execute("""
            SELECT u.name, r.name as role_name 
            FROM biport_dev.users u 
            JOIN biport_dev.roles r ON u.role_id = r.id 
            LIMIT 3
        """)
        user_roles = cursor.fetchall()
        print(f"\nUsers with Roles (first 3):")
        for user_role in user_roles:
            print(f"  - {user_role[0]} has role {user_role[1]}")
        
        print(f"\n=== VERIFICATION COMPLETE ===")
        print(f"✅ Database setup successful!")
        print(f"✅ All tables created in 'biport_dev' schema")
        print(f"✅ Data imported from CSV files")
        print(f"✅ Foreign key relationships working")
        
        return True
        
    except Exception as e:
        print(f"Error during verification: {e}")
        return False
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    if verify_database():
        print(f"\n🎉 Database verification completed successfully!")
    else:
        print(f"\n❌ Database verification failed!")
        sys.exit(1)
