from datetime import datetime
import uuid
from sqlalchemy import Column, <PERSON>, Integer, Foreign<PERSON>ey, Enum, DateTime, or_, text
from app.core import Base, scoped_context
from app.core.enums import ServerType, ServerStatus
from sqlalchemy.dialects.postgresql import UUID
from app.models_old.on_premise_server import OnPremiseServer
from app.core.exceptions import BadRequestError
class ServerDetails(Base):
    __tablename__ = "server_details"

    id = Column(UUID(as_uuid=True), primary_key=True, nullable=False)
    user_id = Column(Integer, ForeignKey('users.id'))
    server_name = Column(String(100), nullable=False)
    server_url = Column(String(100), nullable=False)
    status = Column(Enum(ServerStatus), default=ServerStatus.ACTIVE)
    server_type = Column(Enum(ServerType), nullable=False)
    site_count = Column(Integer, default=0)
    report_count = Column(Integer, server_default=text("0"))
    project_count = Column(Integer, server_default=text("0"))
    created_by = Column(Integer, nullable=False)
    updated_by = Column(Integer, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class ServerDetailsManager:
    @staticmethod
    def add_server(user_id, server_name, server_url, server_type, created_by, updated_by):
        """Adds a new server to the database and returns the new server's UUID."""
        id = uuid.uuid4()
        with scoped_context() as session:
            new_server = ServerDetails(
                id = id,
                user_id=user_id,
                server_name=server_name,
                server_url=server_url,
                server_type=server_type,
                created_by=created_by,
                updated_by=updated_by
            )
            session.add(new_server)
            session.commit()
            return id 

    @staticmethod
    def get_server_by_name_or_url(server_name=None, server_url=None):
        """Fetch a server by its name or URL."""
        with scoped_context() as session:
            return session.query(ServerDetails).filter(
                or_(ServerDetails.server_name == server_name, ServerDetails.server_url == server_url)
            ).first()

    @staticmethod
    def get_server_by_id(server_id: uuid.UUID) -> ServerDetails:
        """Fetch a server by its ID."""
        with scoped_context() as session:
            return session.query(ServerDetails).filter_by(id=server_id).first()
        
    @staticmethod
    def delete_server(server_id: uuid.UUID):
        """Deletes a server by its ID."""
        with scoped_context() as session:
            server = session.query(ServerDetails).filter_by(id=server_id).first()
            session.delete(server)
            session.commit()
            
    @staticmethod
    def update_server(server_id, server_name, server_url, user_id):
        """Updates a server by its ID."""
        with scoped_context() as session:
            server = session.query(ServerDetails).filter_by(id=server_id).first()
            server.server_name = server_name
            server.server_url = server_url
            server.updated_by = user_id
            session.commit()

    @staticmethod
    def get_total_servers() -> int:
        """Fetch the total number of servers."""
        with scoped_context() as session:
            return session.query(ServerDetails).count()

    @staticmethod
    def get_servers(offset: int, limit: int):
        """Fetch servers with pagination."""
        with scoped_context() as session:
            return session.query(ServerDetails).order_by(ServerDetails.updated_at).offset(offset).limit(limit).all()
        
    @staticmethod
    def update_server_status(server_id: uuid.UUID, status):
        """Updates the status of a server."""
        with scoped_context() as session:
            server = session.query(ServerDetails).filter_by(id=server_id).first()
            server.status = status
            session.commit()
    
    @staticmethod
    def get_server(server_id):
        """Updates a server by its ID."""
        with scoped_context() as session:
            server = session.query(ServerDetails.id, ServerDetails.server_type, OnPremiseServer.server_auth_type).join(
                OnPremiseServer, ServerDetails.id == OnPremiseServer.server_id
            ).filter(ServerDetails.id == server_id).first()
            if server:
                return server
            else:
                raise ValueError("Server not found")

    @staticmethod
    def update_site_count(server_id: uuid.UUID, site_count: int):
        """Updates the site count of a server."""
        with scoped_context() as session:
            server = session.query(ServerDetails).filter_by(id=server_id).first()
            if server and server.site_count != site_count:
                # Update the site count only if it has changed
                server.site_count = site_count
                session.commit()

    @staticmethod
    def get_sites_count_and_list(user_id: int):
        """Fetches sites associated with a user."""
        with scoped_context() as session:
            # sites_data = session.query(ServerDetails).filter(ServerDetails.user_id == user_id).all()
            server_details = session.query(ServerDetails.server_name, 
                                       ServerDetails.site_count, 
                                       ServerDetails.report_count, 
                                       ServerDetails.project_count).all()
            return server_details
        
    @staticmethod
    def update_project_and_report_count(server_id: uuid.UUID, project_count: int, report_count: int):
        """Updates the project and report count of a server."""
        with scoped_context() as session:
            server = session.query(ServerDetails).filter_by(id=server_id).first()
            if server:
                if server.project_count != project_count or server.report_count != report_count:
                    server.project_count = project_count
                    server.report_count = report_count
                    session.commit()
