from app.core import logger
from app.core.enums import ChartType, GeneralKeys as GS, WorkSheet as WS

class CardChart:
    @staticmethod
    def check_card_chart(worksheet):
        try:
            panes = worksheet.findall(WS.PANE.value)
            pane = panes[0] if panes else None

            if not pane:
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            text_tag = pane.find(WS.ENCODING_TEXT.value)
            if text_tag is None:
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            text_column_full = text_tag.get(WS.COLUMN.value)
            if not text_column_full:
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            text_column = text_column_full.strip("[]").split(":")

            rows_text_element = worksheet.find(WS.ROWS.value)
            cols_text_element = worksheet.find(WS.COLS.value)

            rows_text = rows_text_element.text if rows_text_element is not None and rows_text_element.text is not None else ''
            cols_text = cols_text_element.text if cols_text_element is not None and cols_text_element.text is not None else ''

            rows = [row.strip("[]").split(":") for row in rows_text.split(",") if row] if rows_text else []
            cols = [col.strip("[]").split(":") for col in cols_text.split(",") if col] if cols_text else []

            if not text_column or text_column == [""]:
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            if rows:
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            if len(text_column) == 3:
                if not cols:
                    return {
                        GS.STATUS.value: True,
                        GS.CHART_TYPE.value: ChartType.CARDS.value
                    }
                else:
                    return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            if len(text_column) == 2:
                if len(cols) > 1:
                    return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}
                if len(cols) == 1:
                    cols = ["Measure Names"]

            if "Measure Values" in cols:
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            return {
                GS.STATUS.value: True,
                GS.CHART_TYPE.value: ChartType.CARDS.value
            }

        except Exception as e:
            logger.error(f"Unexpected error in check_cards: {e}")
            return {
                GS.STATUS.value: False,
                GS.CHART_TYPE.value: None,
                GS.ERROR.value: str(e)
            }
