import os
from pathlib import Path
import shutil
import aiofiles
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, status
from botocore.exceptions import Client<PERSON><PERSON>r
from typing import Tuple, List, Dict, Any
from app.core.config import S3Config
from app.core import ClOUD_WORKBOOKS_PATH
from app.core import ANALYZED_OUTPUTS_DIR, INPUT_FILES_DIR
from app.core.constants import ANALYZED_WORKBOOK_FILE_PATH, WORKBOOKS_PATH
from app.core.logger_setup import logger
from botocore.exceptions import NoCredentialsError, PartialCredentialsError
from botocore.exceptions import EndpointConnectionError




s3_config = S3Config()
bucket_name = s3_config.bucket_name

async def download_twb_files_from_s3_by_id(twb_files: List[dict], is_upload_file: bool) -> Tuple[str, str, str, List[Dict]]:
    if not twb_files:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail='No files provided')

    if is_upload_file:
        full_path = twb_files[0]
        s3_path = full_path.get('s3_path')
        unique_folder = os.path.dirname(s3_path)
    else:
        unique_folder = f"{WORKBOOKS_PATH}"

    local_dir = os.path.join("./storage", unique_folder)
    twb_file_dir = os.path.join(local_dir, INPUT_FILES_DIR)
    os.makedirs(twb_file_dir, exist_ok=True)

    valid_ids, unsupported_files = [], []

    async with S3Config().get_s3_client() as s3_client:
        for entry in twb_files:
            if is_upload_file:
                s3_key = entry.get('s3_path')
            else:
                workbook_id = entry.get('workbook_id')
                s3_key = f"{ClOUD_WORKBOOKS_PATH}{workbook_id}.twb"

            try:
                await s3_client.head_object(Bucket=bucket_name, Key=s3_key)
                valid_ids.append(s3_key)
            except ClientError as e:
                if e.response['Error']['Code'] == "404":
                    unsupported_files.append({"file_id": entry, "message": f"No .twb file found for '{entry}'."})
                else:
                    raise e

    if not valid_ids:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="No valid .twb files found to process.")

    input_dir = await convert_twb_to_txt_s3(bucket_name, valid_ids, twb_file_dir)
    logger.info(f"All TWB files converted: {input_dir}")

    return input_dir, local_dir, unique_folder, unsupported_files


async def zip_output_files(folder_name: str, unique_folder: str, folder_path: str, local_dir: str) -> Tuple[str, str]:
   
    """
    Create zip of output files and upload to S3.

    Args:
        folder_name (str): Name of the folder to zip.
        unique_folder (str): Target S3 folder.
        folder_path (str): Local path to zip.
        local_dir (str): Base directory for zip file.

    Returns:
        Tuple[str, str]: Presigned URL and zip file name.
    """
    parent_s3_folder = unique_folder
    zip_path = shutil.make_archive(base_name=os.path.join(local_dir, folder_name), format='zip', root_dir=folder_path)
    zip_file = Path(zip_path)
    zip_s3_key = f"{parent_s3_folder}/{ANALYZED_OUTPUTS_DIR}/{zip_file.name}"
    await s3_config.upload_to_s3(file_path = str(zip_path), object_name = zip_s3_key)
    zip_file_name = zip_file.name
    download_url= await s3_config.generate_presigned_url(
            object_key = zip_s3_key,
        )
    return download_url, zip_file_name


  


def sanitize_for_json(data: Any, seen: set = None) -> Any:
    """
    Recursively convert objects to JSON-compatible formats and handle circular references.

    Args:
        data (Any): The object to sanitize.
        seen (set, optional): Internal tracker for circular refs.

    Returns:
        Any: Sanitized object.
    """
    if seen is None:
        seen = set()

    if id(data) in seen:
        return "[Circular Reference]"
    seen.add(id(data))

    if isinstance(data, dict):
        return {key: sanitize_for_json(value, seen) for key, value in data.items()}
    elif isinstance(data, list):
        return [sanitize_for_json(item, seen) for item in data]
    elif isinstance(data, (str, int, float, bool)) or data is None:
        return data
    else:
        return str(data)


async def convert_twb_to_txt_s3(bucket_name, s3_keys, local_files_directory):
    """
    Convert TWB files from S3 and save TXT files in an 'input_files' subdirectory locally.

    Args:
        bucket_name (str): The name of the S3 bucket.
        s3_keys (list): List of S3 keys (paths) of the TWB files.
        local_files_directory (str): The local directory where converted files will be stored.

    Returns:
        str: Path to the local directory containing the converted TXT files.
    """
    # Ensure the local directory exists
    os.makedirs(local_files_directory, exist_ok=True)

    if not s3_keys:
        logger.warning("No S3 keys provided.")
        return local_files_directory

    # Process each S3 key
    for s3_key in s3_keys:
        try:
            # Extract and sanitize filename from S3 key
            filename = os.path.basename(s3_key)
            filename = os.path.normpath(filename).replace("..", "")  # Sanitize

            if not filename.endswith(".twb"):
                logger.warning(f"Skipping non-TWB file: {filename}")
                continue

            # Define local path
            local_twb_path = os.path.join(local_files_directory, filename)
            logger.info(f"Preparing to download from S3: Bucket={bucket_name}, Key={s3_key}")
            logger.info(f"Local path for download: {local_twb_path}")

            # Download file from S3
            try:
                async with S3Config().get_s3_client() as s3_client:
                    await s3_client.download_file(bucket_name, s3_key, local_twb_path)
                    logger.info(f"Downloaded file to: {local_twb_path}")
            except (NoCredentialsError, PartialCredentialsError) as e:
                logger.error(f"S3 authentication error: {e}")
                continue
            except EndpointConnectionError as e:
                logger.error(f"Connection error to S3 endpoint: {e}")
                continue
            except Exception as e:
                logger.error(f"Unexpected error during S3 client operation: {e}")
                continue

            # Read and write the content of the TWB file
            try:
                with open(local_twb_path, 'r', encoding='utf-8') as file:
                    content = file.read()

                # Convert to TXT and save locally
                txt_filename = filename.replace('.twb', '.txt')
                txt_file_path = os.path.join(local_files_directory, txt_filename)

                with open(txt_file_path, 'w', encoding='utf-8') as txt_file:
                    txt_file.write(content)
                logger.info(f"Converted {filename} to TXT: {txt_file_path}")
            except IOError as e:
                logger.error(f"File I/O error: {e}")
                continue  # Skip this file and move to the next
        
        except Exception as e:
            logger.error(f"Unexpected error processing file")
    return local_files_directory
