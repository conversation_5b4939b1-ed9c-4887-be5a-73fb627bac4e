# app/routes/dashboard.py

from fastapi import APIRouter, Depends, Form
from fastapi.responses import JSONResponse
from app.models_old.user import UserOld
from app.schemas.datasources import GetUserDetails
from app.core.dependencies import get_current_user
from app.services.dashboard.dashboard_processor import DashboardProcessor


dashboard_router = APIRouter()

@dashboard_router.get("/get-dashboard-details")
async def get_dashboard_details(user: UserOld = Depends(get_current_user)):
    """Get dashboard details."""
    response = DashboardProcessor.get_dashboard_details_process(user.id)
    return JSONResponse(content={"data": response.data, "error": response.error}, status_code = response.status_code)
