import xml.etree.ElementTree as ET
import re, json, xmltodict, os
from openai import OpenAI, APIConnectionError, AuthenticationError
from itertools import islice
from app.core.config import OpenAIConfig, logger

openai_config = OpenAIConfig()
client = openai_config.get_openai_client()

def resolve_dependencies(calculations):
    if not calculations:
        return []

    name_to_caption = {calc['name']: calc.get('caption') for calc in calculations}

    def resolve_formula_with_captions(formula):
        """
        Resolves formulas by replacing dependencies with their captions.
        """
        pattern = r"\[.*?\]"
        matches = re.findall(pattern, formula)

        if not matches:
            return formula

        for match in matches:
            if match in name_to_caption:
                resolved_caption = name_to_caption[match]
                if resolved_caption:  
                    resolved_caption = f"[{resolved_caption}]"
                    formula = formula.replace(match, resolved_caption)
                else:
                    return formula
        return formula

    resolved_calculations = []
    for calc in calculations:
        independent_formula = resolve_formula_with_captions(calc['formula']) if calc['formula'] else "No formula"
        resolved_calculations.append({
            "name": calc['name'],
            "caption": calc.get('caption', calc['name']),
            "formula": independent_formula,
            "data_type": calc.get('data_type', ''),
            "role": calc.get('role', ''),
            "column_type": calc.get('column_type', '')
        })

    return resolved_calculations

def calculate_fields(root):
    logger.info("Started extracting calculated fields from XML root.")
    try:
        calculations_list = []
        datasources_element = root.find('datasources')

        if datasources_element is None:
            logger.warning("No <datasources> tag found in the XML.")
            return []

        datasources = datasources_element.findall('datasource')
        logger.info(f"Found {len(datasources)} datasource(s).")

        for datasource in datasources:
            columns = datasource.findall("column")
            logger.info(f"Datasource has {len(columns)} column(s).")

            for column in columns:
                formula_element = column.find("calculation")

                if formula_element is not None and 'formula' in formula_element.attrib:
                    column_name = column.get('name')
                    formula = formula_element.get('formula')
                    caption = column.get('caption')
                    data_type = column.get('datatype')
                    role = column.get('role')
                    column_type = column.get('type')

                    calculations_list.append({
                        "name": column_name,
                        "formula": formula,
                        "caption": caption,
                        "data_type": data_type,
                        "role": role,
                        "column_type": column_type
                    })
                    logger.info(f"Extracted calculated field: name={column_name}, caption={caption}, type={column_type}")

        logger.info(f"Completed extracting calculated fields. Total calculated fields found: {len(calculations_list)}")
        return resolve_dependencies(calculations_list)

    except ET.ParseError as e:
        logger.error(f"Error parsing XML file: {str(e)}")
        return []
    except Exception as e:
        logger.error(f"Unexpected error during calculation extraction: {str(e)}")
        return []


def fix_xml_line(xml_line):
    return re.sub(r"formula=x'([^']*)'", r'formula="\1"', xml_line)

def extract_json(file_path):
    try:
        with open(file_path, encoding='utf-8') as xml_file:
            content = xml_file.read()
            fixed_content = fix_xml_line(content)
            
            data_dict = xmltodict.parse(fixed_content)
        return data_dict
    except Exception as e:
        raise ValueError(f"Error in converting xml to json - {str(e)}")

def get_tables_data(root):
    logger.info("Started extracting table-column metadata from XML root.")
    try:
        table_column_data = {}
        total_records = 0

        for metadata_record in root.findall(".//metadata-record[@class='column']"):
            table_name = metadata_record.findtext("parent-name")
            column_name = metadata_record.findtext("local-name")

            if not table_name or not column_name:
                logger.warning("Skipping a column due to missing table name or column name.")
                continue

            table_name = table_name.strip('[]')
            column_name = column_name.strip('[]')
            total_records += 1

            if table_name in table_column_data:
                table_column_data[table_name].append(column_name)
                logger.info(f"Appended column '{column_name}' to existing table '{table_name}'.")
            else:
                table_column_data[table_name] = [column_name]
                logger.info(f"Added new table '{table_name}' with column '{column_name}'.")

        logger.info(f"Completed extraction of table-column metadata. Total tables: {len(table_column_data)}, total columns: {total_records}")
        return table_column_data

    except Exception as e:
        logger.error(f"Error while extracting table-column data: {str(e)}")
        raise ValueError(f"Error in getting table column data - {str(e)}")

    
def get_formula_columns(input_string):
    return re.findall(r'\[(.*?)\]', input_string)

def find_tables_with_columns(table_column_data, columns_to_match):
    matching_tables = []
    
    for table, columns in table_column_data.items():
        if any(column in columns for column in columns_to_match):
            matching_tables.append(table)
    
    return matching_tables

def resolve_table_name(calc_column_list, table_column_data, calc_fields):
    table_name = find_tables_with_columns(table_column_data, calc_column_list)
    if table_name:
        return table_name[0]
    else:
        col_value = calc_column_list[0]
        calc_formula = ''
        for data in calc_fields:
            if col_value == data.get('caption'):
                calc_formula = data.get('formula')
                break
        calc_column_list = get_formula_columns(calc_formula)
        return resolve_table_name(calc_column_list, table_column_data, calc_fields)
    
def get_openai_pbi_response(tab_formula, table_info):
    try:
        response = client.chat.completions.create(
        model="gpt-4o",
        messages=[
            {
            "role": "system",
            "content": [
                {
                "type": "text",
                "text": '''You are an expert in both Tableau and Power BI. Your task is to convert a list of Tableau calculated fields into their corresponding Power BI DAX functions. For each Tableau formula, you must accurately translate it into the equivalent DAX expression in Power BI, ensuring that the table and column names are maintained exactly as provided.

                Input Format:
                Tableau Formula: A list of Tableau calculated fields (formulae) that need to be converted.

                Table Information: A dictionary containing the table names and their associated columns.

                Output Instructions:
                DAX Conversion: Provide the exact Power BI DAX equivalent for each Tableau formula.

                Preserve Structure: The structure of the Tableau formula must be maintained without alteration in the DAX conversion.

                No Additional Aggregations: Do not introduce any new aggregations unless explicitly specified in the Tableau formula.

                Exact Field Mapping: Ensure the translated DAX formula references the correct table and field names from the provided table information.

                Formatting: Output the results in the following JSON format, ensuring the Power BI DAX formula is in a single line:

                {
                "json_output": [
                    {
                    "tableau_formula": "<Tableau formula>",
                    "pbi_dax": "<Power BI DAX formula>"
                    }
                ]
                }

                Make sure all formulas are carefully translated to maintain functional equivalence between Tableau and Power BI without skipping any.'''
                }
            ]
            },
            {
            "role": "user",
            "content": [
                {
                "type": "text",
                "text": f"Tableau Formula:{tab_formula}, Table Information:{table_info}"
                }
            ]
            }
        ],
        response_format={
            "type": "json_object"
        }
        )
        res = response.choices[0].message.content
        return res
    except APIConnectionError as e:
        print(f"Issue in connecting to OpenAI API: {str(e)}")
        raise ValueError('Issue in connecting to OpenAI API')
    except AuthenticationError as e:
        print(f"OpenAI key or token was invalid, expired, or revoked.: {str(e)}")
        raise ValueError('OpenAI key or token was invalid, expired, or revoked.')  

def chunk_list(data, chunk_size):
    """Splits a list into chunks of a specified size."""
    it = iter(data)
    return iter(lambda: list(islice(it, chunk_size)), [])

def process_table_column_data(table_column_data, calc_fields):
    for calc in calc_fields:
        calc_name = calc.get('name', '')
        calc_caption = calc.get('caption', '')
        calc_formula = calc.get("formula", "")
        formula_columns = get_formula_columns(calc_formula)
        if 'Parameter' in calc_name or not formula_columns:
            continue
        table_name = resolve_table_name(formula_columns, table_column_data, calc_fields)
        table_column_data[table_name].append(calc_caption)

    return table_column_data

def get_calculations_data_excel(twb_file_path):
    logger.info(f"Starting calculation data extraction from: {twb_file_path}")

    try:
        tree = ET.parse(twb_file_path)
        root = tree.getroot()
    except Exception as e:
        logger.error(f"Failed to parse XML file: {twb_file_path}. Error: {str(e)}")
        raise

    logger.info("Extracting table-column metadata from XML root.")
    table_column_data = get_tables_data(root)

    logger.info("Extracting calculated fields from XML root.")
    calc_fields = calculate_fields(root)

    logger.info("Merging calculated fields with table-column data.")
    table_column_data = process_table_column_data(table_column_data, calc_fields)

    output_json = []
    tableau_formulae = [calc.get('formula', '') for calc in calc_fields]
    pbi_dax_data = []

    logger.info(f"Total formulas to process: {len(tableau_formulae)}")
    for idx, chunk in enumerate(chunk_list(tableau_formulae, 10)):
        try:
            logger.info(f"Processing chunk {idx + 1} with {len(chunk)} formulas.")
            response = get_openai_pbi_response(chunk, table_column_data)
            pbi_dax_chunk = json.loads(response)
            logger.info(f"OpenAI DAX chunk response: {pbi_dax_chunk}")
            pbi_dax_data.extend(pbi_dax_chunk.get("json_output", []))
        except json.JSONDecodeError as e:
            logger.error(f"JSON decoding failed for chunk {idx + 1}. Error: {str(e)}")

    dax_lookup = {
        dax["tableau_formula"].replace(" ", ""): dax["pbi_dax"]
        for dax in pbi_dax_data
    }

    logger.info("Constructing final output JSON from calculated field mappings.")
    for calc in calc_fields:
        calc_name = calc.get('name', '')
        calc_caption = calc.get('caption', '')
        calc_formula = calc.get('formula', '')
        data_type = calc.get('data_type', '')
        role = calc.get('role', '')
        column_type = calc.get('column_type', '')

        formula_columns = get_formula_columns(calc_formula)
        if 'Parameter' in calc_name or not formula_columns:
            logger.info(f"Skipping calculation '{calc_name}' - parameter or no formula columns.")
            continue

        pbi_dax_formula = dax_lookup.get(calc_formula.replace(" ", ""), 'Failed to convert formula')
        table_name = resolve_table_name(formula_columns, table_column_data, calc_fields)

        if " " in calc_caption:
            calc_caption = f"'{calc_caption}'"

        logger.info(f"Generated DAX for calc '{calc_caption}' in table '{table_name}': {pbi_dax_formula}")
        output_json.append({
            "caption": calc_caption,
            "formula": pbi_dax_formula,
            "table_name": table_name,
            "data_type": data_type,
            "role": role,
            "type": column_type
        })

    logger.info(f"Completed processing. Total calculation fields generated: {len(output_json)}")
    return output_json


def get_dax_calculations_for_excel_model(twb_file_path: str) -> list: # New name
    """
    Extracts Tableau calculated fields from a TWB file, converts them to DAX
    using an external service (e.g., OpenAI), and maps them to their target
    excel-based tables for Power BI TMDL generation.

    Args:
        twb_file_path: Absolute path to the Tableau Workbook (TWB) file.

    Returns:
        A list of dictionaries, where each dictionary represents a DAX calculation
        ready to be injected into a TMDL table file. Structure:
        [
            {
                "caption": "<DAX Measure/Column Name (quoted if needed)>",
                "formula": "<Generated DAX Formula>",
                "table_name": "<Target TMDL Table Name (e.g., 'Sales Data.excel')>",
                "data_type": "<Original TWB data_type>",
                "role": "<Original TWB role>",
                "type": "<Original TWB column_type (quantitative/nominal)>"
            }, ...
        ]
    """
    logger.info(f"{twb_file_path} - excel DAX Gen - Starting DAX calculation data extraction.")

    try:
        tree = ET.parse(twb_file_path)
        root = tree.getroot()
    except Exception as e:
        logger.error(f"{twb_file_path} - excel DAX Gen - Failed to parse XML. Error: {str(e)}", exc_info=True)
        raise # Re-raise to be handled by caller

    # 1. Get physical table structure (which excels and their columns exist)
    # This is crucial for OpenAI context and for resolving table names.
    try:
        physical_table_column_data = get_tables_data(root) # Should return {'excelFile1.excel': ['ColA', 'ColB'], ...}
        if not physical_table_column_data:
            logger.warning(f"{twb_file_path} - excel DAX Gen - No physical table/column data extracted from TWB. DAX context might be incomplete.")
    except Exception as e:
        logger.error(f"{twb_file_path} - excel DAX Gen - Error in get_tables_data: {str(e)}", exc_info=True)
        physical_table_column_data = {} # Proceed with empty if error, but log it

    # 2. Extract all Tableau calculated fields
    try:
        all_tableau_calc_fields = calculate_fields(root) # Returns list of dicts: {name, caption, formula, data_type, role, column_type}
        if not all_tableau_calc_fields:
            logger.info(f"{twb_file_path} - excel DAX Gen - No Tableau calculated fields found in TWB.")
            return [] # No calculations to process
    except Exception as e:
        logger.error(f"{twb_file_path} - excel DAX Gen - Error in calculate_fields: {str(e)}", exc_info=True)
        return []


    # 3. Augment physical_table_column_data with calculated field *captions*
    # This is done because OpenAI might see formulas referencing other calculated fields by their caption.
    # process_table_column_data modifies its input, so pass a copy if original is needed elsewhere.
    try:
        # Create a context for OpenAI that includes physical columns and captions of other calcs
        # as if they were columns in their respective tables.
        openai_context_table_data = json.loads(json.dumps(physical_table_column_data)) # Deep copy
        openai_context_table_data = process_table_column_data(openai_context_table_data, all_tableau_calc_fields)
        logger.info(f"{twb_file_path} - excel DAX Gen - Augmented table data for OpenAI context.")
    except Exception as e:
        logger.error(f"{twb_file_path} - excel DAX Gen - Error in process_table_column_data: {str(e)}", exc_info=True)
        openai_context_table_data = physical_table_column_data # Fallback to physical only


    # 4. Prepare Tableau formulas for DAX conversion service
    tableau_formulas_to_convert = [
        calc.get('formula', '') 
        for calc in all_tableau_calc_fields 
        if calc.get('formula', '') # Only include if formula exists
    ]
    if not tableau_formulas_to_convert:
        logger.info(f"{twb_file_path} - excel DAX Gen - No non-empty formulas to convert after filtering.")
        return [] # No actual formulas to send for conversion


    # 5. Call DAX conversion service (e.g., OpenAI) in chunks
    converted_dax_data_list = [] # Stores {'tableau_formula': '...', 'pbi_dax': '...'}
    logger.info(f"{twb_file_path} - excel DAX Gen - Total Tableau formulas for DAX conversion: {len(tableau_formulas_to_convert)}")
    
    # Define a chunk size, e.g., 10 formulas per OpenAI call
    CHUNK_SIZE = 10 
    for idx, formula_chunk in enumerate(chunk_list(tableau_formulas_to_convert, CHUNK_SIZE)):
        try:
            logger.info(f"{twb_file_path} - excel DAX Gen - Processing OpenAI chunk {idx + 1}/{ (len(tableau_formulas_to_convert) + CHUNK_SIZE -1) // CHUNK_SIZE } with {len(formula_chunk)} formulas.")
            # get_openai_pbi_response needs the table context for accurate conversion
            openai_response_str = get_openai_pbi_response(formula_chunk, openai_context_table_data)
            if openai_response_str:
                openai_response_json = json.loads(openai_response_str)
                # logger.debug(f"{twb_file_path} - excel DAX Gen - OpenAI DAX chunk response: {openai_response_json}")
                converted_dax_data_list.extend(openai_response_json.get("json_output", []))
            else:
                logger.warning(f"{twb_file_path} - excel DAX Gen - OpenAI returned empty response for chunk {idx + 1}.")
        except json.JSONDecodeError as e_json:
            logger.error(f"{twb_file_path} - excel DAX Gen - JSON decoding failed for OpenAI response (chunk {idx + 1}). Error: {str(e_json)}")
        except Exception as e_openai: # Catch other OpenAI call errors
            logger.error(f"{twb_file_path} - excel DAX Gen - Error calling OpenAI for chunk {idx + 1}. Error: {str(e_openai)}", exc_info=True)

    # 6. Create a lookup for converted DAX formulas
    # Normalize Tableau formula (e.g., remove spaces) for robust matching
    dax_formula_lookup = {
        dax_item["tableau_formula"].replace(" ", "").lower(): dax_item["pbi_dax"]
        for dax_item in converted_dax_data_list if "tableau_formula" in dax_item and "pbi_dax" in dax_item
    }

    # 7. Construct the final output JSON list
    output_payload_for_tmdl = []
    logger.info(f"{twb_file_path} - excel DAX Gen - Mapping converted DAX to original calculated fields.")
    for calc_field_info in all_tableau_calc_fields:
        original_tableau_formula = calc_field_info.get('formula', '')
        calc_caption = calc_field_info.get('caption', calc_field_info.get('name')) # Prefer caption
        
        # Skip if no formula or if it's a parameter (parameters are handled differently)
        if not original_tableau_formula or 'Parameter' in calc_field_info.get('name', ''):
            logger.debug(f"{twb_file_path} - excel DAX Gen - Skipping '{calc_caption}': No formula or is a parameter.")
            continue

        # Retrieve the converted DAX formula
        normalized_tf_formula_key = original_tableau_formula.replace(" ", "").lower()
        pbi_dax_expression = dax_formula_lookup.get(normalized_tf_formula_key)

        if not pbi_dax_expression:
            logger.warning(f"{twb_file_path} - excel DAX Gen - DAX conversion not found for Tableau calc '{calc_caption}' (Formula: {original_tableau_formula}). Assigning placeholder.")
            pbi_dax_expression = f"// Placeholder: DAX conversion FAILED for Tableau formula: {original_tableau_formula}"

        # Determine the target TMDL table name for this DAX calculation
        # This relies on resolve_table_name using the physical column structure
        try:
            referenced_cols_in_tf = get_formula_columns(original_tableau_formula) # Extracts [ColA], [ColB]
             # Clean them: ColA, ColB
            cleaned_referenced_cols = [col.strip("[]") for col in referenced_cols_in_tf]

            if not cleaned_referenced_cols: # If calc is like "1+1", may not have refs
                 logger.warning(f"{twb_file_path} - excel DAX Gen - Calc '{calc_caption}' has no column references. Cannot reliably assign to a table. Skipping.")
                 # Or assign to a default/first table if that's desired, but risky.
                 continue


            target_tmdl_table_name = resolve_table_name(
                cleaned_referenced_cols,
                physical_table_column_data, # Use the original physical structure
                all_tableau_calc_fields     # For recursive resolution if needed
            )
        except Exception as e_resolve:
            logger.error(f"{twb_file_path} - excel DAX Gen - Could not resolve table for calc '{calc_caption}'. Cols: {cleaned_referenced_cols}. Error: {e_resolve}. Skipping.", exc_info=True)
            continue

        if not target_tmdl_table_name:
            logger.warning(f"{twb_file_path} - excel DAX Gen - Table name not resolved for calc '{calc_caption}'. Cols: {cleaned_referenced_cols}. Skipping.")
            continue
        
        # Prepare caption for TMDL (measure/column name) - ensure quoting for spaces/special chars
        tmdl_calc_name = calc_caption.strip("[]") # Remove any Tableau-style brackets
        if re.search(r"[^a-zA-Z0-9_]", tmdl_calc_name) or " " in tmdl_calc_name:
            tmdl_calc_name = f"'{tmdl_calc_name}'"

        output_payload_for_tmdl.append({
            "caption": tmdl_calc_name, # Name for Power BI measure/column
            "formula": pbi_dax_expression,
            "table_name": target_tmdl_table_name, # Target TMDL table (e.g., 'Sample - Superstore.excel')
            "data_type": calc_field_info.get('data_type', ''), # From TWB
            "role": calc_field_info.get('role', ''), # From TWB
            "type": calc_field_info.get('column_type', '') # From TWB (quantitative/nominal)
        })

    logger.info(f"{twb_file_path} - excel DAX Gen - Completed. Total DAX items prepared for TMDL: {len(output_payload_for_tmdl)}")
    return output_payload_for_tmdl