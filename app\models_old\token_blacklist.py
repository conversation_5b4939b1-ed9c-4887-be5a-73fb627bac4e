# models/token_blacklist.py

from sqlalchemy import Column, String, DateTime
from datetime import datetime
from app.core import Base, scoped_context

class TokenBlacklist(Base):
    __tablename__ = "token_blacklist"

    token_hash = Column(String, primary_key=True)
    expires_at = Column(DateTime, nullable=False)
    blacklisted_at = Column(DateTime, default=datetime.utcnow)


class TokenBlacklistManager:
    @staticmethod
    def add_token(token_hash: str, expires_at: datetime):
        """Adds a token to the blacklist."""
        with scoped_context() as session:
            new_entry = TokenBlacklist(token_hash=token_hash, expires_at=expires_at)
            session.add(new_entry)
            session.commit()

    @staticmethod
    def is_token_blacklisted(token_hash: str) -> bool:
        """Checks if a token is blacklisted."""
        with scoped_context() as session:
            return session.query(TokenBlacklist).filter(
                TokenBlacklist.token_hash == token_hash,
                TokenBlacklist.expires_at > datetime.utcnow()
            ).first() is not None

    @staticmethod
    def remove_expired_tokens():
        """Removes expired tokens from the blacklist."""
        with scoped_context() as session:
            session.query(TokenBlacklist).filter(TokenBlacklist.expires_at < datetime.utcnow()).delete()
            session.commit()