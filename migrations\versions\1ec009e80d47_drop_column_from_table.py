"""drop column from table

Revision ID: 1ec009e80d47
Revises: e05795a81690
Create Date: 2025-04-04 12:21:01.151623

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1ec009e80d47'
down_revision: Union[str, None] = 'e05795a81690'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.drop_column('data_sources', 'key')


def downgrade() -> None:
    op.add_column('data_sources', sa.Column('key', sa.String))