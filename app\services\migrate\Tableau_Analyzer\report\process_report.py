from .worksheet import process_worksheets
from app.core import TABLEAU_WORKSHEET_DIMENSIONS
from app.core.enums import Power<PERSON>TemplateKeys

def process_report_data(twb_file_path, chart_types):

    overall_report_data = []
    worksheet_data = process_worksheets(twb_file_path, chart_types)
    for worksheet in worksheet_data:
        worksheet[PowerBITemplateKeys.DIMENSIONS.value] = TABLEAU_WORKSHEET_DIMENSIONS
        overall_report_data.append(worksheet)

    #TO DO : Dashboard processing
    # dashboard_data = process_dashboards(twb_file_path, chart_types)
    
    return overall_report_data