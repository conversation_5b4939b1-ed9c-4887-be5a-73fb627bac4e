from fastapi import (
    <PERSON><PERSON><PERSON><PERSON>,
    Header,
    <PERSON>,
    Query,
    HTTPException,
    Depends
)
from fastapi.responses import JSONResponse
from app.services.migrate.migration_processor import MigrateProcessor
from app.schemas.migrate import WorkbooksRequest
from app.models_old.user import UserOld
from app.core.dependencies import get_current_user


migrate_router = APIRouter()

@migrate_router.post("/tableau-to-powerbi")
async def convert_tableau_to_powerbi(
    body: WorkbooksRequest,
    is_upload_file: bool = Query(False),
    user: UserOld = Depends(get_current_user)
    #   authorization: str = Header(...),
    #   email: str = Header(...),
):
    """ 
    API for migrating files from tableau to powerBI.

    Parameters
    ----------
    body : WorkbooksRequest
        Contains a list of workbook IDs to be migrated.
    authorization : str
        Authorization token passed in the request header.
    email : str
        UserOld's email passed in the request header.

    Returns
    -------
    A JSON response with the migration result and error information.
    """
    if is_upload_file:
        if not body.s3_paths:
            raise HTTPException(status_code=400, detail="s3_paths are required when is_upload_file is True.")
        response = await MigrateProcessor.tableau_to_powerbi(
            # authorization=authorization,
            # email=email,
            s3_paths=body.s3_paths,
            is_upload=True
        )
    else:
        if not body.workbook_ids:
            raise HTTPException(status_code=400, detail="workbook_ids are required when is_upload_file is False.")
        response = await MigrateProcessor.tableau_to_powerbi(
            # authorization,
            # email,
            workbooks=body.workbook_ids
        )
    return JSONResponse(content = {"data": response.data,"error": response.error},status_code = response.status_code)