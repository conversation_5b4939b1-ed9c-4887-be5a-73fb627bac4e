from ..core import *
from app.core import stacked_bar_chart_json
import json, uuid
from app.core.enums import (
    ChartType, PowerBITemplateKeys, TableauXMLTags,
    PowerBIReportKeys, PowerBIChartTypes, VisualRequest
)
from app.services.migrate.Tableau_Analyzer.report import (
    remove_duplicate_fields,
    extract_encodings_data
)


def get_stacked_bar_report(rows, cols, pane_encodings,table_column_data, datasource_col_list,orderby,worksheet_name):
    try:
        stackedbar_result=[]
        from_list,select_list,category_list,y_ref_list,series_ref_list,orderby_list,table_names,select_list_no_doops,column_properties_json= [],[],[],[],[],[],[],[],{}
        row_list=get_fields_data(rows)
        cols_list=get_fields_data(cols)
        orderby=[orderby] if isinstance(orderby,dict) else [orderby]
        series = pane_encodings.get('color', {}).get('@column')
        series_list=get_fields_data(series)
        for rows in row_list:
            rows=rows.strip()
            y_ref_list,y_tab_name=query_ref_list(rows,table_column_data,datasource_col_list,y_ref_list)
            select_list=get_select_json_list(*list(get_calc_filter_column(rows,table_column_data,datasource_col_list)),select_list)
            column_properties_json.update(get_column_properties_json(*list(get_calc_filter_column(rows, table_column_data,datasource_col_list))))
            if y_tab_name not in table_names:table_names.append(y_tab_name)
        
        for rows in cols_list:
            category_list,cat_tab_name=query_ref_list(rows,table_column_data,datasource_col_list,category_list)
            select_list=get_select_json_list(*list(get_calc_filter_column(rows,table_column_data,datasource_col_list)),select_list)
            if cat_tab_name not in table_names:table_names.append(cat_tab_name)
        if series:
            for series in series_list:
                series_ref_list,series_tab_name=query_ref_list(series,table_column_data,datasource_col_list,series_ref_list)
                select_list=get_select_json_list(*list(get_calc_filter_column(series,table_column_data,datasource_col_list)),select_list)
                if series_tab_name not in table_names:table_names.append(series_tab_name)
        
        orderby=[orderby] if isinstance(orderby,dict) else orderby
        for eachorder in orderby:
            if eachorder.get("@using"):
                direction_order=1 if eachorder.get("@direction")=="ASC" else 2
                order_list=get_fields_data(eachorder.get("@using"))
                for category in order_list:
                    orderfil,ordercol,ordertab=get_calc_filter_column(eachorder.get("@using"),table_column_data,datasource_col_list)
                    orderby_list.extend(get_order_by_list(eachorder.get("@using"),table_column_data,datasource_col_list,direction_order))
                    if y_tab_name not in table_names:table_names.append(y_tab_name)
                    # select_list=get_select_json_list(*list(get_calc_filter_column(eachorder.get("@using"),table_column_data,datasource_col_list)),select_list)
        for ele in select_list:
            if ele not in select_list_no_doops:
                select_list_no_doops.append(ele)
        from_list=get_from_list(table_names)
        stackedbar_json = {
            "visual_config_name" : f'{str(uuid.uuid4()).replace("-","")[:20]}',
            "category_list" : json.dumps(category_list),
            "y_ref_list" : json.dumps(y_ref_list),
            "series_ref_list" : json.dumps(series_ref_list),
            "from_list" : json.dumps(from_list),
            "select_list" :json.dumps(select_list_no_doops),
            "orderby_list":json.dumps(orderby_list),
            "column_properties_json":json.dumps(column_properties_json)
        }
        stackedbar_result.append({"config":stackedbar_json,"template":stacked_bar_chart_json})
        return stackedbar_result
    except Exception as e:
        logger.error(f"---Error in generating stacked bar visual for {worksheet_name}--- {str(e)}")
        raise ValueError(f"Error in generating stacked bar visual for {worksheet_name} - {str(e)}")
    

"""

This chart is managed in bar_or_column_chart.py file.


"""


