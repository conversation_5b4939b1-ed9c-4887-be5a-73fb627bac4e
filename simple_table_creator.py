#!/usr/bin/env python3
"""
Simple script to create tables and import CSV data with better error handling
"""
import psycopg2
import csv
import sys

# Database connection parameters
DB_CONFIG = {
    'host': 'localhost',
    'port': '5432',
    'user': 'postgres',
    'password': 'postgres',
    'database': 'test'
}

def get_connection():
    """Get database connection"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except psycopg2.Error as e:
        print(f"Error connecting to database: {e}")
        return None

def safe_value(value, data_type='str'):
    """Convert CSV value to appropriate type, handling NULL strings"""
    if value is None or value == '' or value.upper() == 'NULL':
        return None
    
    if data_type == 'int':
        try:
            return int(value)
        except (ValueError, TypeError):
            return None
    elif data_type == 'bool':
        return value.lower() == 'true'
    else:
        return value

def create_and_populate_tables():
    """Create tables and populate with data"""
    conn = get_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        # Create schema
        cursor.execute("CREATE SCHEMA IF NOT EXISTS biport_dev")
        print("Schema created.")
        
        # Create ENUM types
        cursor.execute("""
            DO $$ BEGIN
                CREATE TYPE server_status AS ENUM ('ACTIVE', 'INACTIVE');
            EXCEPTION
                WHEN duplicate_object THEN null;
            END $$;
        """)
        
        cursor.execute("""
            DO $$ BEGIN
                CREATE TYPE server_type AS ENUM ('ONPREMISE', 'CLOUD');
            EXCEPTION
                WHEN duplicate_object THEN null;
            END $$;
        """)
        
        cursor.execute("""
            DO $$ BEGIN
                CREATE TYPE server_auth_type AS ENUM ('CREDENTIALS', 'PAT');
            EXCEPTION
                WHEN duplicate_object THEN null;
            END $$;
        """)
        
        # Drop tables if they exist (in reverse order due to foreign keys)
        tables_to_drop = [
            'biport_dev.report_details',
            'biport_dev.project_details', 
            'biport_dev.tableau_site_details',
            'biport_dev.tableau_server_credentials',
            'biport_dev.tableau_server_details',
            'biport_dev.users',
            'biport_dev.roles',
            'biport_dev.organization_details'
        ]
        
        for table in tables_to_drop:
            cursor.execute(f"DROP TABLE IF EXISTS {table} CASCADE")
        
        print("Dropped existing tables.")
        
        # 1. Organization Details Table
        cursor.execute("""
            CREATE TABLE biport_dev.organization_details (
                id UUID PRIMARY KEY,
                name VARCHAR NOT NULL,
                credits INTEGER,
                contact_person_name VARCHAR,
                mobile_number VARCHAR,
                address TEXT,
                service_type VARCHAR,
                created_by UUID,
                updated_by UUID,
                created_at TIMESTAMP,
                updated_at TIMESTAMP,
                is_deleted BOOLEAN DEFAULT FALSE
            )
        """)
        
        # 2. Roles Table
        cursor.execute("""
            CREATE TABLE biport_dev.roles (
                id UUID PRIMARY KEY,
                name VARCHAR NOT NULL,
                created_by UUID,
                updated_by UUID,
                created_at TIMESTAMP,
                updated_at TIMESTAMP,
                is_deleted BOOLEAN DEFAULT FALSE
            )
        """)
        
        # 3. Users Table (without manager_id foreign key initially)
        cursor.execute("""
            CREATE TABLE biport_dev.users (
                id UUID PRIMARY KEY,
                name VARCHAR NOT NULL,
                email VARCHAR UNIQUE NOT NULL,
                password_hash VARCHAR NOT NULL,
                phone_number VARCHAR UNIQUE NOT NULL,
                organization_id UUID REFERENCES biport_dev.organization_details(id),
                role_id UUID REFERENCES biport_dev.roles(id),
                manager_id UUID,
                created_by UUID,
                updated_by UUID,
                created_at TIMESTAMP,
                updated_at TIMESTAMP,
                is_deleted BOOLEAN DEFAULT FALSE
            )
        """)
        
        # 4. Tableau Server Details Table
        cursor.execute("""
            CREATE TABLE biport_dev.tableau_server_details (
                id UUID PRIMARY KEY,
                user_id UUID REFERENCES biport_dev.users(id),
                name VARCHAR NOT NULL,
                server_url VARCHAR NOT NULL,
                status server_status,
                type server_type NOT NULL,
                report_count INTEGER DEFAULT 0,
                project_count INTEGER DEFAULT 0,
                site_count INTEGER DEFAULT 0,
                created_by UUID,
                updated_by UUID,
                created_at TIMESTAMP,
                updated_at TIMESTAMP,
                is_deleted BOOLEAN DEFAULT FALSE,
                organization_id UUID REFERENCES biport_dev.organization_details(id)
            )
        """)
        
        # 5. Tableau Server Credentials Table
        cursor.execute("""
            CREATE TABLE biport_dev.tableau_server_credentials (
                id UUID PRIMARY KEY,
                server_id UUID REFERENCES biport_dev.tableau_server_details(id),
                pat_name VARCHAR,
                pat_secret VARCHAR,
                username VARCHAR,
                password VARCHAR,
                server_auth_type server_auth_type NOT NULL,
                created_by UUID,
                updated_by UUID,
                created_at TIMESTAMP,
                updated_at TIMESTAMP,
                is_deleted BOOLEAN DEFAULT FALSE
            )
        """)
        
        # 6. Tableau Site Details Table
        cursor.execute("""
            CREATE TABLE biport_dev.tableau_site_details (
                id UUID PRIMARY KEY,
                credentials_id UUID REFERENCES biport_dev.tableau_server_credentials(id),
                site_name VARCHAR NOT NULL,
                site_id VARCHAR NOT NULL,
                created_by UUID,
                updated_by UUID,
                created_at TIMESTAMP,
                updated_at TIMESTAMP,
                is_deleted BOOLEAN DEFAULT FALSE
            )
        """)
        
        # 7. Project Details Table
        cursor.execute("""
            CREATE TABLE biport_dev.project_details (
                id UUID PRIMARY KEY,
                name VARCHAR NOT NULL,
                is_upload BOOLEAN,
                site_id UUID REFERENCES biport_dev.tableau_site_details(id),
                server_id UUID REFERENCES biport_dev.tableau_server_details(id),
                parent_id UUID,
                user_id UUID REFERENCES biport_dev.users(id),
                assigned_to UUID REFERENCES biport_dev.users(id),
                created_at TIMESTAMP,
                updated_at TIMESTAMP,
                is_deleted BOOLEAN DEFAULT FALSE
            )
        """)
        
        # 8. Report Details Table
        cursor.execute("""
            CREATE TABLE biport_dev.report_details (
                id UUID PRIMARY KEY,
                name VARCHAR NOT NULL,
                report_id UUID,
                project_id UUID REFERENCES biport_dev.project_details(id),
                is_analyzed BOOLEAN DEFAULT FALSE,
                analyzed_status VARCHAR,
                is_converted BOOLEAN DEFAULT FALSE,
                converted_status VARCHAR,
                is_migrated BOOLEAN DEFAULT FALSE,
                migrated_status VARCHAR,
                unit_tested BOOLEAN DEFAULT FALSE,
                uat_tested BOOLEAN DEFAULT FALSE,
                deployed BOOLEAN DEFAULT FALSE,
                is_scoped BOOLEAN DEFAULT FALSE,
                semantic_type VARCHAR,
                has_semantic_model BOOLEAN DEFAULT FALSE,
                view_count INTEGER DEFAULT 0
            )
        """)
        
        conn.commit()
        print("All tables created successfully.")

        # Now import data
        print("Starting data import...")

        # Import organization_details
        print("Importing organization_details...")
        with open('data/organization_details.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                cursor.execute("""
                    INSERT INTO biport_dev.organization_details
                    (id, name, credits, contact_person_name, mobile_number, address, service_type,
                     created_by, updated_by, created_at, updated_at, is_deleted)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    safe_value(row['id']), safe_value(row['name']),
                    safe_value(row['credits'], 'int'),
                    safe_value(row['contact_person_name']), safe_value(row['mobile_number']),
                    safe_value(row['address']), safe_value(row['service_type']),
                    safe_value(row['created_by']), safe_value(row['updated_by']),
                    safe_value(row['created_at']), safe_value(row['updated_at']),
                    safe_value(row['is_deleted'], 'bool')
                ))

        # Import roles
        print("Importing roles...")
        with open('data/roles.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                cursor.execute("""
                    INSERT INTO biport_dev.roles
                    (id, name, created_by, updated_by, created_at, updated_at, is_deleted)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """, (
                    safe_value(row['id']), safe_value(row['name']),
                    safe_value(row['created_by']), safe_value(row['updated_by']),
                    safe_value(row['created_at']), safe_value(row['updated_at']),
                    safe_value(row['is_deleted'], 'bool')
                ))

        # Import users (without manager_id first)
        print("Importing users...")
        with open('data/users.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                cursor.execute("""
                    INSERT INTO biport_dev.users
                    (id, name, email, password_hash, phone_number, organization_id, role_id,
                     created_by, updated_by, created_at, updated_at, is_deleted)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    safe_value(row['id']), safe_value(row['name']), safe_value(row['email']),
                    safe_value(row['password_hash']), safe_value(row['phone_number']),
                    safe_value(row['organization_id']), safe_value(row['role_id']),
                    safe_value(row['created_by']), safe_value(row['updated_by']),
                    safe_value(row['created_at']), safe_value(row['updated_at']),
                    safe_value(row['is_deleted'], 'bool')
                ))

        # Update users with manager_id
        print("Updating users with manager_id...")
        with open('data/users.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                if safe_value(row['manager_id']):
                    cursor.execute("""
                        UPDATE biport_dev.users
                        SET manager_id = %s
                        WHERE id = %s
                    """, (
                        safe_value(row['manager_id']), safe_value(row['id'])
                    ))

        conn.commit()
        print("Data import completed successfully.")
        return True

    except psycopg2.Error as e:
        print(f"Error: {e}")
        conn.rollback()
        return False
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    if create_and_populate_tables():
        print("Database setup completed successfully!")
    else:
        print("Database setup failed!")
        sys.exit(1)
