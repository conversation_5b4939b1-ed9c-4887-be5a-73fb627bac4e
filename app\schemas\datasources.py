from pydantic import BaseModel
from typing import Optional

class AddDataSourceDetails(BaseModel): 
    user_id: int
    ds_type: str
    ds_details: dict

class RemoveUserDetails(BaseModel): 
    user_id: int
    ds_type: Optional[str] = None
    ds_details: Optional[dict] = None

class RemoveSelectedDataSourceDetails(BaseModel): 
    user_id: int
    ds_type: str
    ds_details: dict

class GetUserDetails(BaseModel): 
    user_id: int
    ds_type: Optional[str] = None

class GetSelectedDSdetails(BaseModel): 
    user_id: int
    ds_type: str


class UpdateExistingDataSource(BaseModel): 
    user_id: int
    ds_type: str
    old_ds: dict
    new_ds: dict


class DataSourceFormation: 
    def __init__(self, user_id, mssql = None, postgresql = None, mysql = None, oracle = None, sqlite = None): 
        self.user_id = user_id
        self.mssql = mssql
        self.postgresql = postgresql
        self.mysql = mysql
        self.oracle = oracle
        self.sqlite = sqlite
