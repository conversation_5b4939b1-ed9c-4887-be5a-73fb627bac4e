import xml.etree.ElementTree as ET
from .table_columns_handler import fetch_table_columns_data
from app.core.enums import Datasource

async def process_datasources(twb_file_path):
    tree = ET.parse(twb_file_path)
    root = tree.getroot()
    datasources = root.find(Datasource.DATASOURCES.value).findall(Datasource.DS.value)
    table_column_data = await fetch_table_columns_data(datasources)
    return table_column_data
    
