import re
from app.core import logger
from  app.core.enums import (
    GeneralKeys as GS,
    Datasource as DS,
    TableConstants
)
from app.core.regex_enums import Regex as RE
from app.core.constants import DATASOURCE_TYPES
import xml.etree.ElementTree as ET
import os
import os
def extract_datasource_tables_and_columns(root):
    """
    Extract tables and their columns from the given XML root element representing datasources.

    This function processes an XML structure that defines one or more datasources,
    each potentially containing definitions for tables and their columns. It parses
    this information to provide structured data about the tables, connection details
    for each datasource, and the human-readable names (captions) of the datasources.

    Args:
        xml_root (xml.etree.ElementTree.Element): The root XML element of the
            document or fragment containing the datasource definitions. This element
            is the starting point for traversing the XML to find relevant data.

    Returns:
        tuple: A tuple containing three elements in the following order:

        1.  list of extracted table data dictionaries (list[dict]):

        2.  list of connection detail dictionaries (list[dict]):

        3.  list of datasource names (captions) (list[str]):
    """
    logger.info("Extracting tables and columns from datasources.")

    extracted_table_data = []
    connection_details = []
    datasource_names = []
    seen_tables = set()

    datasources = root.find(GS.Datasources.value)
    if datasources is None:
        logger.warning("No 'datasources' element found in the XML.")
        return extracted_table_data, connection_details, datasource_names

    for datasource in datasources.findall(DS.DS.value):
        named_connections = datasource.find(DS.NAMED_CONNECTIONS.value)
        if not named_connections:
            logger.warning(f"No named connections found for datasource")
            continue

        file_name_tag = datasource.find(".//connection")
        file_name = file_name_tag.attrib.get(GS.FILE_NAME.value, "") if file_name_tag is not None else ""
        ds_name = os.path.splitext(os.path.basename(file_name))[0]
        if ds_name and ds_name not in datasource_names:
            datasource_names.append(ds_name)

        extract_tag = datasource.find(DS.EXTRACT.value)
        is_extract = extract_tag is not None
        is_hyper = (
            extract_tag is not None and
            extract_tag.find(GS.CONNECTION.value) is not None and
            extract_tag.find(GS.CONNECTION.value).get(GS.CLASS.value, "").lower() == GS.HYPER.value
        )

        connection_type = GS.EXTRACT.value if is_extract else GS.LIVE.value
        extract_class = GS.HYPER.value if is_hyper else ""


        for named_connection in named_connections.findall(GS.NAMED_CONNECTION.value):
            connection = named_connection.find(DS.CONNECTION.value)
            if connection is None:
                logger.warning(f"No connection element found in named connection for datasource")
                continue

            connection.attrib[GS.CONNECTION_TYPE.value] = connection_type
            connection.attrib[GS.EXTRACT_CLASS.value] = extract_class
            connection_details.append(connection.attrib)

            if connection.get(GS.FILE_NAME.value):
                logger.info(f"Excel input detected: filename={connection.get(GS.FILE_NAME.value)}")
                extracted_table_data.extend(
                    extract_tables_from_excel(datasource, seen_tables)
                )

            elif connection.get(GS.DB_NAME.value):
                logger.info(f"SQL input detected: dbname={connection.get(GS.DB_NAME.value)}")
                extracted_table_data.extend(
                    extract_tables_from_sql(datasource, seen_tables)
                )

            else:
                logger.warning(f"Unknown datasource connection type for datasource '{ds_name}'")
                extracted_table_data.extend(
                    extract_tables_from_unknown(datasource, seen_tables)
                )

    logger.info("Extraction of tables and columns completed.")
    return extracted_table_data, connection_details, datasource_names

def extract_tables_from_excel(datasource, seen_tables):
    """
    Extract tables and columns from an Excel datasource element.

    Args:
        datasource (Element): XML element representing a datasource.
        seen_tables (set): Set of table names already processed.

    Returns:
        list: List of dicts representing tables and their columns.
    """
    extracted_table_data = []

    for relation in datasource.findall(DS.RELATION.value):
        table_name = relation.get(GS.TABLE.value)
        if not table_name:
            logger.warning("Relation element missing 'table' attribute in Excel datasource.")
            continue

        clean_table_name = table_name.strip(RE.STRIP_SQUARE_BRACKETS.value).replace(RE.TAB_REPLACE.value, RE.EMPTY.value)
        if clean_table_name in seen_tables:
            continue
        seen_tables.add(clean_table_name)

        columns = _extract_columns_from_relation(relation)
        extracted_table_data.append({
            GS.NAME.value: clean_table_name,
            TableConstants.TYPE_KEY: relation.get(GS.TYPE.value, TableConstants.TABLE_TYPE_DEFAULT),
            TableConstants.COLUMNS_KEY: columns
        })

    return extracted_table_data


def _extract_columns_from_relation(relation):
    """
    Helper to extract column info from a relation element.

    Args:
        relation (Element): XML relation element.

    Returns:
        list: List of dicts with column name, caption, datatype.
    """
    columns = []
    for col in relation.findall(DS.COLUMNS_COLUMN.value):
        col_info = extract_column_info(col)
        if col_info:
            columns.append(col_info)

    if not columns:
        columns = [{
            GS.NAME.value: GS.NO_COLUMNS_AVAILABLE.value,
            GS.CAPTION.value: GS.NO_COLUMNS_AVAILABLE.value,
            GS.DATATYPE.value: GS.UNKNOWN.value
        }]

    return columns

def extract_column_info(col):
    """
    Extracts column info safely and logs warnings if required data missing.

    Args:
        col (Element): XML column element.

    Returns:
        dict or None: Column dictionary or None if critical info missing.
    """
    column_name = col.get(GS.NAME.value)
    if not column_name:
        logger.warning("Column element missing 'name' attribute.")
        return None

    column_caption = col.get(GS.CAPTION.value, column_name)
    column_datatype = col.get(GS.DATATYPE.value, GS.UNKNOWN.value)

    return {
        GS.NAME.value: column_name,
        GS.CAPTION.value: column_caption,
        GS.DATATYPE.value: column_datatype
    }


def extract_tables_from_sql(datasource, seen_tables):
    """
    Extract tables and columns from a SQL datasource element.

    Args:
        datasource (Element): XML element representing datasource.
        seen_tables (set): Set of processed table names.

    Returns:
        list: List of tables with columns extracted.
    """
    extracted_table_data = []

    for relation in datasource.findall(DS.RELATION.value):
        table_name = relation.get(GS.TABLE.value)
        if not table_name:
            logger.warning("Relation element missing 'table' attribute in SQL datasource.")
            continue
        if table_name in seen_tables:
            continue
        seen_tables.add(table_name)

        clean_table_name = table_name.split(RE.DOT.value)[-1].strip(RE.SQUARE_BRACKETS.value)
        columns = []

        for map_tag in datasource.findall(DS.COLS_MAP.value):
            map_value = map_tag.get(GS.VALUE.value, RE.EMPTY.value)
            map_table = map_value.split(RE.DOT.value)[0].strip(RE.SQUARE_BRACKETS.value)

            if map_table == clean_table_name:
                column_name = map_tag.get(GS.KEY.value, "").strip(RE.SQUARE_BRACKETS.value)
                if not column_name:
                    logger.warning(f"Missing column key in mapping for table '{clean_table_name}'")
                    continue
                column_caption = column_name
                column_datatype = extract_column_datatype(datasource, column_name)  # Assumes exists
                columns.append({
                    GS.NAME.value: column_name,
                    GS.CAPTION.value: column_caption,
                    GS.DATATYPE.value: column_datatype
                })

        if not columns:
            columns = [{
                GS.NAME.value: GS.NO_COLUMNS_AVAILABLE.value,
                GS.CAPTION.value: GS.NO_COLUMNS_AVAILABLE.value,
                GS.DATATYPE.value: GS.UNKNOWN.value
            }]

        extracted_table_data.append({
            GS.NAME.value: table_name,
            TableConstants.TYPE_KEY: relation.get(GS.TYPE.value, TableConstants.TABLE_TYPE_DEFAULT),
            TableConstants.COLUMNS_KEY: columns
        })

    return extracted_table_data


def extract_tables_from_unknown(datasource, seen_tables):
    """
    Extract tables and columns from an unknown datasource type.

    Args:
        datasource (Element): XML element for datasource.
        seen_tables (set): Set of seen table names.

    Returns:
        list: Extracted tables with columns.
    """
    logger.warning("Unknown datasource connection type encountered.")
    extracted_table_data = []

    relations = datasource.findall(DS.RELATION.value)
    if not relations:
        return []

    for relation in relations:
        table_name = relation.get(GS.TABLE.value)
        if not table_name:
            logger.warning("Relation missing 'table' attribute in unknown datasource.")
            continue

        clean_table_name = table_name.strip(RE.STRIP_SQUARE_BRACKETS.value).replace(RE.TAB_REPLACE.value, RE.EMPTY.value)
        if clean_table_name in seen_tables:
            continue
        seen_tables.add(clean_table_name)

        columns = []
        for col in relation.findall(DS.COLUMNS_COLUMN.value):
            col_info = extract_column_info(col)
            if col_info:
                columns.append(col_info)

        if not columns:
            columns = [{
                GS.CAPTION.value: GS.NO_COLUMNS_AVAILABLE.value,
                GS.NAME.value: GS.NO_COLUMNS_AVAILABLE.value,
                GS.DATATYPE.value: GS.UNKNOWN.value
            }]

        nested_columns_group = {
            GS.NAME.value: GS.UNKNOWN.value,
            TableConstants.TYPE_KEY: GS.UNKNOWN.value,
            TableConstants.COLUMNS_KEY: columns
        }

        extracted_table_data.append({
            GS.NAME.value: clean_table_name,
            TableConstants.TYPE_KEY: relation.get(GS.TYPE.value, GS.UNKNOWN.value),
            TableConstants.COLUMNS_KEY: [nested_columns_group]
        })

    return extracted_table_data

def extract_column_datatype(datasource, column_name):
    logger.debug(f"Extracting datatype for column: {column_name}")
    for metadata_record in datasource.findall(DS.META_RECORD.value):
        if metadata_record.get(GS.CLASS.value) == GS.COLUMN.value:
            local_name = metadata_record.find(GS.LOCAL_NAME.value)
            local_type = metadata_record.find(GS.LOCAL_TYPE.value)
            if local_name is not None and local_name.text.strip(RE.SQUARE_BRACKETS.value) == column_name:
                if local_type is not None:
                    logger.debug(f"Datatype for column '{column_name}' is '{local_type.text}'.")
                    return local_type.text
    logger.warning(f"Datatype not found for column: {column_name}")
    return None

def extract_semantic_mapping(root):
    logger.info("Extracting semantic mapping.")
    semantic_mapping = {}
    for map_tag in root.findall(DS.COLS_MAP.value):
        semantic_mapping[map_tag.get(GS.KEY.value)] = map_tag.get(GS.VALUE.value)
    logger.info("Semantic mapping extraction completed.")
    return semantic_mapping

def extract_joins(root):
    logger.info("Extracting joins.")
    joins = {}
    for idx, relationship in enumerate(root.findall(DS.RELATIONSHIP.value)):
        try:
            first_end_point = relationship.find(DS.FIRST_END_POINT.value)
            second_end_point = relationship.find(DS.SECOND_ENC_POINT.value)
            expression = relationship.find(DS.EXPRESSION.value)

            first_table = first_end_point.get(GS.OBJECT_ID.value).split(RE.UNDER_SCORE.value)[0] if first_end_point is not None else GS.NULL.value
            second_table = second_end_point.get(GS.OBJECT_ID.value).split(RE.UNDER_SCORE.value)[0] if second_end_point is not None else GS.NULL.value

            if first_table and second_table:
                operator = expression.get(GS.OP.value) if expression is not None else GS.NULL.value

                if expression is not None:
                    expression_elements = expression.findall(GS.EXPRESSION.value)
                    first_column = expression_elements[0].attrib.get(GS.OP.value, GS.NULL.value).strip(RE.SQUARE_BRACKETS.value) if len(expression_elements) > 0 else GS.NULL.value
                    second_column = expression_elements[1].attrib.get(GS.OP.value, GS.NULL.value).strip(RE.SQUARE_BRACKETS.value) if len(expression_elements) > 1 else GS.NULL.value
                else:
                    first_column = GS.NULL.value
                    second_column = GS.NULL.value

                joins[f"join_{idx}"] = f"{first_table}.{first_column} {operator} {second_table}.{second_column}"
        except Exception as e:
            logger.error(f"Error processing join at index {idx}: {e}")
    logger.info("Join extraction completed.")
    return joins


def extract_calculations(root):
    logger.info("Extracting calculations.")
    calculations = []
    for dependency in root.findall(DS.DS.value):
        for column in dependency.findall(GS.COLUMN.value):
            calc = column.find(GS.CALC.value)
            if calc is not None and calc.get(GS.FORMULA.value) != '1':
                calculations.append({
                    GS.CAPTION.value: column.get(GS.CAPTION.value),
                    GS.FORMULA.value: calc.get(GS.FORMULA.value),
                    GS.NAME.value: column.get(GS.NAME.value)
                })
    logger.info("Calculation extraction completed.")
    return calculations


def extract_filters(root):
    logger.info("Extracting filters.")
    filters = []
    for filter_tag in root.findall(DS.FILTER.value):
        filter_data = {}
        for key, value in filter_tag.attrib.items():
            if key == GS.COLUMN.value:
                value = re.sub(RE.BETWEEN_SQUARE_BRACKETS_FOLLOWED_BY_DOT.value, RE.EMPTY.value, value)
            filter_data[key] = value

        groupfilter = filter_tag.find(GS.GROUP_FILTER.value)
        if groupfilter is not None:
            filter_data[GS.GROUP_FILTER.value] = {k: v for k, v in groupfilter.attrib.items()}

        filters.append(filter_data)
    logger.info("Filter extraction completed.")
    return filters


def extract_datasource_details(root):
    datasources_element = root.find(GS.Datasources.value)
    if datasources_element is None:
        logger.warning("No 'datasources' element found in the XML.")
        return {}

    datasources = datasources_element.findall(GS.Datasource.value)

    tables_and_columns, connection_details, datasource_names = extract_datasource_tables_and_columns(root)

    data = {
        "datasource_count": len(datasource_names),
        "connection_details": connection_details,
        "datasources": datasource_names,
        "Tables and Columns": tables_and_columns,
        "Semantic Mapping": extract_semantic_mapping(root),
        "Joins": extract_joins(root),
        "Calculations": extract_calculations(root),
        "Filters": extract_filters(root),
    }
    return {"Datasources": data}

def get_datasource_type(datasource_class):
    datasource_type = DATASOURCE_TYPES.get(datasource_class)
    if datasource_type:
        return datasource_type
    return datasource_class