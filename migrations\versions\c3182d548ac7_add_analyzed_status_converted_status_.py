from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID
from app.core.enums import FileStatus

def upgrade():
    # Step 1: Drop broken table if exists
    op.drop_table('report_details', schema='biport_dev')

    # Step 2: Recreate table with full structure
    op.create_table(
        'report_details',
        sa.Column('id', UUID(as_uuid=True), primary_key=True),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('report_id', UUID(as_uuid=True), nullable=False),
        sa.Column('project_id', UUID(as_uuid=True), nullable=False),

        sa.Column('is_analyzed', sa.<PERSON>(), server_default=sa.text("false")),
        sa.Column('analyzed_status', sa.Enum(FileStatus), nullable=True),

        sa.Column('is_converted', sa.<PERSON>(), server_default=sa.text("false")),
        sa.Column('converted_status', sa.Enum(FileStatus), nullable=True),

        sa.Column('is_migrated', sa.Boolean(), server_default=sa.text("false")),
        sa.Column('migrated_status', sa.Enum(FileStatus), nullable=True),

        sa.Column('unit_tested', sa.Boolean(), server_default=sa.text("false")),
        sa.Column('uat_tested', sa.Boolean(), server_default=sa.text("false")),
        sa.Column('deployed', sa.Boolean(), server_default=sa.text("false")),
        sa.Column('is_scoped', sa.Boolean(), server_default=sa.text("false")),

        sa.Column('semantic_type', sa.String(), nullable=True),
        sa.Column('has_semantic_model', sa.Boolean(), server_default=sa.text("false")),
        sa.Column('view_count', sa.Integer(), server_default=sa.text("0")),

        # Add AuditMixin columns
        sa.Column('created_at', sa.DateTime(), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.func.now(), nullable=False),
        sa.Column('created_by', sa.String(), nullable=True),
        sa.Column('updated_by', sa.String(), nullable=True),

        schema='biport_dev'
    )

    # Step 3: Copy data from old table
    op.execute("""
        INSERT INTO biport_dev.report_details (
            id, name, report_id, project_id,
            is_analyzed, is_converted, is_migrated,
            unit_tested, uat_tested, deployed, is_scoped,
            semantic_type, has_semantic_model, view_count,
            created_at, updated_at, created_by, updated_by
        )
        SELECT
            id, name, report_id, project_id,
            is_analyzed, is_converted, is_migrated,
            unit_tested, uat_tested, deployed, is_scoped,
            semantic_type, has_semantic_model, view_count,
            created_at, updated_at, created_by, updated_by
        FROM biport_dev.report_details_old
    """)
