#!/usr/bin/env python3
"""
Test the report status update with admin user who has access to reports
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from app.services.workspace.workspace_service import WorkspaceService
    from app.services.workspace.workspace_procssor import WorkspaceProcessor
    from app.schemas.workspace import ReportStatusUpdateRequest
    from app.models.users import UserManager
    from app.models.report_details import ReportDetail
    from app.core.session import scoped_context
    print("✅ All imports successful")
except Exception as e:
    print(f"❌ Import error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

def find_admin_user():
    """Find an admin user for testing"""
    try:
        with scoped_context() as session:
            from app.models.users import User
            from app.models.roles import Role
            
            # Look for users with admin role
            admin_users = session.query(User).join(Role).filter(
                Role.name == 'ADMIN'
            ).all()
            
            if admin_users:
                admin_user = admin_users[0]
                print(f"✅ Found admin user: {admin_user.name} ({admin_user.email})")
                return admin_user.email
            else:
                print("❌ No admin users found")
                return None
                
    except Exception as e:
        print(f"❌ Error finding admin user: {e}")
        return None

def test_full_workflow():
    """Test the complete workflow with admin user"""
    print("🧪 Testing Complete Report Status Update Workflow")
    print("=" * 60)
    
    # Find admin user
    admin_email = find_admin_user()
    if not admin_email:
        print("❌ Cannot proceed without admin user")
        return False
    
    try:
        # Get admin user
        admin_user = UserManager.get_user_by_email(admin_email, load_role=True)
        if not admin_user:
            print(f"❌ Admin user not found: {admin_email}")
            return False
        
        print(f"✅ Admin user loaded: {admin_user.name}")
        print(f"   Role: {admin_user.role.name.value if admin_user.role else 'No role'}")
        
        # Step 1: Get reports using workspace service
        print(f"\n📊 Step 1: Getting reports for admin user")
        service = WorkspaceService()
        reports_response = service.get_reports_by_user_role(admin_user)
        
        if not reports_response.success:
            print(f"❌ Failed to get reports: {reports_response.error}")
            return False
        
        print(f"✅ Found {len(reports_response.data)} reports for admin user")
        
        if not reports_response.data:
            print("📭 No reports available for testing")
            return True  # Not a failure, just no data
        
        # Step 2: Test update with first report
        test_report = reports_response.data[0]
        report_id = test_report['report_id']
        
        print(f"\n📝 Step 2: Testing status update")
        print(f"   Report: {test_report['report_name']}")
        print(f"   Report ID: {report_id}")
        print(f"   Current status: unit_tested={test_report.get('unit_tested')}, uat_tested={test_report.get('uat_tested')}, deployed={test_report.get('deployed')}")
        
        # Test update via service
        status_update = ReportStatusUpdateRequest(
            unit_tested=True,
            uat_tested=True,
            deployed=False
        )
        
        update_response = service.update_report_status(report_id, status_update, admin_user)
        
        if not update_response.success:
            print(f"❌ Update failed: {update_response.error}")
            return False
        
        print(f"✅ Update successful: {update_response.data.get('message', 'No message')}")
        print(f"   Updated fields: {update_response.data.get('updated_fields', [])}")
        
        # Step 3: Verify the update
        print(f"\n🔍 Step 3: Verifying update")
        verify_response = service.get_reports_by_user_role(admin_user)
        
        if not verify_response.success:
            print(f"❌ Verification failed: {verify_response.error}")
            return False
        
        updated_report = next((r for r in verify_response.data if r['report_id'] == report_id), None)
        if not updated_report:
            print(f"❌ Updated report not found in verification")
            return False
        
        print(f"✅ Verification successful")
        print(f"   Updated status: unit_tested={updated_report.get('unit_tested')}, uat_tested={updated_report.get('uat_tested')}, deployed={updated_report.get('deployed')}")
        
        # Check if values match what we set
        if (updated_report.get('unit_tested') == True and 
            updated_report.get('uat_tested') == True and 
            updated_report.get('deployed') == False):
            print(f"✅ All status values match expected results")
        else:
            print(f"❌ Status values don't match expected results")
            return False
        
        # Step 4: Test processor
        print(f"\n🔄 Step 4: Testing processor")
        status_update2 = ReportStatusUpdateRequest(unit_tested=False, deployed=True)
        
        processor_response = WorkspaceProcessor.process_update_report_status(report_id, status_update2, admin_user)
        
        if processor_response.success:
            print(f"✅ Processor test successful: {processor_response.data.get('message', 'No message')}")
        else:
            print(f"❌ Processor test failed: {processor_response.error}")
            return False
        
        # Step 5: Final verification
        print(f"\n🔍 Step 5: Final verification")
        final_response = service.get_reports_by_user_role(admin_user)
        final_report = next((r for r in final_response.data if r['report_id'] == report_id), None)
        
        if final_report:
            print(f"✅ Final verification successful")
            print(f"   Final status: unit_tested={final_report.get('unit_tested')}, uat_tested={final_report.get('uat_tested')}, deployed={final_report.get('deployed')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in workflow test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_validation_scenarios():
    """Test validation scenarios"""
    print(f"\n🧪 Testing Validation Scenarios")
    print("=" * 50)
    
    admin_email = find_admin_user()
    if not admin_email:
        return False
    
    try:
        admin_user = UserManager.get_user_by_email(admin_email, load_role=True)
        if not admin_user:
            return False
        
        service = WorkspaceService()
        
        # Test 1: Invalid report ID format
        print("📝 Test 1: Invalid report ID format")
        status_update = ReportStatusUpdateRequest(unit_tested=True)
        response = service.update_report_status("invalid-uuid", status_update, admin_user)
        
        if not response.success and "Invalid report ID format" in response.error:
            print("✅ Invalid report ID validation working")
        else:
            print(f"❌ Invalid report ID validation failed: {response.error}")
            return False
        
        # Test 2: Non-existent report ID (valid UUID format)
        print("📝 Test 2: Non-existent report ID")
        response = service.update_report_status("550e8400-e29b-41d4-a716-************", status_update, admin_user)
        
        if not response.success and "not found or access denied" in response.error:
            print("✅ Non-existent report validation working")
        else:
            print(f"❌ Non-existent report validation failed: {response.error}")
            return False
        
        # Test 3: Empty update (should be handled by processor)
        print("📝 Test 3: Empty status update")
        empty_update = ReportStatusUpdateRequest()
        response = WorkspaceProcessor.process_update_report_status("550e8400-e29b-41d4-a716-************", empty_update, admin_user)
        
        if not response.success and "At least one status field must be provided" in response.error:
            print("✅ Empty update validation working")
        else:
            print(f"❌ Empty update validation failed: {response.error}")
            return False
        
        print("✅ All validation tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Error testing validation: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Report Status Update API Testing (Complete Workflow)")
    print("=" * 70)
    
    tests = [
        ("Complete Workflow", test_full_workflow),
        ("Validation Scenarios", test_validation_scenarios)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*70}")
        result = test_func()
        results.append((test_name, result))
    
    # Summary
    print(f"\n{'='*70}")
    print("🎯 Test Summary:")
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {status}: {test_name}")
    
    all_passed = all(result for _, result in results)
    if all_passed:
        print(f"\n🎉 All tests passed! The report status update API is working correctly.")
        print(f"\n📋 Implementation Summary:")
        print(f"   ✅ Reuses existing ReportDetail.get_reports_by_user_role() for access control")
        print(f"   ✅ Maintains consistent role-based permissions")
        print(f"   ✅ Supports partial updates (individual fields)")
        print(f"   ✅ Proper validation and error handling")
        print(f"   ✅ Works with both service and processor layers")
        print(f"   ✅ Database audit columns populated with defaults")
    else:
        print(f"\n❌ Some tests failed. Check the details above.")
    
    return all_passed

if __name__ == "__main__":
    main()
