from app.core import logger
from app.core.enums import ChartType, GeneralKeys as GS, WorkSheet as WS

class BubbleChart:
    @staticmethod
    def check_bubble_chart(worksheet):
        try:
            marks = worksheet.findall(WS.MARK.value)
            if not marks or not any(mark.get(GS.CLASS.value, '').strip().lower() == ChartType.CIRCLE.value.lower() for mark in marks):
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            rows = worksheet.find(WS.ROWS.value)
            cols = worksheet.find(WS.COLS.value)
            if rows is None or cols is None:
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            encodings = worksheet.findall(WS.ENCODINGS.value)
            if not encodings:
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            encoding_tags = [e for e in encodings[0] if e.tag.lower() != GS.TOOLTIP.value]
            if len(encoding_tags) < 2:
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            size_tags = [e for e in encoding_tags if e.tag.lower() == GS.SIZE.value]
            if len(size_tags) != 1:
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            size_col = size_tags[0].get(GS.COLUMN.value, '')
            size_cols = size_col.strip('[]').split(':')
            if len(size_cols) != 3 or size_cols[2].lower() != GS.QK.value:
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            has_dimension = any(
                enc.tag.lower() != GS.SIZE.value and
                len(enc.get(GS.COLUMN.value, '').strip('[]').split(':')) == 3 and
                enc.get(GS.COLUMN.value, '').strip('[]').split(':')[2].lower() == GS.NK.value
                for enc in encoding_tags
            )
            if not has_dimension:
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            lod_tags = [e for e in encoding_tags if e.tag.lower() == 'lod']
            if lod_tags:
                lod_col = lod_tags[0].get(GS.COLUMN.value, '')
                lod_parts = lod_col.strip('[]').split(':')
                if len(lod_parts) != 3 or lod_parts[2].lower() != 'nk':
                    return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            return {
                GS.STATUS.value: True,
                GS.CHART_TYPE.value: ChartType.BUBBLE_CHART.value
            }

        except Exception as e:
            logger.error(f"Error in check_bubble_chart: {e}")
            return {
                GS.STATUS.value: False,
                GS.CHART_TYPE.value: None,
                GS.ERROR.value: str(e)
            }
