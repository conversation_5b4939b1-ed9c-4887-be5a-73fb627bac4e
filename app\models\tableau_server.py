import uuid
from sqlalchemy import <PERSON>um<PERSON>, <PERSON>, <PERSON>te<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, text, Boolean
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from app.core.session import Base, scoped_context
from app.models.base import AuditMixin
from app.core.enums import ServerStatus, ServerType, ServerAuthType
from app.core.session import scoped_context
from sqlalchemy.orm import joinedload


class TableauServerDetail(Base, AuditMixin):
    __tablename__ = "tableau_server_details"
    __table_args__ = {"schema": "biport_dev"}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    organization_id = Column(UUID(as_uuid=True), ForeignKey("biport_dev.organization_details.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("biport_dev.users.id"), nullable=False)
    name = Column(String, nullable=False)
    server_url = Column(String, nullable=False)
    status = Column(Enum(ServerStatus), nullable=False)
    type = Column(Enum(ServerType), nullable=False)

    report_count = Column(Integer, server_default=text("0"), nullable=False)
    project_count = Column(Integer, server_default=text("0"), nullable=False)
    site_count = Column(Integer, server_default=text("0"), nullable=False)

    # Relationships
    user = relationship("User")
    credentials = relationship("TableauServerCredential", back_populates="server", cascade="all, delete-orphan")

class TableauServerDetailManager:
    @staticmethod
    def get_servers_by_org_id(organization_id: uuid.UUID, offset: int = 0, limit: int = 10):
        """Fetch servers for the organisation with pagination, excluding deleted ones."""
        with scoped_context() as session:
            return session.query(TableauServerDetail).filter(
                TableauServerDetail.organization_id == organization_id,
                TableauServerDetail.is_deleted == False
            ).order_by(TableauServerDetail.updated_at.desc()).offset(offset).limit(limit).all()

    @staticmethod
    def get_total_servers_by_org_id(organization_id: uuid.UUID) -> int:
        """Fetch the total number of servers for a specific organisation, excluding deleted ones."""
        with scoped_context() as session:
            return session.query(TableauServerDetail).filter(
                TableauServerDetail.organization_id == organization_id,
                TableauServerDetail.is_deleted == False
            ).count()

    @staticmethod
    def get_server_by_id(server_id: uuid.UUID) -> TableauServerDetail:
        """Fetch a server by its ID, excluding deleted ones."""
        with scoped_context() as session:
            return session.query(TableauServerDetail).filter(
                TableauServerDetail.id == server_id,
                TableauServerDetail.is_deleted == False
            ).first()

    @staticmethod
    def update_server_status(server_id: uuid.UUID, status: ServerStatus):
        """Updates the status of a server."""
        with scoped_context() as session:
            server = session.query(TableauServerDetail).filter(
                TableauServerDetail.id == server_id,
                TableauServerDetail.is_deleted == False
            ).first()
            if server:
                server.status = status.value
                session.commit()
            else:
                raise ValueError("Server not found")

    @staticmethod
    def soft_delete_server(server_id: uuid.UUID):
        """Soft deletes a server by setting is_deleted=True."""
        with scoped_context() as session:
            server = session.query(TableauServerDetail).filter(
                TableauServerDetail.id == server_id,
                TableauServerDetail.is_deleted == False
            ).first()
            if server:
                setattr(server, 'is_deleted', True)
                session.commit()
            else:
                raise ValueError("Server not found")


class TableauServerCredential(Base, AuditMixin):
    __tablename__ = "tableau_server_credentials"
    __table_args__ = {"schema": "biport_dev"}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    server_id = Column(UUID(as_uuid=True), ForeignKey("biport_dev.tableau_server_details.id"), nullable=False)

    pat_name = Column(String, nullable=True)
    pat_secret = Column(String, nullable=True)

    username = Column(String, nullable=True)
    password = Column(String, nullable=True)

    server_auth_type = Column(Enum(ServerAuthType), nullable=False)
    is_deleted = Column(Boolean, server_default=text("false"), nullable=False)

    # Relationship
    server = relationship("TableauServerDetail", back_populates="credentials")
    sites = relationship("TableauSiteDetail", back_populates="credentials")


class TableauSiteDetail(Base, AuditMixin):
    __tablename__ = "tableau_site_details"
    __table_args__ = {"schema": "biport_dev"}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    credentials_id = Column(UUID(as_uuid=True), ForeignKey("biport_dev.tableau_server_credentials.id"), nullable=False)
    site_name = Column(String, nullable=False)
    site_id = Column(UUID(as_uuid=True), nullable=False)
    is_deleted = Column(Boolean, server_default=text("false"), nullable=False)

    # Relationships
    credentials = relationship("TableauServerCredential", back_populates="sites")

class TableauSiteDetailManager:
    @staticmethod
    def get_sites_by_server_id(server_id: UUID, offset: int = 0, limit: int = 10):
        with scoped_context() as session:
            return (
                session.query(TableauSiteDetail)
                .join(TableauServerCredential)
                .options(joinedload(TableauSiteDetail.credentials))
                .filter(
                    TableauSiteDetail.is_deleted == False,
                    TableauServerCredential.server_id == server_id
                )
                .order_by(TableauSiteDetail.updated_at.desc())
                .offset(offset)
                .limit(limit)
                .all()
            )



    