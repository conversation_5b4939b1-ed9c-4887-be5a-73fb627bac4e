#!/usr/bin/env python3
"""
Comprehensive API testing to identify and resolve issues
"""
import requests
import json
import traceback

# API base URL
BASE_URL = "http://127.0.0.1:9090/app_api"

def test_server_health():
    """Test if server is running"""
    print("🔍 Testing Server Health")
    print("=" * 50)
    
    try:
        response = requests.get(f"{BASE_URL}/ping", timeout=5)
        print(f"✅ Server is running: {response.status_code}")
        print(f"Response: {response.text}")
        return True
    except Exception as e:
        print(f"❌ Server not responding: {e}")
        return False

def test_login_endpoint():
    """Test login endpoint with detailed error handling"""
    print(f"\n🔐 Testing Login Endpoint")
    print("=" * 50)
    
    login_url = f"{BASE_URL}/users/login"
    login_data = {
        "email": "<EMAIL>",
        "password": "Raghava@123"
    }
    
    print(f"POST {login_url}")
    print(f"Body: {json.dumps(login_data, indent=2)}")
    
    try:
        response = requests.post(login_url, json=login_data, timeout=10)
        print(f"\nResponse Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"✅ Login Successful!")
            print(f"Response Keys: {list(response_data.keys())}")
            
            if 'data' in response_data:
                data = response_data['data']
                print(f"Data Keys: {list(data.keys())}")
                
                access_token = data.get('access_token')
                user_email = data.get('user_email')
                role = data.get('role')
                
                print(f"Role: {role}")
                print(f"Access Token Length: {len(access_token) if access_token else 0}")
                print(f"User Email (encoded): {user_email}")
                
                return access_token, user_email, role
            else:
                print("❌ No 'data' key in response")
                return None, None, None
        else:
            print(f"❌ Login Failed!")
            print(f"Response Text: {response.text}")
            try:
                error_data = response.json()
                print(f"Error JSON: {json.dumps(error_data, indent=2)}")
            except:
                print("Could not parse error as JSON")
            return None, None, None
            
    except Exception as e:
        print(f"❌ Exception during login: {e}")
        traceback.print_exc()
        return None, None, None

def test_workspace_endpoint(access_token, user_email, role):
    """Test workspace endpoint with detailed debugging"""
    print(f"\n📊 Testing Workspace Endpoint")
    print("=" * 50)
    
    if not access_token or not user_email:
        print("❌ Missing access token or user email")
        return False
    
    workspace_url = f"{BASE_URL}/workspace/reports"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "X-User-Email": user_email,
        "Content-Type": "application/json"
    }
    
    print(f"GET {workspace_url}")
    print(f"Headers:")
    for key, value in headers.items():
        if key == "Authorization":
            print(f"  {key}: Bearer {access_token[:20]}...")
        else:
            print(f"  {key}: {value}")
    
    try:
        print(f"\nMaking request...")
        response = requests.get(workspace_url, headers=headers, timeout=15)
        
        print(f"Response Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                response_data = response.json()
                print(f"✅ Workspace API Success!")
                print(f"Response Keys: {list(response_data.keys())}")
                
                if 'data' in response_data:
                    reports = response_data['data']
                    print(f"📈 Found {len(reports)} reports")
                    
                    if reports:
                        print(f"\nSample Report:")
                        sample = reports[0]
                        print(f"  Report ID: {sample.get('report_id', 'N/A')}")
                        print(f"  Report Name: {sample.get('report_name', 'N/A')}")
                        print(f"  Project Name: {sample.get('project_name', 'N/A')}")
                        print(f"  Status: Analyzed={sample.get('is_analyzed')}, Converted={sample.get('is_converted')}, Migrated={sample.get('is_migrated')}")
                        
                        # Verify all required fields
                        required_fields = [
                            'report_id', 'report_name', 'project_name',
                            'is_analyzed', 'is_converted', 'is_migrated',
                            'analyzed_status', 'converted_status', 'migrated_status'
                        ]
                        
                        missing_fields = [field for field in required_fields if field not in sample]
                        if missing_fields:
                            print(f"❌ Missing fields: {missing_fields}")
                        else:
                            print(f"✅ All required fields present")
                    
                    print(f"\n✅ Expected for {role} role: Developer should see 2 reports from Project_7")
                    return True
                else:
                    print(f"❌ No 'data' key in response")
                    print(f"Full Response: {json.dumps(response_data, indent=2)}")
                    return False
                    
            except Exception as e:
                print(f"❌ Error parsing JSON response: {e}")
                print(f"Raw Response: {response.text}")
                return False
                
        else:
            print(f"❌ Workspace API Failed!")
            print(f"Status Code: {response.status_code}")
            print(f"Response Text: {response.text}")
            
            # Try to get more details from server logs
            if response.status_code == 500:
                print(f"\n🔍 Server Error Details:")
                print(f"This is likely an internal server error.")
                print(f"Check server logs for detailed error information.")
            
            return False
            
    except Exception as e:
        print(f"❌ Exception during workspace API call: {e}")
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🧪 Comprehensive API Testing")
    print("=" * 60)
    print("Testing workspace API with role-based filtering")
    
    # Step 1: Test server health
    if not test_server_health():
        print("❌ Server is not running. Please start the server first.")
        return
    
    # Step 2: Test login
    access_token, user_email, role = test_login_endpoint()
    if not access_token:
        print("❌ Login failed. Cannot proceed with workspace testing.")
        return
    
    # Step 3: Test workspace endpoint
    success = test_workspace_endpoint(access_token, user_email, role)
    
    # Summary
    print(f"\n" + "=" * 60)
    print("🎯 Test Summary:")
    print(f"  ✅ Server Health: OK")
    print(f"  ✅ Login Endpoint: OK")
    print(f"  {'✅' if success else '❌'} Workspace Endpoint: {'OK' if success else 'FAILED'}")
    
    if success:
        print(f"\n🎉 All tests passed! The workspace API is working correctly.")
    else:
        print(f"\n❌ Workspace API test failed. Check the error details above.")

if __name__ == "__main__":
    main()
