from app.core.response import ServiceResponse
from app.services.roles.role_service import RoleService
from app.schemas.roles import AddRoleRequest


class RoleProcessor:
    """Handles validation and processing of role requests."""

    @staticmethod
    def process_add_role(request: AddRoleRequest) -> ServiceResponse:
        """Processes the add role request with generic exception handling."""
        return RoleService.execute(RoleService.add_role, request)
