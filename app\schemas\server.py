from typing import List, Optional
import uuid
from pydantic import BaseModel, EmailStr, Field, model_validator
from app.core.enums import ServerAuth<PERSON>ype, ServerStatus, ServerType

class ServerCredential(BaseModel):
    pat_name: str
    pat_secret: str
    
class AddServerRequest(BaseModel):
    server_name: str = Field(..., min_length=3, max_length=50)
    server_url: str  # Ensures valid URLs
    username: str | None = None
    password: str | None = None
    pat_name: str | None = None
    pat_secret: str | None = None
    server_credentials: List[ServerCredential]
    user_email: EmailStr
    server_auth_type: ServerAuthType | None = None  # Derived automatically
    server_type: ServerType  # Default to None, can be set later

    @model_validator(mode="before")
    def validate_authentication(cls, values):
        server_type = values.get("server_type")
        server_auth_type = values.get("server_auth_type")
        if server_type == ServerType.ONPREMISE.value:
            if server_auth_type is None:
                raise ValueError("server_auth_type must be provided")
            if server_auth_type == ServerAuthType.CREDENTIALS:
                if not values.get("username") or not values.get("password"):
                    raise ValueError("username & password must be provided")
            elif server_auth_type == ServerAuthType.PAT:
                if not values.get("pat_name") or not values.get("pat_secret"):
                    raise ValueError("pat_name & pat_secret must be provided")
        elif server_type == ServerType.CLOUD.value:
            if not values.get("server_credentials"):
                raise ValueError("At least one PAT credential must be provided")
        return values

class UpdateServerRequest(BaseModel):
    server_id: uuid.UUID
    server_name: str = Field(..., min_length=3, max_length=50)
    server_url: str
    username: str | None = None
    password: str | None = None
    pat_name: str | None = None
    pat_secret: str | None = None
    server_credentials: List[ServerCredential]
    user_email: EmailStr
    server_auth_type: ServerAuthType | None = None  # Derived automatically
    server_type: ServerType  # Default to None, can be set later

    @model_validator(mode="before")
    def validate_authentication(cls, values):
        server_type = values.get("server_type")
        server_auth_type = values.get("server_auth_type")
        if server_type == ServerType.ONPREMISE.value:
            if server_auth_type is None:
                raise ValueError("server_auth_type must be provided")
            if server_auth_type == ServerAuthType.CREDENTIALS:
                if not values.get("username") or not values.get("password"):
                    
                    raise ValueError("username & password must be provided")
            elif server_auth_type == ServerAuthType.PAT:
                if not values.get("pat_name") or not values.get("pat_secret"):
                    raise ValueError("pat_name & pat_secret must be provided")
        elif server_type == ServerType.CLOUD.value:
            if not values.get("server_credentials"):
                raise ValueError("At least one PAT credential must be provided")
        return values


class UpdateServerStatusRequest(BaseModel):
    server_id: uuid.UUID
    status: ServerStatus

class ServerResponse(BaseModel):
    id: uuid.UUID
    server_name: str
    server_url: str
    status: str
    server_type: ServerType
    server_auth_type: Optional[ServerAuthType] = None
    username: Optional[str] = None
    password: Optional[str] = None
    pat_name: Optional[str] = None
    pat_secret: Optional[str] = None
    server_credentials: Optional[List[ServerCredential]] = None

class GetServerResponse(BaseModel):
    data: Optional[ServerResponse] = None
    error: Optional[str] = None

class ServerDataResponse(BaseModel):
    data: List[ServerResponse]
    total: int
    page: int
    page_size: int
    total_pages: int
    
class ServerListResponse(BaseModel):
    data: Optional[ServerDataResponse] = None
    error: Optional[str] = None

# New schemas for TableauServerDetail
class TableauServerResponse(BaseModel):
    id: uuid.UUID
    name: str
    server_url: str
    status: str

class TableauServerDataResponse(BaseModel):
    content: List[TableauServerResponse]
    total: int
    page: int
    page_size: int
    total_pages: int
    
class TableauServerListResponse(BaseModel):
    data: Optional[TableauServerDataResponse] = None
    error: Optional[str] = None

class DeleteServerRequest(BaseModel):
    server_id: uuid.UUID