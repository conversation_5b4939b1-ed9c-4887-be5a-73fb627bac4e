#!/usr/bin/env python3
"""
Test script to verify the workspace API implementation
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.workspace.workspace_service import WorkspaceService
from app.models.users import UserManager
from app.core.session import scoped_context
from app.models.users import User
from app.models.roles import Role
from app.models.organization_details import OrganizationDetail
from sqlalchemy.orm import joinedload

class MockUser:
    """Mock user object to simulate get_current_user response"""
    def __init__(self, user_id, role_name):
        self.id = user_id
        self.role_name = role_name

def test_workspace_service():
    """Test the workspace service with different user roles"""
    print("=== Testing Workspace Service ===\n")
    
    with scoped_context() as session:
        # Get some test users from the database
        users = session.query(User).options(
            joinedload(User.role),
            joinedload(User.organization)
        ).limit(3).all()
        
        if not users:
            print("❌ No users found in database. Please run the database setup first.")
            return
        
        print(f"Found {len(users)} users in database:")
        for user in users:
            role_name = user.role.name.value if user.role and user.role.name else "Unknown"
            print(f"  - {user.name} ({user.email}) - Role: {role_name}")
        
        print("\n" + "="*50)
        
        # Test with each user
        service = WorkspaceService()
        
        for user in users:
            role_name = user.role.name.value if user.role and user.role.name else "Unknown"
            print(f"\n🧪 Testing with user: {user.name} (Role: {role_name})")
            
            # Create mock user object
            mock_user = MockUser(user.id, role_name)
            
            # Test the service
            try:
                response = service.get_reports_by_user_role(mock_user)
                
                if response.success:
                    print(f"✅ Success! Found {len(response.data)} reports")
                    
                    # Show sample reports
                    for i, report in enumerate(response.data[:3]):  # Show first 3
                        print(f"   {i+1}. {report['report_name']} (Project: {report['project_name']})")
                        print(f"      Status: Analyzed={report['is_analyzed']}, Converted={report['is_converted']}, Migrated={report['is_migrated']}")
                else:
                    print(f"❌ Failed: {response.error}")
                    
            except Exception as e:
                print(f"❌ Exception: {e}")
        
        print("\n" + "="*50)
        print("🎉 Workspace service testing completed!")

def test_database_relationships():
    """Test database relationships"""
    print("\n=== Testing Database Relationships ===\n")
    
    with scoped_context() as session:
        # Test user-role relationship
        users_with_roles = session.query(User).join(Role).limit(3).all()
        print(f"Users with roles: {len(users_with_roles)}")
        
        # Test project-report relationship
        from app.models.project_details import ProjectDetail
        from app.models.report_details import ReportDetail
        
        projects_with_reports = session.query(ProjectDetail).join(ReportDetail).limit(3).all()
        print(f"Projects with reports: {len(projects_with_reports)}")
        
        # Test user-project assignment relationship
        assigned_projects = session.query(ProjectDetail).filter(ProjectDetail.assigned_to.isnot(None)).limit(3).all()
        print(f"Assigned projects: {len(assigned_projects)}")
        
        if assigned_projects:
            print("\nSample assigned projects:")
            for project in assigned_projects:
                assignee = session.query(User).filter(User.id == project.assigned_to).first()
                assignee_name = assignee.name if assignee else "Unknown"
                print(f"  - {project.name} assigned to {assignee_name}")

if __name__ == "__main__":
    try:
        test_database_relationships()
        test_workspace_service()
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
