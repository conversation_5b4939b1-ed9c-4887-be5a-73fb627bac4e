"""updating table schema

Revision ID: 03a81c14f05a
Revises: 4fbb3a4a530c
Create Date: 2025-04-04 15:51:47.680678

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '03a81c14f05a'
down_revision: Union[str, None] = '4fbb3a4a530c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    op.drop_column("data_sources","data_sources")
    op.add_column('data_sources', sa.Column("SQL", sa.String(), nullable=True))
    op.add_column('data_sources', sa.Column("POSTGRESQL", sa.String(), nullable=True))

def downgrade():
    op.add_column('data_sources', 'data_sources')
    op.drop_column("data_sources","SQL")
    op.drop_column("data_sources","POSTGRESQL")

