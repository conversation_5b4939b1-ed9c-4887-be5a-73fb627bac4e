import uuid
from app.core.base_service import BaseService
from app.core.response import ServiceResponse
from app.models.roles import RoleManager
from app.schemas.roles import AddRoleRequest
from app.core.exceptions import NotFoundError, ConflictError
from app.core.logger_setup import logger


class RoleService(BaseService):
    """Service class for role operations."""

    @staticmethod
    def add_role(request: AddRoleRequest) -> ServiceResponse:
        """Add a new role to the database."""
        existing_roles = RoleManager.get_all_roles()
        role_name = request.name.value
        for role in existing_roles:
            if role.name == role_name:
                raise ConflictError(f"Role {role_name} already exists")
        
        new_role = RoleManager.create_role(role_name)

        response_data = {
            "id": str(new_role.id),
            "name": new_role.name.value
        }
        
        return ServiceResponse.success(response_data, status_code=201)
