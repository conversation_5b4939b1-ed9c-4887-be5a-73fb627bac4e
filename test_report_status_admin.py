#!/usr/bin/env python3
"""
Test the report status update API with admin user
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from app.services.workspace.workspace_service import WorkspaceService
    from app.services.workspace.workspace_procssor import WorkspaceProcessor
    from app.schemas.workspace import ReportStatusUpdateRequest
    from app.models.users import UserManager
    from app.models.report_details import ReportDetail
    from app.core.session import scoped_context
    print("✅ All imports successful")
except Exception as e:
    print(f"❌ Import error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

def find_admin_user():
    """Find an admin user for testing"""
    print("🔍 Finding admin user for testing")
    print("=" * 40)
    
    try:
        with scoped_context() as session:
            # Look for users with admin role
            from app.models.users import User
            from app.models.role import Role
            
            admin_users = session.query(User).join(Role).filter(
                Role.name == 'ADMIN'
            ).all()
            
            if admin_users:
                admin_user = admin_users[0]
                print(f"✅ Found admin user: {admin_user.name} ({admin_user.email})")
                return admin_user.email
            else:
                print("❌ No admin users found")
                return None
                
    except Exception as e:
        print(f"❌ Error finding admin user: {e}")
        return None

def test_with_admin_user():
    """Test the report status update with admin user"""
    print("🧪 Testing Report Status Update with Admin User")
    print("=" * 60)
    
    # Find admin user
    admin_email = find_admin_user()
    if not admin_email:
        print("❌ Cannot proceed without admin user")
        return False
    
    try:
        # Get admin user
        admin_user = UserManager.get_user_by_email(admin_email, load_role=True)
        if not admin_user:
            print(f"❌ Admin user not found: {admin_email}")
            return False
        
        print(f"✅ Admin user loaded: {admin_user.name}")
        print(f"   Role: {admin_user.role.name.value if admin_user.role else 'No role'}")
        
        # Test get reports
        service = WorkspaceService()
        reports_response = service.get_reports_by_user_role(admin_user)
        
        if not reports_response.success:
            print(f"❌ Failed to get reports: {reports_response.error}")
            return False
        
        print(f"✅ Found {len(reports_response.data)} reports for admin user")
        
        if not reports_response.data:
            print("📭 No reports available for testing")
            return True  # Not a failure, just no data
        
        # Test with first report
        test_report = reports_response.data[0]
        report_id = test_report['report_id']
        
        print(f"\n📋 Testing with report: {test_report['report_name']}")
        print(f"   Report ID: {report_id}")
        print(f"   Current status: unit_tested={test_report.get('unit_tested')}, uat_tested={test_report.get('uat_tested')}, deployed={test_report.get('deployed')}")
        
        # Test update
        print(f"\n📝 Testing status update...")
        status_update = ReportStatusUpdateRequest(
            unit_tested=True,
            uat_tested=False,
            deployed=True
        )
        
        update_response = service.update_report_status(report_id, status_update, admin_user)
        
        if update_response.success:
            print(f"✅ Update successful: {update_response.data.get('message', 'No message')}")
            
            # Verify the update
            print(f"\n🔍 Verifying update...")
            verify_response = service.get_reports_by_user_role(admin_user)
            
            if verify_response.success:
                updated_report = next((r for r in verify_response.data if r['report_id'] == report_id), None)
                if updated_report:
                    print(f"✅ Verification successful")
                    print(f"   Updated status: unit_tested={updated_report.get('unit_tested')}, uat_tested={updated_report.get('uat_tested')}, deployed={updated_report.get('deployed')}")
                    
                    # Check if values match what we set
                    if (updated_report.get('unit_tested') == True and 
                        updated_report.get('uat_tested') == False and 
                        updated_report.get('deployed') == True):
                        print(f"✅ All status values match expected results")
                        return True
                    else:
                        print(f"❌ Status values don't match expected results")
                        return False
                else:
                    print(f"❌ Updated report not found in verification")
                    return False
            else:
                print(f"❌ Verification failed: {verify_response.error}")
                return False
        else:
            print(f"❌ Update failed: {update_response.error}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing with admin user: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_processor_with_admin():
    """Test the processor with admin user"""
    print(f"\n🧪 Testing Processor with Admin User")
    print("=" * 50)
    
    admin_email = find_admin_user()
    if not admin_email:
        return False
    
    try:
        admin_user = UserManager.get_user_by_email(admin_email, load_role=True)
        if not admin_user:
            return False
        
        # Get reports
        service = WorkspaceService()
        reports_response = service.get_reports_by_user_role(admin_user)
        
        if not reports_response.success or not reports_response.data:
            print("📭 No reports available for processor testing")
            return True
        
        report_id = reports_response.data[0]['report_id']
        
        # Test processor
        status_update = ReportStatusUpdateRequest(unit_tested=False, uat_tested=True)
        
        response = WorkspaceProcessor.process_update_report_status(report_id, status_update, admin_user)
        
        if response.success:
            print(f"✅ Processor test successful: {response.data.get('message', 'No message')}")
            return True
        else:
            print(f"❌ Processor test failed: {response.error}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing processor: {e}")
        return False

def test_validation():
    """Test validation scenarios"""
    print(f"\n🧪 Testing Validation Scenarios")
    print("=" * 50)
    
    admin_email = find_admin_user()
    if not admin_email:
        return False
    
    try:
        admin_user = UserManager.get_user_by_email(admin_email, load_role=True)
        if not admin_user:
            return False
        
        service = WorkspaceService()
        
        # Test 1: Invalid report ID
        print("📝 Test 1: Invalid report ID")
        status_update = ReportStatusUpdateRequest(unit_tested=True)
        response = service.update_report_status("invalid-uuid", status_update, admin_user)
        
        if not response.success and "Invalid report ID format" in response.error:
            print("✅ Invalid report ID validation working")
        else:
            print(f"❌ Invalid report ID validation failed: {response.error}")
            return False
        
        # Test 2: Empty update (should be handled by processor)
        print("📝 Test 2: Empty status update")
        empty_update = ReportStatusUpdateRequest()
        response = WorkspaceProcessor.process_update_report_status("550e8400-e29b-41d4-a716-************", empty_update, admin_user)
        
        if not response.success and "At least one status field must be provided" in response.error:
            print("✅ Empty update validation working")
        else:
            print(f"❌ Empty update validation failed: {response.error}")
            return False
        
        print("✅ All validation tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Error testing validation: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Report Status Update API Testing (Admin User)")
    print("=" * 70)
    
    tests = [
        ("Admin User Report Status Update", test_with_admin_user),
        ("Processor with Admin", test_processor_with_admin),
        ("Validation Scenarios", test_validation)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*70}")
        result = test_func()
        results.append((test_name, result))
    
    # Summary
    print(f"\n{'='*70}")
    print("🎯 Test Summary:")
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {status}: {test_name}")
    
    all_passed = all(result for _, result in results)
    if all_passed:
        print(f"\n🎉 All tests passed! The report status update API is working correctly.")
    else:
        print(f"\n❌ Some tests failed. Check the details above.")
    
    return all_passed

if __name__ == "__main__":
    main()
