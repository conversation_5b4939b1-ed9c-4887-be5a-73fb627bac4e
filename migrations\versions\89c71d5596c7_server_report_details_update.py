"""server_report_details_update

Revision ID: 89c71d5596c7
Revises: 347083a96be2
Create Date: 2025-06-17 17:38:24.819504

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '89c71d5596c7'
down_revision: Union[str, None] = '347083a96be2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_report_details_id', table_name='server_report_details')
    op.create_index(op.f('ix_server_report_details_id'), 'server_report_details', ['id'], unique=False)

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('users', 'password',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('users', 'id',
               existing_type=sa.INTEGER(),
               server_default=sa.Identity(always=True, start=1, increment=1, minvalue=1, maxvalue=2147483647, cycle=False, cache=1),
               existing_nullable=False,
               autoincrement=True)
    op.drop_index(op.f('ix_upload_files_report_details_id'), table_name='upload_files_report_details')
    op.alter_column('upload_files_report_details', 'is_migrated',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('false'))
    op.alter_column('upload_files_report_details', 'is_converted',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('false'))
    op.alter_column('upload_files_report_details', 'is_analyzed',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('false'))
    op.drop_index(op.f('ix_server_report_details_id'), table_name='server_report_details')
    op.create_index('ix_report_details_id', 'server_report_details', ['id'], unique=False)
    op.alter_column('folders', 'id',
               existing_type=sa.String(length=36),
               type_=sa.UUID(),
               existing_nullable=False)
    op.alter_column('files', 'folder_id',
               existing_type=sa.String(length=36),
               type_=sa.UUID(),
               existing_nullable=False)
    # ### end Alembic commands ###
