from app.core import ServiceResponse
from app.models.users import User
from app.services.workspace import WorkspaceService
from app.core.logger_setup import logger


class WorkspaceProcessor:
    """Handles validation and business logic for workspace-related APIs."""

    @staticmethod
    def process_get_reports_by_user_role(user: User) -> ServiceResponse:
        """
        Fetch reports based on the current user's role.
        Role determines access level:
          - Admin: sees all reports.
          - Manager: sees reports assigned to them or their subordinates.
          - Developer: sees only reports assigned to them.
        """
        # Get role name safely
        role_name = getattr(user, 'role_name', None)
        logger.info(f"[WorkspaceProcessor] Processing report fetch for user ID: {user.id} (Role: {role_name})")
        print(f"[DEBUG] process_get_reports_by_user_role called for user ID: {user.id}, Role: {role_name}")

        try:
            response = WorkspaceService.execute(WorkspaceService().get_reports_by_user_role, user)
            logger.info(f"[WorkspaceProcessor] Successfully fetched reports. Status: {response.status_code}")
            print(f"[DEBUG] Response status: {response.status_code}, Data count: {len(response.data) if response.data else 0}")
            return response
        except Exception as e:
            logger.error(f"[WorkspaceProcessor] Error occurred while fetching reports: {e}", exc_info=True)
            print(f"[ERROR] Exception in WorkspaceProcessor: {e}")
            return ServiceResponse.failure("Error processing report fetch by user role", 500)

    @staticmethod
    def process_update_report_status(report_id: str, status_update, user: User) -> ServiceResponse:
        """
        Process the update of report status flags (unit_tested, uat_tested, deployed).
        Validates the request and delegates to the service layer.
        """
        logger.info(f"[WorkspaceProcessor] Processing report status update for report ID: {report_id}")
        print(f"[DEBUG] process_update_report_status called for report ID: {report_id}")

        try:
            # Validate that at least one field is being updated
            if (status_update.unit_tested is None and
                status_update.uat_tested is None and
                status_update.deployed is None):
                logger.warning("[WorkspaceProcessor] No status fields provided for update")
                return ServiceResponse.failure("At least one status field must be provided", 400)

            # Log what fields are being updated
            update_fields = []
            if status_update.unit_tested is not None:
                update_fields.append(f"unit_tested={status_update.unit_tested}")
            if status_update.uat_tested is not None:
                update_fields.append(f"uat_tested={status_update.uat_tested}")
            if status_update.deployed is not None:
                update_fields.append(f"deployed={status_update.deployed}")

            logger.info(f"[WorkspaceProcessor] Updating fields: {', '.join(update_fields)}")
            print(f"[DEBUG] Updating fields: {', '.join(update_fields)}")

            # Delegate to service layer
            response = WorkspaceService.execute(
                WorkspaceService().update_report_status,
                report_id,
                status_update,
                user
            )

            logger.info(f"[WorkspaceProcessor] Report status update completed. Status: {response.status_code}")
            print(f"[DEBUG] Update response status: {response.status_code}, Success: {response.success}")

            return response

        except Exception as e:
            logger.error(f"[WorkspaceProcessor] Error occurred while updating report status: {e}", exc_info=True)
            print(f"[ERROR] Exception in WorkspaceProcessor update_report_status: {e}")
            return ServiceResponse.failure("Error processing report status update", 500)