import re
import logging
import uuid

from .table_columns_handler import fetch_objects_data


def determine_cardinality_and_columns(from_column_full, to_column_full, from_endpoint, to_endpoint):
    """
    Determines the cardinality and column order for a relationship based on unique-key attributes.
    Args:
        from_column_full (str): The fully qualified from column.
        to_column_full (str): The fully qualified to column.
        from_endpoint (xml.etree.ElementTree.Element): The first-end-point element.
        to_endpoint (xml.etree.ElementTree.Element): The second-end-point element.
    Returns:
        tuple: (from_col_final, to_col_final, from_cardinality, to_cardinality)
    """
    from_unique = from_endpoint.get("unique-key") == "true" if from_endpoint is not None else False
    to_unique = to_endpoint.get("unique-key") == "true" if to_endpoint is not None else False
    if from_unique and to_unique:
        return from_column_full, to_column_full, "one", "one"
    elif not from_unique and not to_unique:
        return from_column_full, to_column_full, "many", "many"
    elif from_unique and not to_unique:
        return to_column_full, from_column_full, "many", "one"
    elif not from_unique and to_unique:
        return from_column_full, to_column_full, "many", "one"
    else:
        return from_column_full, to_column_full, "unknown", "unknown"

def extract_relationships(datasource_elements):
    """
    Extracts relationships from a list of Tableau <datasource> XML elements' object-graph/relationships sections.
    Args:
        datasource_elements (list[xml.etree.ElementTree.Element]): List of <datasource> XML elements to process, as returned by root.find(".//datasources").findall(".//datasource").
    Returns:
        list[dict]: A list of dictionaries, each containing 'id', 'crossFilteringBehavior', 'fromCardinality', 'toCardinality', 'fromColumn', and 'toColumn'.
    """
    relationships_list = []
    for datasource_element in datasource_elements:
        try:
            relationships_element = datasource_element.find('object-graph/relationships')
            if relationships_element is None:
                logging.info("No <relationships> tag found under <object-graph> in this datasource.")
                continue
            relationship_elements = relationships_element.findall('relationship')
            if not relationship_elements:
                logging.info("No <relationship> tags found under <relationships> in this datasource.")
                continue
            object_id_to_caption = fetch_objects_data(datasource_element)
            for relationship_element in relationship_elements:
                equality_expression = relationship_element.find("expression[@op='=']")
                if equality_expression is None:
                    logging.info("No <expression op='='> tag found in <relationship>.")
                    continue
                column_expressions = equality_expression.findall('expression')
                if len(column_expressions) < 2:
                    logging.info("Less than two <expression> tags found in <expression op='='>.")
                    continue
                clean_column_name = lambda col: re.sub(r'\s*\([^)]+\)', '', col.strip('[]').strip())
                from_column = clean_column_name(column_expressions[0].get('op', ''))
                to_column = clean_column_name(column_expressions[1].get('op', ''))
                from_endpoint = relationship_element.find('first-end-point')
                to_endpoint = relationship_element.find('second-end-point')
                if from_endpoint is None or to_endpoint is None:
                    logging.info("Missing <first-end-point> or <second-end-point> in <relationship>.")
                    continue
                from_table = object_id_to_caption.get(from_endpoint.get('object-id'))
                to_table = object_id_to_caption.get(to_endpoint.get('object-id'))
                if not (from_table and to_table and from_column and to_column):
                    logging.info("Missing table or column name when processing <relationship>.")
                    continue
                from_column_full = f"{from_table}.{from_column}" if ' ' not in from_column else f"{from_table}.'{from_column}'"
                to_column_full = f"{to_table}.{to_column}" if ' ' not in to_column else f"{to_table}.'{to_column}'"
                from_col_final, to_col_final, from_cardinality, to_cardinality = determine_cardinality_and_columns(
                    from_column_full, to_column_full, from_endpoint, to_endpoint
                )
                relationship_id = str(uuid.uuid4())
                relationships_list.append({
                    'id': relationship_id,
                    'crossFilteringBehavior': 'bothDirections',
                    'fromCardinality': from_cardinality,
                    'toCardinality': to_cardinality,
                    'fromColumn': from_col_final,
                    'toColumn': to_col_final
                })
        except Exception as e:
            raise RuntimeError(f"Failed to extract relationships from datasource: {e}")
    return relationships_list