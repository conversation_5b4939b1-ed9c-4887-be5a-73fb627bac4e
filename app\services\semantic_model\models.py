

def write_models_file(table_columns, unique_table_lineage_tags, models_file):
    """
    Writes the main models file containing model metadata and a list of tables.
    """
    with open(models_file, "w") as models_tmdl:
        models_tmdl.write("model Model\n")
        models_tmdl.write("\tculture: en-US\n")
        models_tmdl.write("\tdefaultPowerBIDataSourceVersion: powerBI_V3\n")
        models_tmdl.write("\tdataAccessOptions\n")
        models_tmdl.write("\t\tlegacyRedirects\n")
        models_tmdl.write("\t\treturnErrorValuesAsNull\n\n")
        models_tmdl.write("annotation PBI_QueryOrder = [")
        models_tmdl.write(", ".join(f'"{table}"' for table in table_columns.keys()))
        models_tmdl.write("]\n\n")
        for table in table_columns.keys():
            models_tmdl.write(f"ref table {table}\n")
        models_tmdl.write("annotation __PBI_TimeIntelligenceEnabled = 1\n\n")
        models_tmdl.write("annotation PBI_RemovedChildren = []\n\n")