from ..core import (
    get_background_config, get_from_list, get_projections_data, get_title_config, 
    get_border_config, process_label_data, process_tooltip_data, process_pane_encodings,
    process_format_data, to_list, create_expr, get_color_formatting
)
from app.core.templates import bar_chart_template
import uuid, json
from app.core import logger
from app.core.enums import (
    ChartType, PowerBITemplateKeys, TableauXMLTags,
    PowerBIReportKeys, PowerBIChartTypes, VisualRequest
)
from app.services.migrate.Tableau_Analyzer.report import (
    extract_multiple_encodings_data,
    generate_projections_data
)

def process_bar_colors(color_field, panes, style, table_column_data, datasource_column_list):
    if color_field:
        values_input, value_queryref, min_color, mid_color, max_color = get_color_formatting(style, color_field, table_column_data, datasource_column_list)
        return {"properties":{"fill": {"solid": {"color": {"expr": {"FillRule": {"Input": values_input,"FillRule": {"linearGradient3": {"min": {"color": {"Literal": {"Value": f"'{min_color}'"}}},"mid": {"color": {"Literal": {"Value": f"'{mid_color}'"}}},"max": {"color": {"Literal": {"Value": f"'{max_color}'"}}},"nullColoringStrategy": {"strategy": {"Literal": {"Value": "'asZero'"}}}}}}}}}}},"selector": {"data": [{"dataViewWildcard": {"matchingOption": 1}}]}}
    if panes:
        panes_data = to_list(panes)
        style_rule_data = panes_data[0].get("style",{}).get("style-rule")
        if style_rule_data:
            style_rule_data = to_list(style_rule_data)
            for data in style_rule_data:
                if data.get("@element") == "mark":
                    format_data = data.get("format")
                    format_data = to_list(format_data)
                    for item in format_data:
                        if item.get("@attr") == "mark-color":
                            color = item.get("@value")
                            return {"properties":{"fill":{"solid":{"color":create_expr(f"'{color}'")}}}}
                        
    return {"properties":{"fill":{"solid":{"color":create_expr("'#4e79a7'")}}}}

def get_barchart_objects_data(color_field, panes, style, visual_type, rows, cols, table_column_data, datasource_column_list):
    objects = {}
    objects["lineStyles"] = [{"properties": {"showMarker": {"expr": {"Literal": {"Value": "true"}}}}}]
    style_rule = to_list(style.get("style-rule")) if style else None
    if color_field: objects["legend"] = [{"properties":{"position":create_expr(f"'Right'")}}]
    label_data = process_label_data(style_rule)
    datapoint_details = process_bar_colors(color_field, panes, style, table_column_data, datasource_column_list)
    objects["dataPoint"] = [datapoint_details]
    if label_data: objects["labels"] = [{"properties":label_data}]
    if style_rule:
        if visual_type == ChartType.HORIZONTAL_BAR.value:
            value_axis = process_format_data(style_rule, cols, "cols")
            category_axis = process_format_data(style_rule, rows, "rows")
        else:
            value_axis = process_format_data(style_rule, rows, "rows")
            category_axis = process_format_data(style_rule, cols, "cols")
        if value_axis: objects["valueAxis"] = [{"properties":value_axis}]
        if category_axis: objects["categoryAxis"] = [{"properties":category_axis}]
    return objects

def get_barchart_report(rows, cols, visual_type, panes, table_column_data, datasource_column_list, worksheet_name, worksheet_title_layout, style):
    
    try:
        overall_barchart_result = []

        projections_dict = {}

        color_field, tooltips, lod_data, text_data = process_pane_encodings(panes)

        tooltips_data = process_tooltip_data(data = [tooltips, lod_data, text_data], visual_fields= [rows, cols])

        result_queryref, table_list, select_list = get_projections_data([rows, cols, tooltips_data], table_column_data, datasource_column_list)
        if visual_type == ChartType.HORIZONTAL_BAR.value:
            bar_visual_type = "clusteredBarChart"
            y_queryref_list = result_queryref[cols]
            category_queryref_list = result_queryref[rows]
        else:
            bar_visual_type = "clusteredColumnChart"
            y_queryref_list = result_queryref[rows]
            category_queryref_list = result_queryref[cols]

        tooltip_queryref_list = result_queryref.get(tooltips_data)

        projections_dict["Y"] = y_queryref_list
        projections_dict["Category"] = category_queryref_list
        if tooltip_queryref_list: projections_dict["Tooltips"] = tooltip_queryref_list

        from_list = get_from_list(table_list)

        title_list = get_title_config(worksheet_title_layout, style)
        background_list = get_background_config(style= style)
        objects_data = get_barchart_objects_data(color_field, panes, style, visual_type, rows, cols, table_column_data, datasource_column_list)

        barchart_json = {
            "visual_config_name" : f'{str(uuid.uuid4()).replace("-","")[:20]}',
            "visual_type" : bar_visual_type,
            "projections_data" : json.dumps(projections_dict),
            "from_list" : json.dumps(from_list),
            "select_list" : json.dumps(select_list),
            "objects_data" : json.dumps(objects_data),
            "title_list" : json.dumps(title_list),
            "background_list" : json.dumps(background_list),
            "border_list" : get_border_config()
        }
        overall_barchart_result.append({"config": barchart_json, "template": bar_chart_template})
        return overall_barchart_result

    except Exception as e:
        logger.error(f"---Error in generating bar chart visual for {worksheet_name}--- {str(e)}")
        raise ValueError(f"Error in generating bar chart visual for {worksheet_name} - {str(e)}")


def process_clustered_column_chart(request: VisualRequest):
    """
    Process clustered column chart visual request.
    
    Parameters
    ----------
    request : VisualRequest
        The request object containing details for the clustered column chart.
    
    Returns
    -------
    list
        A list containing the processed clustered column chart data.
    """
    clustered_chart_result = {}
    rows = request.rows
    cols = request.cols
    visual_type = request.visual_type
    table_column_data = request.table_column_data
    panes = request.panes

    encodings = extract_multiple_encodings_data(
        panes=panes,
        tags_to_extract=[
            TableauXMLTags.TOOLTIP.value,
            TableauXMLTags.LOD.value,
            TableauXMLTags.TEXT.value,
            TableauXMLTags.COLOR.value
        ]
    )
    
    clustered_chart_filed_mapping = {
        PowerBIReportKeys.Y.value: cols if visual_type == ChartType.HORIZONTAL_BAR.value else rows,
        PowerBIReportKeys.CATEGORY.value: rows if visual_type == ChartType.HORIZONTAL_BAR.value else cols,
    }

    projections_data = generate_projections_data(table_column_data, clustered_chart_filed_mapping)

    clustered_chart_result[PowerBITemplateKeys.VISUAL_TYPE.value] = PowerBIChartTypes.CLUSTERED_BAR.value if visual_type == ChartType.HORIZONTAL_BAR.value else PowerBIChartTypes.CLUSTERED_COLUMN.value
    clustered_chart_result[PowerBITemplateKeys.WORKSHEET_NAME.value] = request.worksheet_name
    clustered_chart_result[PowerBITemplateKeys.PROJECTIONS_DATA.value] = projections_data


    return clustered_chart_result