table DateTableTemplate_16a63790-2a75-46ea-8317-947b8304b455
	isHidden
	isPrivate
	lineageTag: 9a6694dd-92d8-45fa-b856-8ea7d872ec84

	column Date
		dataType: dateTime
		isHidden
		lineageTag: 68531c02-a8ad-45f5-ace8-05cafb08b321
		dataCategory: PaddedDateTableDates
		summarizeBy: none
		isNameInferred
		sourceColumn: [Date]

		annotation SummarizationSetBy = User

	column Year = YEAR([Date])
		dataType: int64
		isHidden
		lineageTag: 481704bd-1842-4b2c-af17-87e100bb744b
		dataCategory: Years
		summarizeBy: none

		annotation SummarizationSetBy = User

		annotation TemplateId = Year

	column MonthNo = MONTH([Date])
		dataType: int64
		isHidden
		lineageTag: 021dc2de-db4a-4e3b-91a9-6fec920acaf6
		dataCategory: MonthOfYear
		summarizeBy: none

		annotation SummarizationSetBy = User

		annotation TemplateId = MonthNumber

	column Month = FORMAT([Date], "MMMM")
		dataType: string
		isHidden
		lineageTag: b6b361f9-8cbb-4ab6-ad22-17dbf9e988ed
		dataCategory: Months
		summarizeBy: none
		sortByColumn: MonthNo

		annotation SummarizationSetBy = User

		annotation TemplateId = Month

	column QuarterNo = INT(([MonthNo] + 2) / 3)
		dataType: int64
		isHidden
		lineageTag: cd61e0d9-9e21-4821-b35d-3700a957e8dc
		dataCategory: QuarterOfYear
		summarizeBy: none

		annotation SummarizationSetBy = User

		annotation TemplateId = QuarterNumber

	column Quarter = "Qtr " & [QuarterNo]
		dataType: string
		isHidden
		lineageTag: bbbcbb17-be6d-4e10-978c-ce3a703d4a99
		dataCategory: Quarters
		summarizeBy: none
		sortByColumn: QuarterNo

		annotation SummarizationSetBy = User

		annotation TemplateId = Quarter

	column Day = DAY([Date])
		dataType: int64
		isHidden
		lineageTag: b3736660-9a31-4ada-aba9-8f7270a44a78
		dataCategory: DayOfMonth
		summarizeBy: none

		annotation SummarizationSetBy = User

		annotation TemplateId = Day

	hierarchy 'Date Hierarchy'
		lineageTag: bb32d615-9413-44a0-9e4f-1df74cc971c2

		level Year
			lineageTag: 1133d053-f74d-4085-a955-962ca3b856c9
			column: Year

		level Quarter
			lineageTag: a5e1ff9f-941b-4790-b68a-5dd731b1fe4f
			column: Quarter

		level Month
			lineageTag: fa660c40-b7a8-4198-be69-7742c4f8b13e
			column: Month

		level Day
			lineageTag: ae1105e4-66f8-49d5-8aa0-d3554732afc5
			column: Day

		annotation TemplateId = DateHierarchy

	partition DateTableTemplate_16a63790-2a75-46ea-8317-947b8304b455 = calculated
		mode: import
		source = Calendar(Date(2015,1,1), Date(2015,1,1))

	annotation __PBI_TemplateDateTable = true

	annotation DefaultItem = DateHierarchy

