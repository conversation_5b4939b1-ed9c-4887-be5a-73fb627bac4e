from enum import Enum


class Messages(Enum):
    USER_EXISTED_ERROR = "User Already Existing with the same data source details"
    USER_NOT_EXISTED = "User not existed"
    DATA_SOURCE_EXISTED = "Data source already existed"
    DATA_SOURCE_NOT_EXISTED = "Data source not existed"
    USER_DETAILS_ADDED = "User data source details are added successfully"
    USER_DETAILS_UPDATED = "User data source details are updated successfully"
    DATA_TYPE_NOT_EXISTED = "Data source type not existed in our list of data sources"
    USER_DETAILS_NOT_EXISTED = "User data source details are not existed"
    USER_DETAILS_REMOVED = "User data source details are removed successfully"
    USER_DETAILS_FETCHED_SUCCESSFULLY = "User data source details are fetched successfully"
    DATASOURCE_DETAILS_REMOVED = "Data source details are removed successfully"