import json, uuid

from app.core.enums import TableauXMLTags
from ..core import get_background_config, get_border_config, get_calc_filter_column, get_from_list, get_queryref, get_select_json, get_title_config
from app.core import map_json_template
from app.core import logger
from app.services.migrate.Tableau_Analyzer.report import (
    remove_duplicate_fields,
    extract_multiple_encodings_data,
    generate_projections_data
)
from app.core.enums import (
    ChartType, PowerBITemplateKeys,
    PowerBIReportKeys, PowerBIChartTypes, VisualRequest
)


def get_map_report(pane_encodings, table_column_data, datasource_col_list, worksheet_name, worksheet_title_layout, style):
    try:
        overall_map_result = []
        map_table_list, select_list = [], []
        category = pane_encodings.get(TableauXMLTags.TEXT.value,{}).get('@column')
        size = pane_encodings.get(TableauXMLTags.SIZE.value,{}).get('@column')
        if category and size:
            category_filter, category_column, category_table = get_calc_filter_column(category, table_column_data, datasource_col_list)
            size_filter, size_column, size_table = get_calc_filter_column(size, table_column_data, datasource_col_list)
            if category_table not in map_table_list: map_table_list.append(category_table)
            if size_table not in map_table_list: map_table_list.append(size_table)
            category_queryref = get_queryref(category_filter, category_column, category_table)
            size_queryref = get_queryref(size_filter, size_column, size_table)
            from_list = get_from_list(map_table_list)
            select_list.append(get_select_json(category_column, category_filter, category_table, category_queryref))
            select_list.append(get_select_json(size_column, size_filter, size_table, size_queryref))

            title_list = get_title_config(worksheet_title_layout, style)
            background_list = get_background_config(style= style)
            map_json = {
                "visual_config_name" : f'{str(uuid.uuid4()).replace("-","")[:20]}',
                "category_queryref" : category_queryref,
                "size_queryref" : size_queryref,
                "from_list" : json.dumps(from_list),
                "select_list" : json.dumps(select_list),
                "orderby_source" : size_table[0].lower(),
                "orderby_property" : size_column,
                "title_list" : json.dumps(title_list),
                "background_list" : json.dumps(background_list),
                "border_list" : get_border_config()
               
            }

            overall_map_result.append({"config": map_json, "template": map_json_template})
            return overall_map_result
        
    except Exception as e:
        logger.error(f"---Error in generating map visual for {worksheet_name}--- {str(e)}")
        raise ValueError(f"Error in generating map visual for {worksheet_name} - {str(e)}")


def process_map_report(request: VisualRequest):
    """
    Process map chart visual request.
    Parameters
    ----------
    request : VisualRequest
        The request object containing details for the map chart.
    Returns
    -------
    dict
        A dictionary containing the processed map chart data.
    """
    map_result = {}

    panes = request.panes
    table_column_data = request.table_column_data

    encodings = extract_multiple_encodings_data(
        panes=panes,
        tags_to_extract=[
            TableauXMLTags.TOOLTIP.value,
            TableauXMLTags.LOD.value,
            TableauXMLTags.TEXT.value,
            TableauXMLTags.COLOR.value,
            TableauXMLTags.SIZE.value
        ]
    )
    

    unique_text_data = remove_duplicate_fields(encodings[TableauXMLTags.TEXT.value])
    unique_size_data = remove_duplicate_fields(encodings[TableauXMLTags.SIZE.value])

    map_result[PowerBITemplateKeys.VISUAL_TYPE.value] = PowerBIChartTypes.MAP.value
    map_result[PowerBITemplateKeys.WORKSHEET_NAME.value] = request.worksheet_name
    map_chart_filed_mapping ={
        PowerBIReportKeys.SIZE.value : unique_size_data,
        PowerBIReportKeys.CATEGORY.value : unique_text_data
    }
    projections_data = generate_projections_data(table_column_data, map_chart_filed_mapping)
    
    map_result[PowerBIReportKeys.VISUAL_TYPE.value] = PowerBIChartTypes.MAP.value
    map_result[PowerBITemplateKeys.WORKSHEET_NAME.value] = request.worksheet_name
    map_result[PowerBITemplateKeys.PROJECTIONS_DATA.value] = projections_data
    
    return map_result

