from typing import List, Optional
from fastapi import UploadFile
from sqlalchemy import UUID

from app.core import scoped_context, ServiceResponse
from app.models_old.folders import Folder
from app.schemas.folders import FolderCreate, FolderResponse, FolderTree, FolderUpdate
from app.services.folder.folder_service import FolderService





class FolderManager:
    @staticmethod
    def get_all_active_folders():
        with scoped_context() as session:
            return session.query(Folder).filter(Folder.is_deleted == 0).all()

class FolderProcessor:
    @staticmethod
    async def process_get_all_folders() -> ServiceResponse:
        return await FolderService.execute(FolderService().get_all_folders)

    @staticmethod
    async def process_create_folder(folder: FolderCreate) -> ServiceResponse:
        return await FolderService.execute(FolderService().main_create_folder, folder)

    @staticmethod
    async def process_get_folders_by_parent(parent_id: Optional[UUID]) -> ServiceResponse:
        return await FolderService.execute(FolderService().get_folders_by_parent, parent_id)

    @staticmethod
    async def process_get_root_folders() -> ServiceResponse:
        return await FolderService.execute(FolderService().get_null_folders)
    
    @staticmethod
    async def process_soft_delete(folder_id: str) -> ServiceResponse:
        return await FolderService.execute(FolderService().soft_delete_folder, folder_id)
    
    @staticmethod
    async def process_update_folder(folder_id: UUID, folder_update: FolderUpdate) -> ServiceResponse:
        return await FolderService.execute(FolderService().update_folder, folder_id, folder_update)

    @staticmethod
    async def process_upload_file(uploaded_file: List[UploadFile], parent_id: Optional[str]) -> ServiceResponse:
        return await FolderService.execute(FolderService().upload_file, uploaded_file, parent_id)
    
    @staticmethod
    async def process_get_folder_tree(folder_id: UUID) -> ServiceResponse:
        return await FolderService.execute(FolderService().get_folder_tree, folder_id)
    

    @staticmethod
    async def process_get_folder_summary() -> ServiceResponse:
        return await FolderService.execute(FolderService().get_folder_summary)


    @staticmethod
    async def process_get_folder_summary_by_project_id(project_id: UUID) -> ServiceResponse:
        return await FolderService.execute(lambda: FolderService().get_folder_summary_by_project_id(project_id))