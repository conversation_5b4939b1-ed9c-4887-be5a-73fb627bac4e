FROM python:3.11.4-slim
 
EXPOSE 9090
 
RUN mkdir -p /app/log_files && chmod -R 777 /app/log_files
 
RUN apt-get update -y && \
    apt-get install -y python3-pip
 
RUN apt-get update && apt-get install -y ca-certificates
 
COPY ./requirements.txt /app/requirements.txt
 
WORKDIR /app
 
RUN pip install -r requirements.txt
 
COPY . .
 
CMD ["gunicorn", "-b", "0.0.0.0:9090", "-k", "uvicorn.workers.UvicornWorker", "run:app", "--workers", "2", "--timeout", "1200"]