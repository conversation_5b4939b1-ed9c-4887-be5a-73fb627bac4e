"""nullable_reportFalseupdate

Revision ID: 0c048dfe7ad8
Revises: 93943d732732
Create Date: 2025-06-17 18:01:55.483805

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0c048dfe7ad8'
down_revision: Union[str, None] = '93943d732732'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
   # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('server_report_details', 'dashboard_count')
    op.drop_column('server_report_details', 'worksheet_count')
    op.drop_column('server_report_details', 'calc_count')
    op.drop_column('server_report_details', 'datasource_count')

    op.add_column('server_report_details',
        sa.Column('dashboard_count', sa.Integer(), server_default='0', nullable=False)
    )
    op.add_column('server_report_details',
        sa.Column('worksheet_count', sa.Integer(), server_default='0', nullable=False)
    )
    op.add_column('server_report_details',
        sa.Column('calc_count', sa.Integer(), server_default='0', nullable=False)
    )
    op.add_column('server_report_details',
        sa.Column('datasource_count', sa.Integer(), server_default='0', nullable=False)
    )
    # ### end Alembic commands ###

def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('users', 'password',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('users', 'id',
               existing_type=sa.INTEGER(),
               server_default=sa.Identity(always=True, start=1, increment=1, minvalue=1, maxvalue=2147483647, cycle=False, cache=1),
               existing_nullable=False,
               autoincrement=True)
    op.drop_index(op.f('ix_upload_files_report_details_id'), table_name='upload_files_report_details')
    op.alter_column('upload_files_report_details', 'is_migrated',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('false'))
    op.alter_column('upload_files_report_details', 'is_converted',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('false'))
    op.alter_column('upload_files_report_details', 'is_analyzed',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('false'))
    op.alter_column('server_report_details', 'datasource_count',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.alter_column('server_report_details', 'calc_count',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.alter_column('server_report_details', 'worksheet_count',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.alter_column('server_report_details', 'dashboard_count',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.alter_column('folders', 'id',
               existing_type=sa.String(length=36),
               type_=sa.UUID(),
               existing_nullable=False)
    op.alter_column('files', 'folder_id',
               existing_type=sa.String(length=36),
               type_=sa.UUID(),
               existing_nullable=False)
    # ### end Alembic commands ###
