"""added new column into server_report_details

Revision ID: 50ad1fba00e5
Revises: 57ff7ed87652
Create Date: 2025-06-11 15:59:47.437624

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '50ad1fba00e5'
down_revision: Union[str, None] = '57ff7ed87652'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
  
    op.add_column('server_report_details', sa.Column('workbook_id', sa.UUID(), nullable=True))
   
    op.create_unique_constraint(None, 'server_report_details', ['workbook_id'])

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
 
    op.drop_constraint(None, 'server_report_details', type_='unique')
    op.drop_column('server_report_details', 'workbook_id')

    # ### end Alembic commands ###
