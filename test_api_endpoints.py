#!/usr/bin/env python3
"""
Test the API endpoints for login and workspace
"""
import requests
import json
import base64

# API base URL
BASE_URL = "http://127.0.0.1:9090/app_api"

def test_login():
    """Test the login endpoint"""
    print("🔐 Testing Login Endpoint")
    print("=" * 50)
    
    login_url = f"{BASE_URL}/users/login"
    login_data = {
        "email": "<EMAIL>",
        "password": "Raghava@123"
    }
    
    print(f"POST {login_url}")
    print(f"Body: {json.dumps(login_data, indent=2)}")
    
    try:
        response = requests.post(login_url, json=login_data)
        print(f"\nResponse Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"✅ Login Successful!")
            print(f"Response: {json.dumps(response_data, indent=2)}")
            
            # Extract token and email for next request
            if 'data' in response_data:
                access_token = response_data['data'].get('access_token')
                user_email = response_data['data'].get('user_email')  # This should be base64 encoded
                
                return access_token, user_email
            else:
                print("❌ No data in response")
                return None, None
        else:
            print(f"❌ Login Failed!")
            print(f"Response: {response.text}")
            return None, None
            
    except Exception as e:
        print(f"❌ Error during login: {e}")
        return None, None

def test_workspace_reports(access_token, user_email):
    """Test the workspace reports endpoint"""
    print(f"\n📊 Testing Workspace Reports Endpoint")
    print("=" * 50)
    
    if not access_token or not user_email:
        print("❌ Missing access token or user email")
        return
    
    workspace_url = f"{BASE_URL}/workspace/reports"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "X-User-Email": user_email,
        "Content-Type": "application/json"
    }
    
    print(f"GET {workspace_url}")
    print(f"Headers:")
    for key, value in headers.items():
        if key == "Authorization":
            print(f"  {key}: Bearer {access_token[:20]}...")
        else:
            print(f"  {key}: {value}")
    
    try:
        response = requests.get(workspace_url, headers=headers)
        print(f"\nResponse Status: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"✅ Workspace API Successful!")
            
            if 'data' in response_data and response_data['data']:
                reports = response_data['data']
                print(f"📈 Found {len(reports)} reports")
                
                # Show first few reports
                print(f"\nSample Reports:")
                for i, report in enumerate(reports[:3]):
                    print(f"  {i+1}. Report ID: {report.get('report_id', 'N/A')}")
                    print(f"     Report Name: {report.get('report_name', 'N/A')}")
                    print(f"     Project Name: {report.get('project_name', 'N/A')}")
                    print(f"     Status: Analyzed={report.get('is_analyzed', False)}, Converted={report.get('is_converted', False)}, Migrated={report.get('is_migrated', False)}")
                    print(f"     Status Enums: analyzed_status={report.get('analyzed_status')}, converted_status={report.get('converted_status')}, migrated_status={report.get('migrated_status')}")
                    print()
                
                if len(reports) > 3:
                    print(f"  ... and {len(reports) - 3} more reports")
                
                # Verify response format
                print(f"\n🔍 Response Format Verification:")
                if reports:
                    sample_report = reports[0]
                    required_fields = [
                        'report_id', 'report_name', 'project_name',
                        'is_analyzed', 'is_converted', 'is_migrated',
                        'analyzed_status', 'converted_status', 'migrated_status'
                    ]
                    
                    missing_fields = [field for field in required_fields if field not in sample_report]
                    if missing_fields:
                        print(f"❌ Missing required fields: {missing_fields}")
                    else:
                        print(f"✅ All required fields present")
                        
                    print(f"✅ Response format matches requirements")
                
            else:
                print(f"📭 No reports found for this user")
                print(f"Response: {json.dumps(response_data, indent=2)}")
                
        else:
            print(f"❌ Workspace API Failed!")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error during workspace API call: {e}")

def test_user_role_access():
    """Test with the specific user to verify role-based access"""
    print(f"\n👤 Testing Role-Based Access for Raghavendra (DEVELOPER)")
    print("=" * 60)
    
    # Login
    access_token, user_email = test_login()
    
    if access_token and user_email:
        # Test workspace
        test_workspace_reports(access_token, user_email)
        
        print(f"\n📋 Expected Behavior for DEVELOPER Role:")
        print(f"  - Should see only reports for projects assigned to them")
        print(f"  - Based on our database test, Raghavendra should see 2 reports")
        print(f"  - Reports should be from Project_7 (Report_16 and Report_19)")
    else:
        print(f"❌ Could not test workspace due to login failure")

if __name__ == "__main__":
    print("🧪 API Endpoint Testing")
    print("=" * 60)
    print("Testing the workspace API implementation with role-based filtering")
    print()
    
    try:
        test_user_role_access()
        
        print(f"\n🎉 API Testing Completed!")
        print(f"\nIf successful, this confirms:")
        print(f"  ✅ Login endpoint working")
        print(f"  ✅ JWT token generation working")
        print(f"  ✅ Workspace endpoint accessible")
        print(f"  ✅ Role-based filtering implemented")
        print(f"  ✅ Session-bound user context working")
        print(f"  ✅ Response format correct")
        
    except Exception as e:
        print(f"❌ API testing failed: {e}")
        import traceback
        traceback.print_exc()
