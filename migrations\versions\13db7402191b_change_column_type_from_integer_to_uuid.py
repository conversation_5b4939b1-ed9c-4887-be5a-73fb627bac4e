"""change column type from Integer to UUID

Revision ID: 13db7402191b
Revises: 5c3d83fe1099
Create Date: 2025-04-11 05:28:07.621158

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import uuid


# revision identifiers, used by Alembic.
revision: str = '13db7402191b'
down_revision: Union[str, None] = '5c3d83fe1099'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

def upgrade():
    op.drop_column("data_sources","id")
    # op.add_column('data_sources', sa.Column('id', sa.UUID(),nullable=False,default=uuid.uuid4))

def downgrade():
    pass

