"""Add assigned_to column to project_details

Revision ID: 0c76e4c4ef02
Revises: 3f020f353051
Create Date: 2025-07-09 19:06:36.162909

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0c76e4c4ef02'
down_revision: Union[str, None] = '3f020f353051'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('project_details', sa.Column('assigned_to', sa.String(), nullable=True), schema= 'biport_dev')

def downgrade() -> None:
    op.drop_column('project_details', 'assigned_to', schema= 'biport_dev')

