import re
from app.core.enums import GeneralKeys as GS, WorkSheet as WS, ChartType
from app.core.regex_enums import Regex as RE
from .common import get_quantitative_columns, get_rows_cols_details
from app.core import logger
from .common import check_common_structure

class Table:
    @staticmethod
    def check_table(worksheet):
        valid, pane, rows_details, cols_details, quantitative_cols = check_common_structure(worksheet, require_quantitative=True)

        if not valid:
            return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

        text_tag = pane.find(WS.ENCODING_TEXT.value)
        if text_tag is None:
            return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

        text_column_full = text_tag.get(WS.COLUMN.value)
        if not text_column_full:
            logger.debug("No column attribute on <text> tag.")
            return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

        text_column = text_column_full.split(RE.DOT.value)[-1].strip('[]')
        logger.debug(f"Detected text column: {text_column}")

        for col in quantitative_cols:
            col_name = col[GS.COLUMN_INSTANCE.value].get(WS.NAME.value, '').strip('[]')
            logger.debug(f"Comparing with quantitative column: {col_name}")
            if col_name == text_column:
                chart_type = (
                    ChartType.PIVOT_TABLE.value if (rows_details and cols_details)
                    else ChartType.TEXT_TABLE.value
                )
                return {GS.STATUS.value: True, GS.CHART_TYPE.value: chart_type}

        pattern = r'^\[[^:]+:[^:]+:[^\]]+\]$'
        if re.match(pattern, text_column):
            logger.debug("Text column matches invalid pattern — rejecting.")
            return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

        chart_type = (
            ChartType.PIVOT_TABLE.value if (rows_details and cols_details)
            else ChartType.TEXT_TABLE.value
        )
        return {GS.STATUS.value: True, GS.CHART_TYPE.value: chart_type}
