from itertools import islice
from typing import Iterator
from os import makedirs
from os.path import dirname
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from openai import APIConnectionError, AuthenticationError

from app.core import  AuthenticationError, logger
from app.core.config import OpenAIConfig




openai_config = OpenAIConfig()
client = openai_config.get_openai_client()

def chunk_list(data, chunk_size) -> Iterator[list]:
    #Splits a list into chunks of a specified size.
    iterator = iter(data)
    return iter(lambda: list(islice(iterator, chunk_size)), [])


def get_openai_response(tab_formula, table_info):
    try:
        response = client.chat.completions.create(
            model='gpt-4o',
            messages=[{
        "role": "system",
        "content": [
            {
            "type": "text",
            "text": '''You are skilled in both Tableau and Power BI. Your role involves transforming a Tableau calculated field formula into a corresponding DAX formula for Power BI, making sure it accurately utilizes the table and column names provided. Employ the SELECTEDVALUE function solely for scenarios where a column is compared to specific values within logical functions like IF or SWITCH, ensuring the DAX formula operates effectively within a measure context. Avoid using SELECTEDVALUE for simple column references that are being concatenated or manipulated without conditional logic.

            Input Format:

            Tableau Formula: A Tableau calculated field formula requiring conversion.
            Table Information: A dictionary with table names and their associated columns.

            Output Instructions:

            1. Provide the Power BI equivalent DAX formula. Utilize SELECTEDVALUE only where necessary.
            2. Output the formula as a single line, strictly don't include ```DAX and ``` at the start or end.
            3. Do not introduce any aggregations unless they are explicitly present in the Tableau formula.'''
            }
        ]
        },
        {
        "role": "user",
        "content": [
            {
            "type": "text",
            "text": f"Tableau Formula:{tab_formula}, Table Information:{table_info}"
            }
        ]
        }]          
        )
        return response.choices[0].message.content
    except APIConnectionError as e:
        logger.error(f"------Issue in connecting to OpenAI API: {str(e)}")
        raise ValueError('Issue in connecting to OpenAI API')
    except AuthenticationError as e:
        logger.error(f"------OpenAI key or token was invalid, expired, or revoked.: {str(e)}")
        raise ValueError('OpenAI key or token was invalid, expired, or revoked.')


def generate_pdf(data: dict, pdf_path: str):

    makedirs(dirname(pdf_path), exist_ok=True)

    doc = SimpleDocTemplate(pdf_path, pagesize=A4,
                            rightMargin=30, leftMargin=30,
                            topMargin=50, bottomMargin=40)
    elements = []
    styles = getSampleStyleSheet()
    title_style = styles["Title"]
    heading_style = styles["Heading2"]
    normal_style = styles["BodyText"]

    # Add title
    elements.append(Paragraph("Tableau Calculated Fields and PowerBI DAX", title_style))
    elements.append(Spacer(1, 20))

    # 👇 Safely unpack the "dax" dictionary
    dax_data = data.get("dax", {})

    for file_name, formulas in dax_data.items():
        elements.append(Paragraph(f"Workbook: {file_name}", heading_style))
        elements.append(Spacer(1, 10))

        table_data = [["Calculated Field", "PowerBI DAX"]]

        if isinstance(formulas, str):
            table_data.append([formulas, ""])
        elif isinstance(formulas, list):
            for row in formulas:
                if not isinstance(row, dict):
                    row = {"cal_field": str(row), "powerbi_dax": ""}

                field = Paragraph(str(row.get("cal_field", "")).strip().replace("\n", "<br />"), normal_style)
                dax = Paragraph(str(row.get("powerbi_dax", "")).strip().replace("\n", "<br />"), normal_style)
                table_data.append([field, dax])
        else:
            table_data.append(["Invalid data format", ""])

        table = Table(table_data, colWidths=[doc.width / 2.0] * 2)
        table.setStyle(TableStyle([
            ("BACKGROUND", (0, 0), (-1, 0), colors.HexColor("#D3D3D3")),
            ("TEXTCOLOR", (0, 0), (-1, 0), colors.black),
            ("ALIGN", (0, 0), (-1, -1), "LEFT"),
            ("FONTNAME", (0, 0), (-1, 0), "Helvetica-Bold"),
            ("FONTSIZE", (0, 0), (-1, -1), 9),
            ("BOTTOMPADDING", (0, 0), (-1, 0), 8),
            ("GRID", (0, 0), (-1, -1), 0.5, colors.grey),
        ]))

        elements.append(table)
        elements.append(Spacer(1, 20))

    doc.build(elements)
    return pdf_path