"""updated data_sources table

Revision ID: afdd419d770b
Revises: ba480bcde273
Create Date: 2025-04-14 18:54:53.687792

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'afdd419d770b'
down_revision: Union[str, None] = 'ba480bcde273'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('data_sources', sa.Column('created_at', sa.DateTime(), nullable=True))
    op.add_column('data_sources', sa.Column('updated_at', sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('data_sources', 'updated_at')
    op.drop_column('data_sources', 'created_at')
    # ### end Alembic commands ###
