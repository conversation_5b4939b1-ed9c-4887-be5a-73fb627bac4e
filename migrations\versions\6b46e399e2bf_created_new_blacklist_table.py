"""created new blacklist table

Revision ID: 6b46e399e2bf
Revises: 60da7820da40
Create Date: 2025-06-10 16:32:59.252708

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6b46e399e2bf'
down_revision: Union[str, None] = '60da7820da40'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('token_blacklist',
    sa.Column('token_hash', sa.String(), nullable=False),
    sa.Column('expires_at', sa.DateTime(), nullable=False),
    sa.Column('blacklisted_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('token_hash')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('token_blacklist')
    # ### end Alembic commands ###
