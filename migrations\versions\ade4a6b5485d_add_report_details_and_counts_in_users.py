"""Add report_details and counts in users

Revision ID: ade4a6b5485d
Revises: c6f926715b64
Create Date: 2025-05-30 12:57:10.371874

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ade4a6b5485d'
down_revision: Union[str, None] = 'c6f926715b64'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

def upgrade() -> None:
    # ### commands auto generated by Alembic - adjusted for native UUID types ###
    op.create_table(
        's3_credentials',
        sa.Column('id', sa.UUID(as_uuid=True), nullable=False),
        sa.Column('bucket_name', sa.String(), nullable=False),
        sa.Column('region', sa.String(), nullable=False),
        sa.Column('access_key', sa.String(), nullable=False),
        sa.Column('secret_key', sa.String(), nullable=False),
        sa.Column('organization_id', sa.UUID(as_uuid=True), nullable=False),
        sa.ForeignKeyConstraint(['organization_id'], ['organization_details.id']),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_s3_credentials_id'), 's3_credentials', ['id'], unique=False)

    op.create_table(
        'report_details',
        sa.Column('id', sa.UUID(as_uuid=True), nullable=False),
        sa.Column('report_name', sa.String(), nullable=False),
        sa.Column('is_analised', sa.Boolean(), nullable=True),
        sa.Column('is_converted', sa.Boolean(), nullable=True),
        sa.Column('is_migrated', sa.Boolean(), nullable=True),
        sa.Column('serverid', sa.UUID(as_uuid=True), nullable=False),
        sa.ForeignKeyConstraint(['serverid'], ['server_details.id']),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_report_details_id'), 'report_details', ['id'], unique=False)

    op.alter_column('files', 'folder_id',
        existing_type=sa.UUID(),
        type_=sa.UUID(as_uuid=True),
        existing_nullable=False
    )
    op.create_foreign_key(None, 'files', 'folders', ['folder_id'], ['id'])

    op.alter_column('folders', 'id',
        existing_type=sa.UUID(),
        type_=sa.UUID(as_uuid=True),
        existing_nullable=False
    )

    op.add_column('user_reports', sa.Column('report_count', sa.Integer(), nullable=True))
    op.add_column('user_reports', sa.Column('project_count', sa.Integer(), nullable=True))
    op.add_column('user_reports', sa.Column('dashboard_count', sa.Integer(), nullable=True))
    op.add_column('user_reports', sa.Column('worksheet_count', sa.Integer(), nullable=True))
    op.add_column('user_reports', sa.Column('calc_count', sa.Integer(), nullable=True))
    op.add_column('user_reports', sa.Column('datasource_count', sa.Integer(), nullable=True))
    # ### end Alembic commands ###



def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_reports', 'datasource_count')
    op.drop_column('user_reports', 'calc_count')
    op.drop_column('user_reports', 'worksheet_count')
    op.drop_column('user_reports', 'dashboard_count')
    op.drop_column('user_reports', 'project_count')
    op.drop_column('user_reports', 'report_count')
    op.alter_column('folders', 'id',
               existing_type=sa.String(length=36),
               type_=sa.UUID(),
               existing_nullable=False)
    op.drop_constraint(None, 'files', type_='foreignkey')
    op.alter_column('files', 'folder_id',
               existing_type=sa.String(length=36),
               type_=sa.UUID(),
               existing_nullable=False)
    op.drop_index(op.f('ix_report_details_id'), table_name='report_details')
    op.drop_table('report_details')
    op.drop_index(op.f('ix_s3_credentials_id'), table_name='s3_credentials')
    op.drop_table('s3_credentials')
    # ### end Alembic commands ###
