from typing import Dict, Any
from xml.etree.ElementTree import Element
from app.core.enums import ChartType, GeneralKeys as GS, WorkSheet as WS
from app.core.regex_enums import Regex as RE
from .common import extract_column_names

class Line:
    @staticmethod
    def check_line_chart(worksheet: Element) -> Dict[str, Any]:
        # Check for path encoding
        panes = worksheet.findall(WS.PANES_PANE.value)
        has_path_attrib = any(pane.find(WS.PANES_PATH_ATT.value) is not None for pane in panes)
        if has_path_attrib:
            return {GS.STATUS.value: True, GS.CHART_TYPE.value: ChartType.LINE.value}

        # Extract row and column text
        rows_elem = worksheet.find(WS.ROWS.value)
        cols_elem = worksheet.find(WS.COLS.value)
        rows_text = rows_elem.text if rows_elem is not None else RE.EMPTY.value
        cols_text = cols_elem.text if cols_elem is not None else RE.EMPTY.value
        rows = extract_column_names(rows_text) if rows_text else []
        cols = extract_column_names(cols_text) if cols_text else []

        # Build column maps
        column_instances = worksheet.findall(WS.DS_COL_INSTANCES.value) or []
        columns = worksheet.findall(WS.DS_COLS.value) or []
        column_map = {}

        for instance in column_instances:
            column_map[instance.get(WS.NAME.value)] = {
                WS.COLUMN.value: instance.get(WS.COLUMN.value),
                WS.NAME.value: instance.get(WS.NAME.value),
                WS.TYPE.value: instance.get(WS.TYPE.value),
                WS.DATATYPE.value: None,
                WS.ROLE.value: None,
                "is_calculated": False
            }

        column_lookup = {col.get(WS.NAME.value): col for col in columns}

        for name, details in column_map.items():
            column_name = details[WS.COLUMN.value]
            if column_name in column_lookup:
                col = column_lookup[column_name]
                details[WS.DATATYPE.value] = col.get(WS.DATATYPE.value)
                details[WS.ROLE.value] = col.get(WS.ROLE.value)
                details[WS.TYPE.value] = col.get(WS.TYPE.value)
                details["is_calculated"] = col.find("calculation") is not None

        # Analyze rows and cols
        def analyze_columns(column_list):
            column_details = []
            for col_name in column_list:
                if col_name in column_map:
                    column_details.append(column_map[col_name])
                elif col_name == "[:Measure Names]":
                    column_details.append({
                        WS.COLUMN.value: "[:Measure Names]",
                        WS.NAME.value: "Measure Names",
                        WS.TYPE.value: "nominal",
                        WS.DATATYPE.value: "string",
                        WS.ROLE.value: "dimension",
                        "is_calculated": False
                    })
                elif col_name == "[Multiple Values]":
                    column_details.append({
                        WS.COLUMN.value: "[Multiple Values]",
                        WS.NAME.value: "Measure Values",
                        WS.TYPE.value: "quantitative",
                        WS.DATATYPE.value: "string",
                        WS.ROLE.value: "measure",
                        "is_calculated": False
                    })
            return column_details

        rows_details = analyze_columns(rows)
        cols_details = analyze_columns(cols)

        # At least one quantitative column
        if sum(1 for col in rows_details + cols_details if col[WS.TYPE.value] == 'quantitative') == 0:
            return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

        date_types = {"date", "datetime"}
        date_in_rows = []
        date_in_cols = []

        for col in rows_details:
            if col[WS.DATATYPE.value] in date_types:
                date_in_rows.append(col)
            elif col["is_calculated"] and col[WS.DATATYPE.value] == "integer" and col[WS.NAME.value].lower().startswith("[none:"):
                date_in_rows.append(col)

        for col in cols_details:
            if col[WS.DATATYPE.value] in date_types:
                date_in_cols.append(col)
            elif col["is_calculated"] and col[WS.DATATYPE.value] == "integer" and col[WS.NAME.value].lower().startswith("[none:"):
                date_in_cols.append(col)

        # Date column alone in row or col
        if (date_in_rows and len(rows_details) == 1) or (date_in_cols and len(cols_details) == 1):
            return {GS.STATUS.value: True, GS.CHART_TYPE.value: ChartType.LINE.value}

        # Date column at end of rows or cols
        if date_in_rows:
            for row in date_in_rows:
                if rows_details.index(row) == len(rows_details) - 1:
                    return {GS.STATUS.value: True, GS.CHART_TYPE.value: ChartType.LINE.value}

        if date_in_cols:
            for col in date_in_cols:
                if cols_details.index(col) == len(cols_details) - 1:
                    return {GS.STATUS.value: True, GS.CHART_TYPE.value: ChartType.LINE.value}

        return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}