"""changing data type of the columns in data_sources

Revision ID: e05795a81690
Revises: c62f95afed1b
Create Date: 2025-04-03 23:08:37.648289

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'e05795a81690'
down_revision: Union[str, None] = 'c62f95afed1b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    # Change key and encoded_data to String
    op.alter_column('data_sources', 'key', type_=sa.String())
    op.alter_column('data_sources', 'encoded_data', type_=sa.String())

def downgrade():
    # Change key and encoded_data back to LargeBinary
    op.alter_column('data_sources', 'key', type_=sa.LargeBinary())
    op.alter_column('data_sources', 'encoded_data', type_=sa.LargeBinary())
