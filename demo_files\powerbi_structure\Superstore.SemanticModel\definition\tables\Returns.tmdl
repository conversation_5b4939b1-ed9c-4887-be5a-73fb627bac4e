table Returns
	lineageTag: e627590a-bf92-47cd-a95b-c5e8f2d03bac

	column Returned
		dataType: string
		lineageTag: e5fd2692-da38-44f0-99f9-cf363c7dc378
		summarizeBy: none
		sourceColumn: Returned

		annotation SummarizationSetBy = Automatic

	column 'Order ID'
		dataType: string
		lineageTag: c07e82cb-6793-40f9-b6ac-db9b99ded0bb
		summarizeBy: none
		sourceColumn: Order ID

		annotation SummarizationSetBy = Automatic

	partition Returns = m
		mode: import
		source =
				let
				    Source = Excel.Workbook(File.Contents("C:\Users\<USER>\Downloads\Sample - Superstoree.xlsx"), null, true),
				    Returns_Sheet = Source{[Item="Returns",Kind="Sheet"]}[Data],
				    #"Changed Type" = Table.TransformColumnTypes(Returns_Sheet,{{"Column1", type text}, {"Column2", type text}}),
				    #"Promoted Headers" = Table.PromoteHeaders(#"Changed Type", [PromoteAllScalars=true]),
				    #"Changed Type1" = Table.TransformColumnTypes(#"Promoted Headers",{{"Returned", type text}, {"Order ID", type text}})
				in
				    #"Changed Type1"

	annotation PBI_ResultType = Table

	annotation PBI_NavigationStepName = Navigation

