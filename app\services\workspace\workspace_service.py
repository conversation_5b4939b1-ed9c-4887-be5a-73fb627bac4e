from app.core import BaseService, ServiceResponse
from app.models.project_details import ProjectDetail
from app.models.report_details import ReportDetail
from sqlalchemy.orm import joinedload
from sqlalchemy import or_
from uuid import UUID
from app.models.users import User, UserManager
from app.core.logger_setup import logger
from app.core.session import scoped_context


class WorkspaceService(BaseService):
    def get_reports_by_user_role(self, user) -> ServiceResponse:
        with scoped_context() as session:
            try:
                logger.info(f"[WorkspaceService] Starting - Fetching reports for user ID: {user.id}")
                print(f"[DEBUG] Starting - Fetching reports for user ID: {user.id}")

                # Reload user with session-bound context to avoid DetachedInstanceError
                logger.info(f"[WorkspaceService] Reloading user with session context")
                print(f"[DEBUG] Reloading user with session context")

                session_user = UserManager.get_user_by_id_with_relations(str(user.id), session)
                if not session_user:
                    logger.error("[WorkspaceService] User not found in database")
                    print(f"[ERROR] User not found in database")
                    return ServiceResponse.failure("User not found", 404)

                logger.info(f"[WorkspaceService] User reloaded successfully: {session_user.email}")
                print(f"[DEBUG] User reloaded successfully: {session_user.email}")

                # Get role name from session-bound user
                logger.info(f"[WorkspaceService] Getting role information")
                print(f"[DEBUG] Getting role information")

                role_name = session_user.role.name.value if session_user.role and session_user.role.name else None
                if not role_name:
                    logger.error("[WorkspaceService] User role not found")
                    print(f"[ERROR] User role not found")
                    return ServiceResponse.failure("User role not found", 400)

                logger.info(f"[WorkspaceService] User role: {role_name}")
                print(f"[DEBUG] User role: {role_name}")

                # Get organization ID from session-bound user
                organization_id = session_user.organization_id
                logger.info(f"[WorkspaceService] User organization ID: {organization_id}")
                print(f"[DEBUG] User organization ID: {organization_id}")

                # Build reports query with specific columns only (avoid audit columns)
                reports_query = (
                    session.query(
                        ReportDetail.id,
                        ReportDetail.name,
                        ReportDetail.is_analyzed,
                        ReportDetail.is_converted,
                        ReportDetail.is_migrated,
                        ReportDetail.analyzed_status,
                        ReportDetail.converted_status,
                        ReportDetail.migrated_status,
                        ReportDetail.unit_tested,
                        ReportDetail.uat_tested,
                        ReportDetail.deployed,
                        ProjectDetail.name.label('project_name')
                    )
                    .join(ProjectDetail, ReportDetail.project_id == ProjectDetail.id)
                )

                # Apply role-based filtering (handle both uppercase and title case)
                role_upper = role_name.upper() if role_name else ""

                if role_upper == "ADMIN":
                    # Admin sees all reports in their organization
                    reports_query = reports_query.join(
                        User, ProjectDetail.user_id == User.id
                    ).filter(User.organization_id == organization_id)
                    logger.info("[WorkspaceService] Applied ADMIN filter - all reports in organization")

                elif role_upper == "MANAGER":
                    # Manager sees reports for projects assigned to them or their subordinates
                    subordinate_ids = session.query(User.id).filter(User.manager_id == session_user.id).all()
                    subordinate_ids = [sid[0] for sid in subordinate_ids]
                    logger.info(f"[WorkspaceService] Manager subordinate IDs: {subordinate_ids}")
                    print(f"[DEBUG] Manager subordinate IDs: {subordinate_ids}")

                    if subordinate_ids:
                        reports_query = reports_query.filter(
                            or_(
                                ProjectDetail.assigned_to == session_user.id,
                                ProjectDetail.assigned_to.in_(subordinate_ids)
                            )
                        )
                    else:
                        reports_query = reports_query.filter(ProjectDetail.assigned_to == session_user.id)

                    # Also filter by organization
                    reports_query = reports_query.join(
                        User, ProjectDetail.user_id == User.id
                    ).filter(User.organization_id == organization_id)
                    logger.info("[WorkspaceService] Applied MANAGER filter")

                elif role_upper == "DEVELOPER":
                    # Developer sees only reports for projects assigned to them
                    reports_query = reports_query.filter(
                        ProjectDetail.assigned_to == session_user.id
                    ).join(
                        User, ProjectDetail.user_id == User.id
                    ).filter(User.organization_id == organization_id)
                    logger.info("[WorkspaceService] Applied DEVELOPER filter")

                else:
                    logger.error(f"[WorkspaceService] Unknown role: {role_name}")
                    return ServiceResponse.failure(f"Unknown user role: {role_name}", 400)

                # Execute query
                reports = reports_query.all()
                logger.info(f"[WorkspaceService] Found {len(reports)} reports")
                print(f"[DEBUG] Found {len(reports)} reports")

                # Format response according to requirements
                report_data = []
                for report in reports:
                    try:
                        # Since we're selecting specific columns, report is now a tuple
                        report_item = {
                            "report_id": str(report.id),
                            "report_name": report.name,
                            "project_name": report.project_name,
                            "is_analyzed": report.is_analyzed,
                            "is_converted": report.is_converted,
                            "is_migrated": report.is_migrated,
                            "analyzed_status": report.analyzed_status.value if report.analyzed_status else None,
                            "converted_status": report.converted_status.value if report.converted_status else None,
                            "migrated_status": report.migrated_status.value if report.migrated_status else None,
                            "unit_tested": report.unit_tested,
                            "uat_tested": report.uat_tested,
                            "deployed": report.deployed
                        }
                        report_data.append(report_item)
                    except Exception as e:
                        logger.error(f"[WorkspaceService] Error formatting report {report.id}: {e}")
                        print(f"[ERROR] Error formatting report {report.id}: {e}")
                        continue

                logger.info(f"[WorkspaceService] Successfully formatted {len(report_data)} reports")
                return ServiceResponse.success(report_data)

            except Exception as e:
                logger.error(f"[WorkspaceService] Error in get_reports_by_user_role: {e}", exc_info=True)
                print(f"[ERROR] Exception in WorkspaceService: {e}")
                return ServiceResponse.failure(f"Error fetching reports: {str(e)}", 500)

    def update_report_status(self, report_id: str, status_update, user) -> ServiceResponse:
        """
        Update the unit_tested, uat_tested, and deployed flags for a specific report.
        Reuses existing role-based access control from ReportDetail.get_reports_by_user_role()
        """
        with scoped_context() as session:
            try:
                logger.info(f"[WorkspaceService] Starting - Updating report status for report ID: {report_id}")
                print(f"[DEBUG] Updating report status for report ID: {report_id}")

                # Reload user with session-bound context
                session_user = UserManager.get_user_by_id_with_relations(str(user.id), session)
                if not session_user:
                    logger.error("[WorkspaceService] User not found in database")
                    return ServiceResponse.failure("User not found", 404)

                # Get role name from session-bound user
                role_name = session_user.role.name.value if session_user.role and session_user.role.name else None
                if not role_name:
                    logger.error("[WorkspaceService] User role not found")
                    return ServiceResponse.failure("User role not found", 400)

                logger.info(f"[WorkspaceService] User role: {role_name}")
                print(f"[DEBUG] User role: {role_name}")

                # Convert report_id to UUID
                try:
                    report_uuid = UUID(report_id)
                except ValueError:
                    logger.error(f"[WorkspaceService] Invalid report ID format: {report_id}")
                    return ServiceResponse.failure("Invalid report ID format", 400)

                # Create a simple query to check if user has access to this specific report
                # Use specific column selection to avoid audit columns issue
                report_query = session.query(
                    ReportDetail.id,
                    ReportDetail.name,
                    ReportDetail.unit_tested,
                    ReportDetail.uat_tested,
                    ReportDetail.deployed
                ).filter(ReportDetail.id == report_uuid)

                # Apply the same role-based filtering logic as in get_reports_by_user_role
                role_upper = role_name.upper() if role_name else ""
                organization_id = session_user.organization_id

                if role_upper == "ADMIN":
                    # Admin sees all reports in their organization
                    report_query = report_query.join(
                        ProjectDetail, ReportDetail.project_id == ProjectDetail.id
                    ).join(
                        User, ProjectDetail.user_id == User.id
                    ).filter(User.organization_id == organization_id)

                elif role_upper == "MANAGER":
                    # Manager sees reports for projects assigned to them or their subordinates
                    subordinate_ids = session.query(User.id).filter(User.manager_id == session_user.id).all()
                    subordinate_ids = [sid[0] for sid in subordinate_ids]

                    if subordinate_ids:
                        report_query = report_query.join(
                            ProjectDetail, ReportDetail.project_id == ProjectDetail.id
                        ).filter(
                            or_(
                                ProjectDetail.assigned_to == session_user.id,
                                ProjectDetail.assigned_to.in_(subordinate_ids)
                            )
                        ).join(
                            User, ProjectDetail.user_id == User.id
                        ).filter(User.organization_id == organization_id)
                    else:
                        report_query = report_query.join(
                            ProjectDetail, ReportDetail.project_id == ProjectDetail.id
                        ).filter(
                            ProjectDetail.assigned_to == session_user.id
                        ).join(
                            User, ProjectDetail.user_id == User.id
                        ).filter(User.organization_id == organization_id)

                elif role_upper == "DEVELOPER":
                    # Developer sees only reports for projects assigned to them
                    report_query = report_query.join(
                        ProjectDetail, ReportDetail.project_id == ProjectDetail.id
                    ).filter(
                        ProjectDetail.assigned_to == session_user.id
                    ).join(
                        User, ProjectDetail.user_id == User.id
                    ).filter(User.organization_id == organization_id)
                else:
                    logger.error(f"[WorkspaceService] Unknown role: {role_name}")
                    return ServiceResponse.failure(f"Unknown user role: {role_name}", 400)

                # Get the report (this returns a tuple due to specific column selection)
                report_tuple = report_query.first()

                if not report_tuple:
                    logger.error(f"[WorkspaceService] Report not found or user doesn't have access: {report_id}")
                    return ServiceResponse.failure("Report not found or access denied", 404)

                logger.info(f"[WorkspaceService] Report found: {report_tuple.name}")
                print(f"[DEBUG] Report found: {report_tuple.name}")

                # Prepare update data and fields list
                update_data = {}
                updated_fields = []

                if status_update.unit_tested is not None:
                    update_data['unit_tested'] = status_update.unit_tested
                    updated_fields.append(f"unit_tested={status_update.unit_tested}")

                if status_update.uat_tested is not None:
                    update_data['uat_tested'] = status_update.uat_tested
                    updated_fields.append(f"uat_tested={status_update.uat_tested}")

                if status_update.deployed is not None:
                    update_data['deployed'] = status_update.deployed
                    updated_fields.append(f"deployed={status_update.deployed}")

                if not updated_fields:
                    logger.warning("[WorkspaceService] No fields to update")
                    return ServiceResponse.failure("No fields provided for update", 400)

                # Use bulk update to avoid loading the full object with audit columns
                session.query(ReportDetail).filter(ReportDetail.id == report_uuid).update(update_data)

                # Commit the changes
                session.commit()

                logger.info(f"[WorkspaceService] Successfully updated report status: {', '.join(updated_fields)}")
                print(f"[DEBUG] Updated fields: {', '.join(updated_fields)}")

                return ServiceResponse.success({
                    "message": f"Report status updated successfully: {', '.join(updated_fields)}",
                    "report_id": report_id,
                    "updated_fields": updated_fields
                })

            except Exception as e:
                session.rollback()
                logger.error(f"[WorkspaceService] Error in update_report_status: {e}", exc_info=True)
                print(f"[ERROR] Exception in WorkspaceService update_report_status: {e}")
                return ServiceResponse.failure(f"Error updating report status: {str(e)}", 500)