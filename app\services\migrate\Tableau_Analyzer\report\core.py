import re
import xmltodict
import xml.etree.ElementTree as ET
from collections import OrderedDict
from typing import List, Tuple, Any, Dict
from app.core.enums import PowerBITemplate<PERSON><PERSON>s


def convert_xml_to_dict(data):
    try:
        string_data = ET.tostring(data, encoding='utf-8')
        return xmltodict.parse(string_data)
    except Exception as e:
        raise ValueError(f"Error in converting xml to dict")


def extract_datasource_filter_column(data: str) -> Tuple[str | None, str | None, str | None]:
    """
    Extracts the datasource ID, column value, and filter value from the input string.

    Example:
        "[federated.xyz].[none:Sub-Category:nk]" → ('federated.xyz', 'Sub-Category', 'none')
        "[federated.abc].[cum:sum:Profit:qk]"    → ('federated.abc', 'Profit', 'sum')

    Returns:
        Tuple of (datasource_id, column_value, filter_value) or (None, None, None) if format is invalid.
    """
    match = re.match(r'\[([^\]]+)\]\.\[([^\]]+)\]', data)
    if not match:
        return None, None, None

    datasource_id, filter_info = match.groups()
    parts = filter_info.split(':')

    if len(parts) < 3:
        return datasource_id, None, None

    column_value = parts[-2]
    filter_value = parts[-3]
    
    return datasource_id, column_value, filter_value

def get_fields_data(data):
    if data is None: return []
    queryref_string_list=re.findall(r'\[.*?\].\[.*?\]', data)
    return queryref_string_list

def remove_duplicate_fields(*args: List[Any]) -> List[Any]:

    combined_data = [item for lst in args for item in lst]
    return list(OrderedDict.fromkeys(combined_data))

def find_table_name(table_columns, datasource_name, column_name):
    """Find the table name for a given datasource and column."""
    datasource = next(
        (ds for ds in table_columns if ds['datasource'] == datasource_name),
        None
    )
    if not datasource:
        return None

    for table_name, columns in datasource['table_columns'].items():
        if any(col['column_name'] == column_name for col in columns):
            return table_name

    return None

def generate_projections_data(table_columns_data: List[Dict], field_mapping: Dict[str, List]) -> Dict:
    """
    Generates projection data for multiple visual encodings like rows, columns, tooltips, colors, etc.
    
    Args:
        table_columns_data (List[Dict]): The table columns and metadata.
        field_mapping (Dict[str, List]): A mapping from semantic roles (e.g., Y, CATEGORY) to fields.
    
    Returns:
        Dict: Mapping of PowerBIReportKeys to corresponding processed projection data.
    """
    projections = {}
    for key, fields in field_mapping.items():
        if fields:
            projections[key] = []
            for field in fields:
                if field:
                    datasource_id, column_value, filter_value = extract_datasource_filter_column(field)
                    table_name = find_table_name(
                        table_columns=table_columns_data,
                        datasource_name=datasource_id,
                        column_name=column_value
                    )
                    projections[key].append({
                        PowerBITemplateKeys.COLUMN.value: column_value,
                        PowerBITemplateKeys.FILTER.value: filter_value,
                        PowerBITemplateKeys.TABLE_NAME.value: table_name
                    })
    return projections
