#!/usr/bin/env python3
"""
Test the workspace queries directly against the database
"""
import psycopg2
import sys

# Database connection parameters
DB_CONFIG = {
    'host': 'localhost',
    'port': '5432',
    'user': 'postgres',
    'password': 'postgres',
    'database': 'test'
}

def get_connection():
    """Get database connection"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except psycopg2.Error as e:
        print(f"Error connecting to database: {e}")
        return None

def test_role_based_queries():
    """Test role-based filtering queries"""
    conn = get_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        print("=== Testing Role-Based Workspace Queries ===\n")
        
        # Get sample users with their roles
        cursor.execute("""
            SELECT u.id, u.name, u.email, r.name as role_name, u.organization_id
            FROM biport_dev.users u
            JOIN biport_dev.roles r ON u.role_id = r.id
            LIMIT 3
        """)
        users = cursor.fetchall()
        
        print(f"Sample users:")
        for user in users:
            print(f"  - {user[1]} ({user[2]}) - Role: {user[3]}")
        
        if not users:
            print("❌ No users found with roles")
            return False
        
        # Test queries for each role type
        for user in users:
            user_id, user_name, user_email, role_name, org_id = user
            print(f"\n🧪 Testing queries for {user_name} (Role: {role_name})")
            
            if role_name == 'ADMIN':
                # Admin sees all reports in their organization
                query = """
                    SELECT r.id, r.name as report_name, p.name as project_name,
                           r.is_analyzed, r.is_converted, r.is_migrated,
                           r.analyzed_status, r.converted_status, r.migrated_status
                    FROM biport_dev.report_details r
                    JOIN biport_dev.project_details p ON r.project_id = p.id
                    JOIN biport_dev.users u ON p.user_id = u.id
                    WHERE u.organization_id = %s
                """
                cursor.execute(query, (org_id,))

            elif role_name == 'MANAGER':
                # Manager sees reports for projects assigned to them or their subordinates
                # First get subordinates
                cursor.execute("SELECT id FROM biport_dev.users WHERE manager_id = %s", (user_id,))
                subordinates = [row[0] for row in cursor.fetchall()]

                if subordinates:
                    placeholders = ','.join(['%s'] * (len(subordinates) + 1))
                    query = f"""
                        SELECT r.id, r.name as report_name, p.name as project_name,
                               r.is_analyzed, r.is_converted, r.is_migrated,
                               r.analyzed_status, r.converted_status, r.migrated_status
                        FROM biport_dev.report_details r
                        JOIN biport_dev.project_details p ON r.project_id = p.id
                        JOIN biport_dev.users u ON p.user_id = u.id
                        WHERE p.assigned_to IN ({placeholders}) AND u.organization_id = %s
                    """
                    cursor.execute(query, subordinates + [user_id] + [org_id])
                else:
                    query = """
                        SELECT r.id, r.name as report_name, p.name as project_name,
                               r.is_analyzed, r.is_converted, r.is_migrated,
                               r.analyzed_status, r.converted_status, r.migrated_status
                        FROM biport_dev.report_details r
                        JOIN biport_dev.project_details p ON r.project_id = p.id
                        JOIN biport_dev.users u ON p.user_id = u.id
                        WHERE p.assigned_to = %s AND u.organization_id = %s
                    """
                    cursor.execute(query, (user_id, org_id))

            elif role_name == 'DEVELOPER':
                # Developer sees only reports for projects assigned to them
                query = """
                    SELECT r.id, r.name as report_name, p.name as project_name,
                           r.is_analyzed, r.is_converted, r.is_migrated,
                           r.analyzed_status, r.converted_status, r.migrated_status
                    FROM biport_dev.report_details r
                    JOIN biport_dev.project_details p ON r.project_id = p.id
                    JOIN biport_dev.users u ON p.user_id = u.id
                    WHERE p.assigned_to = %s AND u.organization_id = %s
                """
                cursor.execute(query, (user_id, org_id))
            
            reports = cursor.fetchall()
            print(f"   Found {len(reports)} reports")
            
            # Show sample reports
            for i, report in enumerate(reports[:3]):
                print(f"     {i+1}. {report[1]} (Project: {report[2]})")
                print(f"        Status: Analyzed={report[3]}, Converted={report[4]}, Migrated={report[5]}")
        
        print(f"\n✅ Role-based query testing completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False
    finally:
        cursor.close()
        conn.close()

def test_data_integrity():
    """Test data integrity for workspace queries"""
    conn = get_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        print(f"\n=== Testing Data Integrity ===\n")
        
        # Check if we have the required data
        cursor.execute("SELECT COUNT(*) FROM biport_dev.users")
        user_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM biport_dev.project_details")
        project_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM biport_dev.report_details")
        report_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM biport_dev.roles")
        role_count = cursor.fetchone()[0]
        
        print(f"Data counts:")
        print(f"  - Users: {user_count}")
        print(f"  - Projects: {project_count}")
        print(f"  - Reports: {report_count}")
        print(f"  - Roles: {role_count}")
        
        # Check for assigned projects
        cursor.execute("SELECT COUNT(*) FROM biport_dev.project_details WHERE assigned_to IS NOT NULL")
        assigned_projects = cursor.fetchone()[0]
        print(f"  - Assigned Projects: {assigned_projects}")
        
        # Check for reports with projects
        cursor.execute("""
            SELECT COUNT(*) 
            FROM biport_dev.report_details r
            JOIN biport_dev.project_details p ON r.project_id = p.id
        """)
        reports_with_projects = cursor.fetchone()[0]
        print(f"  - Reports with Projects: {reports_with_projects}")
        
        # Check role distribution
        cursor.execute("""
            SELECT r.name, COUNT(u.id) as user_count
            FROM biport_dev.roles r
            LEFT JOIN biport_dev.users u ON r.id = u.role_id
            GROUP BY r.name
        """)
        role_distribution = cursor.fetchall()
        print(f"\nRole distribution:")
        for role, count in role_distribution:
            print(f"  - {role}: {count} users")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during integrity testing: {e}")
        return False
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    try:
        if test_data_integrity():
            test_role_based_queries()
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
