from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from app.api.analysis import analysis_router
from app.api.migrate import migrate_router
from app.api.dax import dax_router
from app.api.auth import auth_router
from app.api.server_configure import server_router
from app.api.datasources import datasources_router
from app.api.discover import discover_router
from app.api.folders import folder_router
from app.api.workspace import workspace_router
from app.api.prep import prep_router
from app.api.dashboard import dashboard_router
from app.api.roles import roles_router
from app.api.organization import organization_router
from app.api.semantic_model import semantic_model_router

app = FastAPI(
    title="My API",
    description="This is my API documentation",
    version="1.0.0",
    docs_url="/swagger"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/app_api/ping")
def read_root():
    return {"message": "Hello, Fast<PERSON>I"}


app.include_router(server_router, prefix="/app_api", tags=["Server Configuration"])
app.include_router(analysis_router, prefix="/app_api", tags=["Analysis"])
app.include_router(migrate_router, prefix="/app_api", tags=["Migration"])
app.include_router(dax_router, prefix="/app_api", tags=["DAX"])
app.include_router(auth_router, prefix="/app_api/users", tags=["Authentication"])
app.include_router(datasources_router, prefix="/app_api", tags=["Data Sources"])
app.include_router(discover_router, prefix="/app_api", tags=["Discover"])
app.include_router(folder_router, prefix="/app_api", tags=["Folders"])
app.include_router(workspace_router, prefix="/app_api", tags=["Workspace"])
app.include_router(prep_router, prefix="/app_api", tags=["Tableau Prep Analysis"])
app.include_router(dashboard_router, prefix="/app_api", tags=["Dashboard"])
app.include_router(roles_router, prefix="/app_api", tags=["Roles"])
app.include_router(organization_router, prefix="/app_api", tags=["Organization Details"])
app.include_router(semantic_model_router, prefix="/app_api", tags=["Semantic Model"])

if __name__ == "__main__":
    uvicorn.run("run:app", port=9090, reload=True)
# uvicorn run:app --reload
