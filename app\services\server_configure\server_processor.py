from app.models_old.user import UserOld
from app.schemas import AddServerRequest, UpdateServerRequest, UpdateServerStatusRequest
from .server_service import ServerService
from app.core import ServiceResponse
from fastapi import BackgroundTasks
import uuid
from app.schemas import DeleteServerRequest

class ServerProcessor:
    """Handles validation and processing of server requests."""

    @staticmethod
    def process_add_server(request: AddServerRequest, user: UserOld, background_tasks: BackgroundTasks) -> ServiceResponse:
        """Processes the add server request with generic exception handling."""
        return ServerService.execute(ServerService().add_server, request, user, background_tasks)

    @staticmethod
    async def process_delete_server(server_id) -> ServiceResponse:
        """Processes the delete server request with generic exception handling."""
        return await ServerService.execute(ServerService().delete_server, server_id)

    @staticmethod
    def process_update_server(request: UpdateServerRequest, user: UserOld) -> ServiceResponse:
        """Processes the update server request with generic exception handling."""
        return ServerService.execute(ServerService().update_server, request, user)

    @staticmethod
    def process_get_server(server_id: uuid.UUID) -> ServiceResponse:
        """Processes the get server request with generic exception handling."""
        return ServerService.execute(ServerService().get_server, server_id)

    @staticmethod
    def process_get_servers(page: int, page_size: int) -> ServiceResponse:
        """Processes the get servers request with generic exception handling."""
        return ServerService.execute(ServerService().get_servers, page, page_size)

    @staticmethod
    def process_get_org_servers(organization_id: uuid.UUID, page: int, page_size: int) -> ServiceResponse:
        """Processes the get organisation servers request with generic exception handling."""
        return ServerService.execute(ServerService().get_org_servers, organization_id, page, page_size)

    @staticmethod
    def process_update_server_status(request: UpdateServerStatusRequest) -> ServiceResponse:
        """Processes the update server status request with generic exception handling."""
        return ServerService.execute(ServerService().update_server_status, request)

    @staticmethod
    def update_tableau_server_status( request: UpdateServerStatusRequest) -> ServiceResponse:
        """Process update TableauServerDetail status request."""
        return ServerService.execute(ServerService().update_tableau_server_status, request)

    @staticmethod
    def process_soft_delete_tableau_server(request: DeleteServerRequest) -> ServiceResponse:
        """Process soft delete TableauServerDetail request."""
        return ServerService.execute(ServerService().soft_delete_tableau_server, request)
