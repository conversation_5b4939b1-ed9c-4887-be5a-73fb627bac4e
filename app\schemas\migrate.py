from pydantic import BaseModel, EmailStr
from typing import List, Dict, Optional
from enum import Enum
import uuid
from app.core.constants import *

class Messages(Enum):
    SUCCESS = "Successfuly Completed"


class MigarationInput(BaseModel):
    """
    Pydantic model to create request object for tableau to powerbi migration.
    """
    # decoded_email: EmailStr
    # payload: Dict
    # organization_name: str
    # user_email: str
    s3_input_path: str
    local_download_path: str
    logger_id: str = str(uuid.uuid4()).replace("-","")[:20]
    report_type:str = MIGRATE_REPORT_TYPE
    twb_files_count: int
    twb_files: List
    process_id: str
    powerbi_structure: str

class WorkbookId(BaseModel):
    workbook_id: str
    workbook_name: str

class S3path(BaseModel):
    s3_path: str
    workbook_name: str

class WorkbooksRequest(BaseModel):
    workbook_ids: Optional[List[WorkbookId]] = None
    s3_paths: Optional[List[S3path]] = None
