import os
import time
import uuid
import zipfile
import shutil
from pathlib import Path
from typing import Optional

import aiofiles
from fastapi import APIRouter, Form, UploadFile, File, HTTPException, status, Depends
from fastapi.responses import J<PERSON>NResponse

from app.models_old.user import UserOld
from app.core.config import logger, S3Config
from app.core.dependencies import get_current_user
from app.core.exceptions import BadRequestError
from app.services.semantic_model.model_generate import semantic_model_generation, semantic_model_generation_v2
from app.services.semantic_model.model_generate_csv import semantic_model_generation_csv
from app.services.semantic_model.excel.model_generate_excel import semantic_model_generation_excel
from app.services.migrate.Tableau_Analyzer.semantic_model import store_and_prepare_semantic_model_env

from app.services.migrate import create_semantic_model, upload_pbi_to_s3
semantic_model_router = APIRouter()
s3_config = S3Config()

@semantic_model_router.post("/semantic_model")
async def generate_semantic_model(
    process_id: str = Form(...),
    user: UserOld = Depends(get_current_user)
):
    start_time = time.time()
    logger.info(f" - Semantic model generation started.")

    try:
        organization_name = user.organization_name
        user_email = user.email

        if not process_id:
            raise HTTPException(status_code=400, detail="Missing required form field: process_id")

        base_storage_dir = './storage'
        local_dir = os.path.join(base_storage_dir, uuid.uuid4().hex[:5])
        local_input_path = os.path.join(local_dir, "twb_files")
        Path(local_input_path).mkdir(parents=True, exist_ok=True)
        logger.info(f" - Created local download path: {local_input_path}")

        s3_input_path = f"{organization_name}/{user_email}/{process_id}/input_files"
        try:
            twb_files_path = await s3_config.download_semantic_model_input_files(s3_input_path, local_input_path)
        except Exception as e:
            logger.exception(f"Failed to download semantic model input files from S3: {s3_input_path}")
            raise BadRequestError(detail=f"S3 download failed: {str(e)}")

        if not twb_files_path:
            raise HTTPException(status_code=404, detail="No TWB files found in S3 input path.")

        download_links = await semantic_model_generation(
            organization_name, user_email, twb_files_path, local_dir, process_id
        )

        end_time = time.time()
        logger.info(f" - Semantic model generation completed in {end_time - start_time:.2f} seconds.")

        return JSONResponse(content={
            'message': "Semantic model generation completed successfully.",
            'download_links': download_links,
        }, status_code=200)

    except HTTPException as http_err:
        logger.error(f" - HTTPException: {http_err.detail}")
        raise http_err
    except Exception as err:
        logger.error(f" - Exception: {str(err)}")
        raise HTTPException(status_code=500, detail=str(err))

@semantic_model_router.post("/semantic_model/v2")
async def generate_semantic_model_v2(
    process_id: str = Form(...),
    file_type: Optional[str] = Form(None),
    zip_files: Optional[UploadFile] = File(None),
    user: UserOld = Depends(get_current_user)
):
    start_time = time.time()
    logger.info("Semantic model v2 generation started.")

    try:
        organization_name = user.organization_name
        user_email = user.email

        if not process_id:
            raise HTTPException(status_code=400, detail="Missing required form field: process_id")

        base_storage_dir = './storage'
        local_dir = os.path.join(base_storage_dir, uuid.uuid4().hex[:5])
        local_input_path = os.path.join(local_dir, "twb_files")
        Path(local_input_path).mkdir(parents=True, exist_ok=True)
        logger.info(f"Created local download path: {local_input_path}")

        # Handle uploaded zip file if present
        extracted_path = await handle_uploaded_zip(zip_files, local_input_path)

        # Download all semantic model input files from S3 folder
        s3_input_path = f"{organization_name}/{user_email}/{process_id}/input_files"
        try:
            twb_files_path = await s3_config.download_semantic_model_input_files(s3_input_path, local_input_path)
        except Exception as e:
            logger.exception(f"Failed to download semantic model input files from S3: {s3_input_path}")
            raise BadRequestError(detail=f"S3 download failed: {str(e)}")

        if not file_type and twb_files_path:
            ext = os.path.splitext(twb_files_path[0])[1].lower()
            file_type = 'csv' if ext == '.csv' else 'excel' if ext in ['.xls', '.xlsx'] else 'twb'

        if file_type == 'csv':
            download_links = await semantic_model_generation_csv(
                organization_name, user_email, twb_files_path,
                local_dir, process_id, extracted_path
            )
        elif file_type == 'excel':
            download_links = await semantic_model_generation_excel(
                organization_name, user_email, twb_files_path,
                local_dir, process_id, extracted_path
            )
        else:
            download_links = await semantic_model_generation_v2(
                organization_name, user_email, twb_files_path,
                local_dir, process_id, extracted_path
            )

        end_time = time.time()
        logger.info(f"Semantic model v2 generation completed. Time taken: {end_time - start_time:.2f} seconds.")
        return JSONResponse(content={
            'message': "Semantic model generation completed successfully.",
            'download_links': download_links,
        }, status_code=200)

    except HTTPException as http_err:
        logger.error(f"HTTPException: {http_err.detail}")
        raise http_err
    except Exception as err:
        logger.error(f"Exception: {str(err)}")
        raise HTTPException(status_code=500, detail=str(err))

async def handle_uploaded_zip(zip_file: Optional[UploadFile], extract_path: str):
    if not zip_file:
        logger.info("No zip file uploaded, skipping extraction.")
        return extract_path
    try:
        os.makedirs(extract_path, exist_ok=True)
        zip_file_path = os.path.join(extract_path, zip_file.filename)
        async with aiofiles.open(zip_file_path, "wb") as f:
            content = await zip_file.read()
            await f.write(content)
        with zipfile.ZipFile(zip_file_path, 'r') as zip_ref:
            zip_ref.extractall(extract_path)
        os.remove(zip_file_path)
        logger.info(f"Zip file extracted successfully to {extract_path}")
        return extract_path
    except Exception as e:
        logger.error(f"Error extracting zip file: {str(e)}")
        raise Exception("Failed to extract uploaded zip file.")
    
#This endpoint is a test API for semantic model generation.
@semantic_model_router.post("/semantic_model_test")
async def semantic_model_test(
    twb_file: UploadFile = File(...),
    pbi_env_file: UploadFile = File(...)
):
    if not twb_file or not pbi_env_file:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Both twb_file and pbi_env_file are required."
        )
    twb_file_path, powerbi_folder_path, unique_id = store_and_prepare_semantic_model_env(twb_file, pbi_env_file)
    await create_semantic_model(twb_file_path, powerbi_folder_path)
    download_urls = await upload_pbi_to_s3(powerbi_folder_path, unique_id)

    return JSONResponse(
        content={
            "data": download_urls
        },
        status_code = status.HTTP_200_OK
    )