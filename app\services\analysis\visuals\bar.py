from app.core import logger
from app.core.enums import (
    ChartType, GeneralKeys as GS, WorkSheet as WS,
)
from .common import get_quantitative_columns, get_rows_cols_details
from app.core.regex_enums import Regex as RE
from .bubble_chart import Bub<PERSON><PERSON><PERSON>
from .bullet import <PERSON><PERSON><PERSON><PERSON>
from .donut import Donut<PERSON><PERSON>
from .waterfall import Waterfall<PERSON>hart
from .polygon import Polygon

class Bar:
    @staticmethod
    def check_bar_chart(worksheet):
        try:
            quantitative_cols = get_quantitative_columns(worksheet)
            if not quantitative_cols:
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            (
                quantitative_in_rows,
                quantitative_in_cols,
                rows, cols, columns,
                rows_details, cols_details
            ) = get_rows_cols_details(worksheet)

            def determine_chart_type(rows_details, cols_details):
                date_types = GS.DATE_TIME_SET.value
                has_date_column = any(col.get(WS.DATATYPE.value) in date_types for col in columns)
                total_quantitative = quantitative_in_rows + quantitative_in_cols

                if not total_quantitative:
                    return None
                if quantitative_in_rows and quantitative_in_cols:
                    return None
                if not has_date_column:
                    if quantitative_in_rows:
                        return ChartType.VERTICAL_BAR.value
                    if quantitative_in_cols:
                        return ChartType.HORIZONTAL_BAR.value
                date_in_rows = [col for col in rows_details if col[WS.DATATYPE.value] in date_types]
                date_in_cols = [col for col in cols_details if col[WS.DATATYPE.value] in date_types]
                if date_in_rows:
                    if rows_details.index(date_in_rows[0]) == len(rows_details) - 1:
                        return None
                if date_in_cols:
                    if cols_details.index(date_in_cols[0]) == len(cols_details) - 1:
                        return None
                if quantitative_in_rows:
                    return ChartType.VERTICAL_BAR.value
                if quantitative_in_cols:
                    return ChartType.HORIZONTAL_BAR.value
                return None

            measure_values_in_cols = any("Multiple Values" in str(col) for col in cols)
            measure_values_in_rows = any("Multiple Values" in str(row) for row in rows)

            if measure_values_in_rows:
                chart_type = ChartType.VERTICAL_BAR.value
            elif measure_values_in_cols:
                chart_type = ChartType.HORIZONTAL_BAR.value
            else:
                chart_type = determine_chart_type(rows_details, cols_details)

            if not chart_type:
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            response = Bar.check_stacked_bar_chart(
                worksheet, rows, cols, chart_type, quantitative_cols
            )
            if response[GS.STATUS.value]:
                return response

            logger.info(f"Chart type: {chart_type}")
            return {GS.STATUS.value: True, GS.CHART_TYPE.value: chart_type}

        except ValueError as e:
            logger.error(f"Error analyzing worksheet for bar chart: {e}")
            return {GS.STATUS.value: False, GS.CHART_TYPE.value: None, GS.ERROR.value: str(e)}
        except Exception as e:
            logger.error(f"Unexpected error in check_bar_chart: {e}")
            return {GS.STATUS.value: False, GS.CHART_TYPE.value: None, GS.ERROR.value: "Unexpected error"}

    @staticmethod
    def get_bar_type(worksheet):
        (
            quantitative_in_rows, quantitative_in_cols,
            rows, cols, columns, rows_details, cols_details
        ) = get_rows_cols_details(worksheet)

        quantitative_cols = get_quantitative_columns(worksheet)
        measure_values_in_cols = any("Multiple Values" in str(col) for col in cols)
        measure_values_in_rows = any("Multiple Values" in str(row) for row in rows)

        if quantitative_in_rows:
            chart_type = ChartType.VERTICAL_BAR.value
        elif quantitative_in_cols:
            chart_type = ChartType.HORIZONTAL_BAR.value
        elif measure_values_in_rows:
            chart_type = ChartType.VERTICAL_BAR.value
        elif measure_values_in_cols:
            chart_type = ChartType.HORIZONTAL_BAR.value
        else:
            chart_type = None

        if not chart_type:
            return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

        response = Bar.check_stacked_bar_chart(
            worksheet, rows, cols, chart_type, quantitative_cols
        )
        if response[GS.STATUS.value]:
            return response

        return {GS.STATUS.value: True, GS.CHART_TYPE.value: chart_type}

    @staticmethod
    def check_stacked_bar_chart(worksheet, rows, cols, chart_type, quantitative_cols):
        try:
            panes = worksheet.findall(WS.PANE.value)
            pane = panes[0] if panes else None
            if not pane:
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            color_tag = pane.find(WS.ENCODING_COLOR.value)
            if color_tag is None:
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            color_column_full = color_tag.get(WS.COLUMN.value)
            if not color_column_full:
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            color_column = color_column_full.split(RE.DOT.value)[-1]

            if color_column in rows or color_column in cols:
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            quantitative_col_names = {
                col[GS.COLUMN_INSTANCE.value][WS.NAME.value]
                for col in quantitative_cols
                if GS.COLUMN_INSTANCE.value in col and WS.NAME.value in col[GS.COLUMN_INSTANCE.value]
            }

            if color_column in quantitative_col_names:
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            chart_type_mapping = {
                ChartType.VERTICAL_BAR.value: ChartType.STACKED_VERTICAL_BAR.value,
                ChartType.HORIZONTAL_BAR.value: ChartType.STACKED_HORIZONTAL_BAR.value
            }

            chart_name = chart_type_mapping.get(chart_type)
            if chart_name:
                return {GS.STATUS.value: True, GS.CHART_TYPE.value: chart_name}

            return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}
        except Exception as e:
            logger.error(f"Error in check_stacked_bar_chart: {e}")
            return {GS.STATUS.value: False, GS.CHART_TYPE.value: None, GS.ERROR.value: str(e)}

class Direct:
    @staticmethod
    def get_direct_chart_type(worksheet):
        panes = worksheet.findall(WS.PANES_PANE.value)
        mark_class = (
            panes[0].find(WS.MARK.value).get(WS.CLASS.value, None)
            if panes else None
        )

        if not mark_class:
            raise ValueError("Mark class not found in pane for direct chart type.")
        
        donut_chart_response = DonutChart.check_donut_chart(worksheet)
        if donut_chart_response[GS.STATUS.value]:
            return donut_chart_response[GS.CHART_TYPE.value]

        if mark_class.lower() == ChartType.CIRCLE.value.lower():
            bubble_chart_response = BubbleChart.check_bubble_chart(worksheet)
            if bubble_chart_response[GS.STATUS.value]:
                return bubble_chart_response[GS.CHART_TYPE.value] 
       
        if mark_class.lower() == ChartType.GANTT_BAR.value.lower():
            waterfall_chart_response = WaterfallChart.check_waterfall_chart(worksheet)
            if waterfall_chart_response[GS.STATUS.value]:
                return waterfall_chart_response[GS.CHART_TYPE.value]

        if mark_class.lower() == ChartType.POLYGON.value.lower():
            polygon_chart_response = Polygon.check_polygon(worksheet)
            if polygon_chart_response[GS.STATUS.value]:
                return polygon_chart_response[GS.CHART_TYPE.value]    

        if mark_class == ChartType.AUTOMATIC.value and len(panes) > 1:
            return ChartType.MIXED_CHART.value

        elif len(panes) > 1:
            for pane in panes:
                mark_class1 = pane.find(WS.MARK.value).get(WS.CLASS.value, None)
                if mark_class1 != mark_class:
                    return ChartType.MIXED_CHART.value
            return mark_class

        elif mark_class == ChartType.SQUARE.value:
            return ChartType.HIGHLIGHTED_TABLE.value

        elif mark_class == ChartType.BAR.value:
            bullet_chart_response = BulletChart.check_bullet_chart(worksheet)
            if bullet_chart_response[GS.STATUS.value]:
                return bullet_chart_response[GS.CHART_TYPE.value]

            bar_response = Bar.get_bar_type(worksheet)
            return (
                bar_response[GS.CHART_TYPE.value]
                if bar_response[GS.STATUS.value]
                else ChartType.BAR.value
            )

        elif mark_class == ChartType.MULTIPOLYGON.value:
            return ChartType.FILLED_MAP.value

        return mark_class
