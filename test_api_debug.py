#!/usr/bin/env python3
"""
Debug the workspace API endpoint
"""
import requests
import json

# API base URL
BASE_URL = "http://127.0.0.1:9090/app_api"

def test_workspace_with_debug():
    """Test workspace endpoint with detailed debugging"""
    print("🔍 Debugging Workspace API Endpoint")
    print("=" * 50)
    
    # First login to get token
    login_url = f"{BASE_URL}/users/login"
    login_data = {
        "email": "<EMAIL>",
        "password": "Raghava@123"
    }
    
    print("Step 1: Login")
    response = requests.post(login_url, json=login_data)
    if response.status_code != 200:
        print(f"❌ Login failed: {response.text}")
        return
    
    login_response = response.json()
    access_token = login_response['data']['access_token']
    user_email = login_response['data']['user_email']
    
    print(f"✅ Login successful")
    print(f"Role: {login_response['data']['role']}")
    print(f"User: {login_response['data']['user_name']}")
    
    # Test workspace endpoint
    print(f"\nStep 2: Test Workspace Endpoint")
    workspace_url = f"{BASE_URL}/workspace/reports"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "X-User-Email": user_email,
        "Content-Type": "application/json"
    }
    
    print(f"URL: {workspace_url}")
    print(f"Headers: {headers}")
    
    try:
        response = requests.get(workspace_url, headers=headers)
        print(f"\nResponse Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success!")
            print(f"Response: {json.dumps(data, indent=2)}")
        else:
            print(f"❌ Error Response:")
            print(f"Status: {response.status_code}")
            print(f"Text: {response.text}")
            
            # Try to get more details
            try:
                error_data = response.json()
                print(f"JSON Error: {json.dumps(error_data, indent=2)}")
            except:
                print("Could not parse error as JSON")
                
    except Exception as e:
        print(f"❌ Exception during request: {e}")

def test_simple_endpoints():
    """Test other endpoints to see if they work"""
    print(f"\n🧪 Testing Other Endpoints")
    print("=" * 50)
    
    # Test ping
    try:
        response = requests.get(f"{BASE_URL}/ping")
        print(f"Ping endpoint: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"Ping failed: {e}")

if __name__ == "__main__":
    test_simple_endpoints()
    test_workspace_with_debug()
