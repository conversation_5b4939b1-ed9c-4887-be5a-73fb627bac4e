import xml.etree.ElementTree as ET
import json
import os
from app.core import logger
from .worksheet import extract_worksheet_details
from .dashboard import extract_dashboard_details
from .datasource import extract_datasource_details
from .chart_types import analyse_Chart_types
from .calculations import calculate_fields
from app.core.enums import (
    GeneralKeys as GS
)


def process_twb_files(input_dir, output_dir):
    processing_contents = ["Dashboards","Worksheets","Datasources","Calculations"]
    content_processing_functions = [extract_dashboard_details,extract_worksheet_details,extract_datasource_details,calculate_fields]
    for process_content,func in zip(processing_contents,content_processing_functions):
        process_contents(func,process_content, input_dir, output_dir)
    analyse_Chart_types(input_dir, output_dir)


def process_contents(func, processing_content, input_dir, output_dir, file_extension=GS.TXT_FILE.value):
    logger.info(f"Starting {processing_content} processing.")
    if not os.path.exists(input_dir):
        logger.error(f"Input directory '{input_dir}' does not exist.")
        return {"error": f"Directory '{input_dir}' does not exist."}

    if not os.path.exists(output_dir):
        logger.error(f"Output directory '{output_dir}' does not exist.")
        return {"error": f"Directory '{output_dir}' does not exist."}

    for filename in os.listdir(input_dir):
        if filename.endswith(file_extension):
            folder_name = os.path.splitext(filename)[0]
            folder_path = os.path.join(output_dir, folder_name)
            os.makedirs(folder_path, exist_ok=True)

            output_file_path = os.path.join(folder_path, f"{processing_content.lower()}.json")
            xml_file_path = os.path.join(input_dir, filename)

            extract_semantic_model(func, processing_content, xml_file_path, output_file_path)

    logger.info(f"{processing_content} processing completed.")


def extract_semantic_model(func, process_content, xml_file_path, output_file_path):
    try:
        logger.info(f"Processing file: {xml_file_path}")
        
        tree = ET.parse(xml_file_path)
        root = tree.getroot()
        data = func(root)
        with open(output_file_path, 'w') as f:
            json.dump(data, f, indent=4)
        if not data : logger.warning(f"The file {xml_file_path} is not containing <{process_content.lower()}> tag")
        logger.info(f"Successfully processed {xml_file_path} and saved output to {output_file_path}")
    except ET.ParseError as e:
        logger.error(f"Error parsing XML file {xml_file_path}: {e}")
    except FileNotFoundError as e:
        logger.error(f"File not found: {e}")
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}")
