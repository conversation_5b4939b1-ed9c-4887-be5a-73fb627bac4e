"""changing the workbook_id type

Revision ID: 4ac6f8d5e3fe
Revises: 1d8c3c7120a4
Create Date: 2025-06-11 12:30:45.206258

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4ac6f8d5e3fe'
down_revision: Union[str, None] = '1d8c3c7120a4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
