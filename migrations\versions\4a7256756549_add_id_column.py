from alembic import op
import sqlalchemy as sa
import uuid

# Revision identifiers, used by Alembic.
revision = '************'
down_revision = '13db7402191b'
branch_labels = None
depends_on = None

def upgrade():
    # Drop the "data_sources" table if it exists
    op.drop_table('data_sources')

    # Create the new "data_sources" table
    op.create_table(
        'data_sources',
        sa.Column('id', sa.UUID(as_uuid=True), primary_key=True, nullable=False, default=uuid.uuid4),
        sa.<PERSON>umn('user_id', sa.Integer(), sa.<PERSON>ey('users.id')),
        sa.Column('mssql', sa.String(), nullable=True),
        sa.Column('postgresql', sa.String(), nullable=True),
        sa.Column('oracle', sa.String(), nullable=True),
        sa.Column('mysql', sa.String(), nullable=True),
        sa.Column('sqlite', sa.String(), nullable=True)
    )

def downgrade():
    # Revert the changes: Drop the new "data_sources" table
    op.drop_table('data_sources')