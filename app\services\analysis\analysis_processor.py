from typing import List, Dict
from .analysis_services import AnalysisService
from app.schemas import AnalyseInput
from app.core.exceptions import ServerError

class AnalyseProcessor:

    @staticmethod
    async def analyse_processor(twb_files: List[dict], is_upload_file: bool, user) -> dict:
        if not twb_files:
            raise ServerError(detail="Input 'twb_files' is required and cannot be empty.")
        processed_files = []
        for wb in twb_files:
            processed_files.append({
            'workbook_id': getattr(wb, 'workbook_id', None),
            'workbook_name': getattr(wb, 'workbook_name', None),
            's3_path': getattr(wb, 's3_path', None) if is_upload_file else getattr(wb, 'workbook_id', None)
        })
        
        analyse_input = AnalyseInput(twb_files=processed_files, is_upload_file=is_upload_file, user=user)
        return await AnalysisService.execute(
            AnalysisService().analysing_report,
            analyse_input
        
        )