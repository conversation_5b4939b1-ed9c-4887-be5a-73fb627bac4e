#!/usr/bin/env python3
"""
Script to import project_details and report_details data with proper foreign key handling
"""
import psycopg2
import csv
import sys
from uuid import uuid4

# Database connection parameters
DB_CONFIG = {
    'host': 'localhost',
    'port': '5432',
    'user': 'postgres',
    'password': 'postgres',
    'database': 'test'
}

def get_connection():
    """Get database connection"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except psycopg2.Error as e:
        print(f"Error connecting to database: {e}")
        return None

def safe_value(value, data_type='str'):
    """Convert CSV value to appropriate type, handling NULL strings"""
    if value is None or value == '' or value.upper() == 'NULL':
        return None
    
    if data_type == 'int':
        try:
            return int(value)
        except (ValueError, TypeError):
            return None
    elif data_type == 'bool':
        return value.lower() == 'true'
    else:
        return value

def create_missing_references():
    """Create missing users and sites that are referenced in the data"""
    conn = get_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        # Get existing users, sites, and servers
        cursor.execute("SELECT id FROM biport_dev.users")
        existing_users = {row[0] for row in cursor.fetchall()}
        
        cursor.execute("SELECT id FROM biport_dev.tableau_site_details")
        existing_sites = {row[0] for row in cursor.fetchall()}
        
        cursor.execute("SELECT id FROM biport_dev.tableau_server_details")
        existing_servers = {row[0] for row in cursor.fetchall()}
        
        cursor.execute("SELECT id FROM biport_dev.organization_details LIMIT 1")
        default_org = cursor.fetchone()[0]
        
        cursor.execute("SELECT id FROM biport_dev.roles WHERE name = 'DEVELOPER' LIMIT 1")
        default_role = cursor.fetchone()[0]
        
        # Collect missing user IDs from project_details.csv
        missing_users = set()
        with open('data/project_details.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                user_id = safe_value(row['user_id'])
                assigned_to = safe_value(row['assigned_to'])
                
                if user_id and user_id not in existing_users:
                    missing_users.add(user_id)
                if assigned_to and assigned_to not in existing_users:
                    missing_users.add(assigned_to)
        
        # Create missing users
        print(f"Creating {len(missing_users)} missing users...")
        for i, user_id in enumerate(missing_users):
            cursor.execute("""
                INSERT INTO biport_dev.users 
                (id, name, email, password_hash, phone_number, organization_id, role_id, 
                 created_at, updated_at, is_deleted)
                VALUES (%s, %s, %s, %s, %s, %s, %s, NOW(), NOW(), FALSE)
            """, (
                user_id, f"Generated User {i+1}", f"user{i+1}@example.com", 
                "dummy_hash", f"555000{i+1:04d}", default_org, default_role
            ))
        
        # Check for missing site (the one referenced in project_details)
        missing_site_id = "c4b2b37c-e59d-49b3-a41d-2fc2cf99cc2f"
        if missing_site_id not in existing_sites:
            print("Creating missing site...")
            # First create a dummy credential for this site
            dummy_cred_id = str(uuid4())
            
            # Get an existing server to link the credential to
            cursor.execute("SELECT id FROM biport_dev.tableau_server_details LIMIT 1")
            existing_server = cursor.fetchone()
            if existing_server:
                cursor.execute("""
                    INSERT INTO biport_dev.tableau_server_credentials 
                    (id, server_id, pat_name, pat_secret, server_auth_type, created_at, updated_at, is_deleted)
                    VALUES (%s, %s, %s, %s, %s, NOW(), NOW(), FALSE)
                """, (
                    dummy_cred_id, existing_server[0], "Dummy PAT", "dummy_secret", "PAT"
                ))
                
                # Now create the site
                cursor.execute("""
                    INSERT INTO biport_dev.tableau_site_details 
                    (id, credentials_id, site_name, site_id, created_at, updated_at, is_deleted)
                    VALUES (%s, %s, %s, %s, NOW(), NOW(), FALSE)
                """, (
                    missing_site_id, dummy_cred_id, "Generated Site", "generated-site-id"
                ))
        
        conn.commit()
        print("Missing references created successfully.")
        return True
        
    except Exception as e:
        print(f"Error creating missing references: {e}")
        conn.rollback()
        return False
    finally:
        cursor.close()
        conn.close()

def import_project_details():
    """Import project details data"""
    conn = get_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        print("Importing project_details...")
        imported_count = 0
        skipped_count = 0
        
        with open('data/project_details.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                try:
                    cursor.execute("""
                        INSERT INTO biport_dev.project_details 
                        (id, name, is_upload, site_id, server_id, parent_id, user_id, assigned_to,
                         created_at, updated_at, is_deleted)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        ON CONFLICT (id) DO NOTHING
                    """, (
                        safe_value(row['id']), safe_value(row['name']), 
                        safe_value(row['is_upload'], 'bool'),
                        safe_value(row['site_id']), safe_value(row['server_id']),
                        safe_value(row['parent_id']), safe_value(row['user_id']), 
                        safe_value(row['assigned_to']), safe_value(row['created_at']), 
                        safe_value(row['updated_at']), safe_value(row['is_deleted'], 'bool')
                    ))
                    imported_count += 1
                except psycopg2.IntegrityError as e:
                    print(f"Skipping project {row['id']}: {e}")
                    conn.rollback()
                    skipped_count += 1
                    continue
        
        conn.commit()
        print(f"Imported {imported_count} projects, skipped {skipped_count}")
        return True
        
    except Exception as e:
        print(f"Error importing project details: {e}")
        conn.rollback()
        return False
    finally:
        cursor.close()
        conn.close()

def import_report_details():
    """Import report details data"""
    conn = get_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        # Get existing project IDs
        cursor.execute("SELECT id FROM biport_dev.project_details")
        existing_projects = {row[0] for row in cursor.fetchall()}
        
        print("Importing report_details...")
        imported_count = 0
        skipped_count = 0
        
        with open('data/report_details.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                project_id = safe_value(row['project_id'])
                
                # Only import if project exists
                if project_id in existing_projects:
                    try:
                        cursor.execute("""
                            INSERT INTO biport_dev.report_details 
                            (id, name, report_id, project_id, is_analyzed, analyzed_status, is_converted, 
                             converted_status, is_migrated, migrated_status, unit_tested, uat_tested, 
                             deployed, is_scoped, semantic_type, has_semantic_model, view_count)
                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                            ON CONFLICT (id) DO NOTHING
                        """, (
                            safe_value(row['id']), safe_value(row['name']), safe_value(row['report_id']), 
                            project_id, safe_value(row['is_analyzed'], 'bool'),
                            safe_value(row['analyzed_status']), safe_value(row['is_converted'], 'bool'),
                            safe_value(row['converted_status']), safe_value(row['is_migrated'], 'bool'),
                            safe_value(row['migrated_status']), safe_value(row['unit_tested'], 'bool'),
                            safe_value(row['uat_tested'], 'bool'), safe_value(row['deployed'], 'bool'),
                            safe_value(row['is_scoped'], 'bool'), safe_value(row['semantic_type']), 
                            safe_value(row['has_semantic_model'], 'bool'),
                            safe_value(row['view_count'], 'int') or 0
                        ))
                        imported_count += 1
                    except psycopg2.IntegrityError as e:
                        print(f"Skipping report {row['id']}: {e}")
                        conn.rollback()
                        skipped_count += 1
                        continue
                else:
                    skipped_count += 1
        
        conn.commit()
        print(f"Imported {imported_count} reports, skipped {skipped_count}")
        return True
        
    except Exception as e:
        print(f"Error importing report details: {e}")
        conn.rollback()
        return False
    finally:
        cursor.close()
        conn.close()

def main():
    """Main function"""
    print("Starting project and report data import...")
    
    # Step 1: Create missing references
    if not create_missing_references():
        print("Failed to create missing references. Exiting.")
        sys.exit(1)
    
    # Step 2: Import project details
    if not import_project_details():
        print("Failed to import project details. Exiting.")
        sys.exit(1)
    
    # Step 3: Import report details
    if not import_report_details():
        print("Failed to import report details. Exiting.")
        sys.exit(1)
    
    print("Project and report data import completed successfully!")

if __name__ == "__main__":
    main()
