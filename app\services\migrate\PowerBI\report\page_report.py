import json
from .get_singlevisual_data  import get_config_json, get_uuid
from ...Tableau_Analyzer.report.worksheet import process_worksheets
from app.core.enums import PowerBIReportKeys, PowerBITemplateKeys

def generate_report_sections(root, chart_types):
    tableau_analyzed_data = process_worksheets(root, chart_types)
    sections_list = []
    for ordinal_num, visual_details in enumerate(tableau_analyzed_data, start=1):
        config_json = {}
        visual_containsers = []
        config_json[PowerBIReportKeys.CONFIG.value] = {}
        config_json[PowerBIReportKeys.DISPLAY_NAME.value] = visual_details.get(PowerBITemplateKeys.WORKSHEET_NAME.value)
        config_json[PowerBIReportKeys.DISLAY_OPTION.value] = 1
        config_json[PowerBIReportKeys.NAME.value] = get_uuid()
        config_json[PowerBIReportKeys.ORDINAL.value] = ordinal_num
        visual_containsers.append({PowerBIReportKeys.CONFIG.value:json.dumps(get_config_json(visual_details))})
        config_json[PowerBIReportKeys.VISUAL_CONTAINERS.value] = visual_containsers
        sections_list.append(config_json)
    return sections_list
