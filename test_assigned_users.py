#!/usr/bin/env python3
"""
Test workspace queries with users who have assigned projects
"""
import psycopg2

# Database connection parameters
DB_CONFIG = {
    'host': 'localhost',
    'port': '5432',
    'user': 'postgres',
    'password': 'postgres',
    'database': 'test'
}

def get_connection():
    """Get database connection"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except psycopg2.Error as e:
        print(f"Error connecting to database: {e}")
        return None

def test_assigned_users():
    """Test with users who have assigned projects"""
    conn = get_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        print("=== Testing Users with Assigned Projects ===\n")
        
        # Get users who have projects assigned to them
        cursor.execute("""
            SELECT DISTINCT u.id, u.name, u.email, r.name as role_name, u.organization_id,
                   COUNT(p.id) as project_count
            FROM biport_dev.users u
            JOIN biport_dev.roles r ON u.role_id = r.id
            JOIN biport_dev.project_details p ON p.assigned_to = u.id
            GROUP BY u.id, u.name, u.email, r.name, u.organization_id
            ORDER BY project_count DESC
            LIMIT 5
        """)
        assigned_users = cursor.fetchall()
        
        print(f"Users with assigned projects:")
        for user in assigned_users:
            print(f"  - {user[1]} ({user[2]}) - Role: {user[3]} - Projects: {user[5]}")
        
        if not assigned_users:
            print("❌ No users found with assigned projects")
            return False
        
        # Test workspace queries for these users
        for user in assigned_users:
            user_id, user_name, user_email, role_name, org_id, project_count = user
            print(f"\n🧪 Testing workspace query for {user_name} (Role: {role_name}, Projects: {project_count})")
            
            if role_name == 'ADMIN':
                # Admin sees all reports in their organization
                query = """
                    SELECT r.id, r.name as report_name, p.name as project_name,
                           r.is_analyzed, r.is_converted, r.is_migrated
                    FROM biport_dev.report_details r
                    JOIN biport_dev.project_details p ON r.project_id = p.id
                    JOIN biport_dev.users u ON p.user_id = u.id
                    WHERE u.organization_id = %s
                """
                cursor.execute(query, (org_id,))
                
            elif role_name == 'MANAGER':
                # Manager sees reports for projects assigned to them or their subordinates
                cursor.execute("SELECT id FROM biport_dev.users WHERE manager_id = %s", (user_id,))
                subordinates = [row[0] for row in cursor.fetchall()]
                
                if subordinates:
                    placeholders = ','.join(['%s'] * (len(subordinates) + 1))
                    query = f"""
                        SELECT r.id, r.name as report_name, p.name as project_name,
                               r.is_analyzed, r.is_converted, r.is_migrated
                        FROM biport_dev.report_details r
                        JOIN biport_dev.project_details p ON r.project_id = p.id
                        JOIN biport_dev.users u ON p.user_id = u.id
                        WHERE p.assigned_to IN ({placeholders}) AND u.organization_id = %s
                    """
                    cursor.execute(query, subordinates + [user_id] + [org_id])
                else:
                    query = """
                        SELECT r.id, r.name as report_name, p.name as project_name,
                               r.is_analyzed, r.is_converted, r.is_migrated
                        FROM biport_dev.report_details r
                        JOIN biport_dev.project_details p ON r.project_id = p.id
                        JOIN biport_dev.users u ON p.user_id = u.id
                        WHERE p.assigned_to = %s AND u.organization_id = %s
                    """
                    cursor.execute(query, (user_id, org_id))
                
            elif role_name == 'DEVELOPER':
                # Developer sees only reports for projects assigned to them
                query = """
                    SELECT r.id, r.name as report_name, p.name as project_name,
                           r.is_analyzed, r.is_converted, r.is_migrated
                    FROM biport_dev.report_details r
                    JOIN biport_dev.project_details p ON r.project_id = p.id
                    JOIN biport_dev.users u ON p.user_id = u.id
                    WHERE p.assigned_to = %s AND u.organization_id = %s
                """
                cursor.execute(query, (user_id, org_id))
            
            reports = cursor.fetchall()
            print(f"   ✅ Found {len(reports)} reports accessible to this user")
            
            # Show sample reports
            for i, report in enumerate(reports[:3]):
                print(f"     {i+1}. {report[1]} (Project: {report[2]})")
                print(f"        Status: Analyzed={report[3]}, Converted={report[4]}, Migrated={report[5]}")
        
        print(f"\n✅ Testing completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        cursor.close()
        conn.close()

def test_manager_subordinates():
    """Test manager-subordinate relationships"""
    conn = get_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        print(f"\n=== Testing Manager-Subordinate Relationships ===\n")
        
        # Get managers and their subordinates
        cursor.execute("""
            SELECT m.id, m.name as manager_name, m.email as manager_email,
                   COUNT(s.id) as subordinate_count
            FROM biport_dev.users m
            JOIN biport_dev.roles r ON m.role_id = r.id
            LEFT JOIN biport_dev.users s ON s.manager_id = m.id
            WHERE r.name = 'MANAGER'
            GROUP BY m.id, m.name, m.email
        """)
        managers = cursor.fetchall()
        
        for manager in managers:
            manager_id, manager_name, manager_email, subordinate_count = manager
            print(f"Manager: {manager_name} ({manager_email}) - Subordinates: {subordinate_count}")
            
            # Get subordinate details
            cursor.execute("""
                SELECT s.id, s.name, s.email, r.name as role_name
                FROM biport_dev.users s
                JOIN biport_dev.roles r ON s.role_id = r.id
                WHERE s.manager_id = %s
            """, (manager_id,))
            subordinates = cursor.fetchall()
            
            for sub in subordinates:
                print(f"  - {sub[1]} ({sub[2]}) - Role: {sub[3]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during manager testing: {e}")
        return False
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    try:
        test_manager_subordinates()
        test_assigned_users()
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
