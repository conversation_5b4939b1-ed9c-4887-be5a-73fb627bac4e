from datetime import datetime
from sqlalchemy import Column, DateTime, String, Integer, UUID
from uuid import uuid4
from app.core.session import Base, scoped_context


class OrganizationDetails(Base):
    __tablename__ = "organization_details"
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    organization_name = Column(String(50), nullable=False)
    allowed_reports = Column(Integer)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class OrganizationDetailsManager:

    @staticmethod
    def get_or_create(organization_name, allowed_reports):
        """Fetch or create an organization record."""
        
        with scoped_context() as session:
            org_details = session.query(OrganizationDetails).filter_by(organization_name=organization_name).first()
            if not org_details:
                org_details = OrganizationDetails(
                    organization_name=organization_name,
                    allowed_reports=allowed_reports
                )
                session.add(org_details)
                session.commit()
                session.refresh(org_details)
            return org_details