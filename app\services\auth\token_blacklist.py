from datetime import datetime, timedelta
import hashlib

from app.models_old.token_blacklist import TokenBlacklistManager

def _hash_token(token: str) -> str:
    return hashlib.sha256(token.encode()).hexdigest()


def blacklist_token(token: str, expires_in_seconds: int):
    token_hash = _hash_token(token)
    expires_at = datetime.utcnow() + timedelta(seconds=expires_in_seconds)
    TokenBlacklistManager.add_token(token_hash, expires_at)


def is_token_blacklisted(token: str) -> bool:
    token_hash = _hash_token(token)
    return TokenBlacklistManager.is_token_blacklisted(token_hash)
