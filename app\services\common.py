import jwt, os
from jwt.exceptions import InvalidTokenError, ExpiredSignatureError
from dotenv import load_dotenv
from app.core.config import J<PERSON>TConfig
from flask import abort
import base64
from app.services.auth.jwt_token import derive_user_key, decrypt
from functools import wraps
from app.core.response import ServiceResponse
from app.core.constants import LIMIT_EXCEEDED, TOO_MANY_FILES
load_dotenv()

secret_key = JWTConfig().secret_key
allowed_reports = os.getenv('ALLOWED_REPORTS')
algorithm = JWTConfig().algorithm

def validate_and_decode_token(auth_header,email):
    """Validates and decodes the JWT token."""

    if not auth_header or not auth_header.startswith('Bearer '):
        abort(401, description = 'Authorization token is missing or invalid')

    token = auth_header.split(' ')[1]
    email = base64.b64decode(email).decode("utf-8")
    user_key = derive_user_key(email, salt=secret_key)
    decrypted_payload = decrypt(token, user_key)
    try:
        return jwt.decode(decrypted_payload, secret_key, algorithms=[algorithm])
    except ExpiredSignatureError as e:
        abort(401, description = 'Token has expired')
    
    except InvalidTokenError as e:
        abort(401, description = 'Invalid token')
    
    
def extract_required_claims(decoded_token):
    """Extracts and validates required claims from the decoded token."""

    organization_name = decoded_token.get('organizationName')
    user_email = decoded_token.get('email')

    if not organization_name or not user_email:
        raise ValueError('Token is missing required claims')


    return organization_name, user_email

def validate_organization_limits(fun):
    @wraps(fun)
    async def wrapper(request):
        """Validates organization limits and allowed file counts."""
        from ..models_old.user_reports import UserReportsManager
        user_reports = UserReportsManager(user_email = request.user_email,
                                        organization_name = request.organization_name,
                                        allowed_reports = allowed_reports
                                        )
        remaining_allowed_files = user_reports.calculate_remaining_reports(
                                                    user_reports.organization.id,
                                                    user_reports.organization.allowed_reports,
                                                    request.report_type
                                                    )
        if remaining_allowed_files == 0:
            return ServiceResponse.failure(error = LIMIT_EXCEEDED,status_code = 404)
        if request.twb_files_count > remaining_allowed_files:
            return ServiceResponse.failure(error = TOO_MANY_FILES.format(remaining_allowed_files))
        return await fun(request)
    return wrapper

def add_or_update_userReports(organization_name, user_email, migrated_files=0, dax_files=0, analyzed_files=0):
    from ..models_old.user_reports import UserReportsManager
    user_reports = UserReportsManager(user_email= user_email, organization_name= organization_name, allowed_reports= allowed_reports)
    user_reports.add_or_update(migrated_files, dax_files, analyzed_files)
