table People
	lineageTag: e2b41f4a-0a01-4a21-9bfa-1e3b72ab7a80

	column 'Regional Manager'
		dataType: string
		lineageTag: d08e3988-cc8e-44be-aa83-8d0c64ced45b
		summarizeBy: none
		sourceColumn: Regional Manager

		annotation SummarizationSetBy = Automatic

	column Region
		dataType: string
		lineageTag: c3fd89d5-8dd0-48cc-899f-2e82b62ea465
		summarizeBy: none
		sourceColumn: Region

		annotation SummarizationSetBy = Automatic

	partition People = m
		mode: import
		source =
				let
				    Source = Excel.Workbook(File.Contents("C:\Users\<USER>\Downloads\Sample - Superstoree.xlsx"), null, true),
				    People_Sheet = Source{[Item="People",Kind="Sheet"]}[Data],
				    #"Changed Type" = Table.TransformColumnTypes(People_Sheet,{{"Column1", type text}, {"Column2", type text}}),
				    #"Promoted Headers" = Table.PromoteHeaders(#"Changed Type", [PromoteAllScalars=true]),
				    #"Changed Type1" = Table.TransformColumnTypes(#"Promoted Headers",{{"Regional Manager", type text}, {"Region", type text}})
				in
				    #"Changed Type1"

	annotation PBI_ResultType = Table

	annotation PBI_NavigationStepName = Navigation

