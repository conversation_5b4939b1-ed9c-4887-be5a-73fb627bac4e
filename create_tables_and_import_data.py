#!/usr/bin/env python3
"""
Script to create tables in PostgreSQL test database and import CSV data
"""
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import csv
import os
import sys
from datetime import datetime

# Database connection parameters
DB_CONFIG = {
    'host': 'localhost',
    'port': '5432',
    'user': 'postgres',
    'password': 'postgres',
    'database': 'test'
}

def get_connection():
    """Get database connection"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except psycopg2.Error as e:
        print(f"Error connecting to database: {e}")
        return None

def create_schema():
    """Create biport_dev schema if it doesn't exist"""
    conn = get_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        cursor.execute("CREATE SCHEMA IF NOT EXISTS biport_dev")
        conn.commit()
        print("Schema 'biport_dev' created successfully.")
        return True
    except psycopg2.Error as e:
        print(f"Error creating schema: {e}")
        return False
    finally:
        cursor.close()
        conn.close()

def create_tables():
    """Create all tables based on CSV file structures"""
    conn = get_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        # Create ENUM types first
        cursor.execute("""
            DO $$ BEGIN
                CREATE TYPE server_status AS ENUM ('ACTIVE', 'INACTIVE');
            EXCEPTION
                WHEN duplicate_object THEN null;
            END $$;
        """)
        
        cursor.execute("""
            DO $$ BEGIN
                CREATE TYPE server_type AS ENUM ('ONPREMISE', 'CLOUD');
            EXCEPTION
                WHEN duplicate_object THEN null;
            END $$;
        """)
        
        cursor.execute("""
            DO $$ BEGIN
                CREATE TYPE server_auth_type AS ENUM ('CREDENTIALS', 'PAT');
            EXCEPTION
                WHEN duplicate_object THEN null;
            END $$;
        """)
        
        # 1. Organization Details Table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS biport_dev.organization_details (
                id UUID PRIMARY KEY,
                name VARCHAR NOT NULL,
                credits INTEGER,
                contact_person_name VARCHAR,
                mobile_number VARCHAR,
                address TEXT,
                service_type VARCHAR,
                created_by UUID,
                updated_by UUID,
                created_at TIMESTAMP,
                updated_at TIMESTAMP,
                is_deleted BOOLEAN DEFAULT FALSE
            )
        """)
        
        # 2. Roles Table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS biport_dev.roles (
                id UUID PRIMARY KEY,
                name VARCHAR NOT NULL,
                created_by UUID,
                updated_by UUID,
                created_at TIMESTAMP,
                updated_at TIMESTAMP,
                is_deleted BOOLEAN DEFAULT FALSE
            )
        """)
        
        # 3. Users Table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS biport_dev.users (
                id UUID PRIMARY KEY,
                name VARCHAR NOT NULL,
                email VARCHAR UNIQUE NOT NULL,
                password_hash VARCHAR NOT NULL,
                phone_number VARCHAR UNIQUE NOT NULL,
                organization_id UUID REFERENCES biport_dev.organization_details(id),
                role_id UUID REFERENCES biport_dev.roles(id),
                manager_id UUID REFERENCES biport_dev.users(id),
                created_by UUID,
                updated_by UUID,
                created_at TIMESTAMP,
                updated_at TIMESTAMP,
                is_deleted BOOLEAN DEFAULT FALSE
            )
        """)
        
        # 4. Tableau Server Details Table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS biport_dev.tableau_server_details (
                id UUID PRIMARY KEY,
                user_id UUID REFERENCES biport_dev.users(id),
                name VARCHAR NOT NULL,
                server_url VARCHAR NOT NULL,
                status server_status,
                type server_type NOT NULL,
                report_count INTEGER DEFAULT 0,
                project_count INTEGER DEFAULT 0,
                site_count INTEGER DEFAULT 0,
                created_by UUID,
                updated_by UUID,
                created_at TIMESTAMP,
                updated_at TIMESTAMP,
                is_deleted BOOLEAN DEFAULT FALSE,
                organization_id UUID REFERENCES biport_dev.organization_details(id)
            )
        """)
        
        # 5. Tableau Server Credentials Table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS biport_dev.tableau_server_credentials (
                id UUID PRIMARY KEY,
                server_id UUID REFERENCES biport_dev.tableau_server_details(id),
                pat_name VARCHAR,
                pat_secret VARCHAR,
                username VARCHAR,
                password VARCHAR,
                server_auth_type server_auth_type NOT NULL,
                created_by UUID,
                updated_by UUID,
                created_at TIMESTAMP,
                updated_at TIMESTAMP,
                is_deleted BOOLEAN DEFAULT FALSE
            )
        """)
        
        # 6. Tableau Site Details Table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS biport_dev.tableau_site_details (
                id UUID PRIMARY KEY,
                credentials_id UUID REFERENCES biport_dev.tableau_server_credentials(id),
                site_name VARCHAR NOT NULL,
                site_id VARCHAR NOT NULL,
                created_by UUID,
                updated_by UUID,
                created_at TIMESTAMP,
                updated_at TIMESTAMP,
                is_deleted BOOLEAN DEFAULT FALSE
            )
        """)
        
        # 7. Project Details Table (final_project_details.csv)
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS biport_dev.project_details (
                id UUID PRIMARY KEY,
                name VARCHAR NOT NULL,
                is_upload BOOLEAN,
                site_id UUID REFERENCES biport_dev.tableau_site_details(id),
                server_id UUID REFERENCES biport_dev.tableau_server_details(id),
                parent_id UUID,
                user_id UUID REFERENCES biport_dev.users(id),
                assigned_to UUID REFERENCES biport_dev.users(id),
                created_at TIMESTAMP,
                updated_at TIMESTAMP,
                is_deleted BOOLEAN DEFAULT FALSE
            )
        """)

        # 8. Report Details Table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS biport_dev.report_details (
                id UUID PRIMARY KEY,
                name VARCHAR NOT NULL,
                report_id UUID,
                project_id UUID REFERENCES biport_dev.project_details(id),
                is_analyzed BOOLEAN DEFAULT FALSE,
                analyzed_status VARCHAR,
                is_converted BOOLEAN DEFAULT FALSE,
                converted_status VARCHAR,
                is_migrated BOOLEAN DEFAULT FALSE,
                migrated_status VARCHAR,
                unit_tested BOOLEAN DEFAULT FALSE,
                uat_tested BOOLEAN DEFAULT FALSE,
                deployed BOOLEAN DEFAULT FALSE,
                is_scoped BOOLEAN DEFAULT FALSE,
                semantic_type VARCHAR,
                has_semantic_model BOOLEAN DEFAULT FALSE,
                view_count INTEGER DEFAULT 0
            )
        """)

        conn.commit()
        print("All tables created successfully.")
        return True

    except psycopg2.Error as e:
        print(f"Error creating tables: {e}")
        conn.rollback()
        return False
    finally:
        cursor.close()
        conn.close()

def safe_value(value, data_type='str'):
    """Convert CSV value to appropriate type, handling NULL strings"""
    if value is None or value == '' or value.upper() == 'NULL':
        return None

    if data_type == 'int':
        try:
            return int(value)
        except (ValueError, TypeError):
            return None
    elif data_type == 'bool':
        return value.lower() == 'true'
    else:
        return value

def import_csv_data():
    """Import data from CSV files into respective tables"""
    conn = get_connection()
    if not conn:
        return False

    try:
        cursor = conn.cursor()

        # Import organization_details
        print("Importing organization_details...")
        with open('data/organization_details.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                cursor.execute("""
                    INSERT INTO biport_dev.organization_details
                    (id, name, credits, contact_person_name, mobile_number, address, service_type,
                     created_by, updated_by, created_at, updated_at, is_deleted)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (id) DO NOTHING
                """, (
                    safe_value(row['id']), safe_value(row['name']),
                    safe_value(row['credits'], 'int'),
                    safe_value(row['contact_person_name']), safe_value(row['mobile_number']),
                    safe_value(row['address']), safe_value(row['service_type']),
                    safe_value(row['created_by']), safe_value(row['updated_by']),
                    safe_value(row['created_at']), safe_value(row['updated_at']),
                    safe_value(row['is_deleted'], 'bool')
                ))

        # Import roles
        print("Importing roles...")
        with open('data/roles.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                cursor.execute("""
                    INSERT INTO biport_dev.roles
                    (id, name, created_by, updated_by, created_at, updated_at, is_deleted)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (id) DO NOTHING
                """, (
                    safe_value(row['id']), safe_value(row['name']),
                    safe_value(row['created_by']), safe_value(row['updated_by']),
                    safe_value(row['created_at']), safe_value(row['updated_at']),
                    safe_value(row['is_deleted'], 'bool')
                ))

        # Import users (first pass without manager_id to avoid foreign key issues)
        print("Importing users (first pass)...")
        with open('data/users.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                cursor.execute("""
                    INSERT INTO biport_dev.users
                    (id, name, email, password_hash, phone_number, organization_id, role_id,
                     created_by, updated_by, created_at, updated_at, is_deleted)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (id) DO NOTHING
                """, (
                    safe_value(row['id']), safe_value(row['name']), safe_value(row['email']),
                    safe_value(row['password_hash']), safe_value(row['phone_number']),
                    safe_value(row['organization_id']), safe_value(row['role_id']),
                    safe_value(row['created_by']), safe_value(row['updated_by']),
                    safe_value(row['created_at']), safe_value(row['updated_at']),
                    safe_value(row['is_deleted'], 'bool')
                ))

        # Update users with manager_id (second pass)
        print("Updating users with manager_id (second pass)...")
        with open('data/users.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                if safe_value(row['manager_id']):
                    cursor.execute("""
                        UPDATE biport_dev.users
                        SET manager_id = %s
                        WHERE id = %s
                    """, (
                        safe_value(row['manager_id']), safe_value(row['id'])
                    ))

        # Import tableau_server_details
        print("Importing tableau_server_details...")
        with open('data/tableau_server_details.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                cursor.execute("""
                    INSERT INTO biport_dev.tableau_server_details
                    (id, user_id, name, server_url, status, type, report_count, project_count,
                     site_count, created_by, updated_by, created_at, updated_at, is_deleted, organization_id)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (id) DO NOTHING
                """, (
                    safe_value(row['id']), safe_value(row['user_id']), safe_value(row['name']),
                    safe_value(row['server_url']), safe_value(row['status']), safe_value(row['type']),
                    safe_value(row['report_count'], 'int') or 0,
                    safe_value(row['project_count'], 'int') or 0,
                    safe_value(row['site_count'], 'int') or 0,
                    safe_value(row['created_by']), safe_value(row['updated_by']),
                    safe_value(row['created_at']), safe_value(row['updated_at']),
                    safe_value(row['is_deleted'], 'bool'),
                    safe_value(row['organization_id'])
                ))

        # Import tableau_server_credentials
        print("Importing tableau_server_credentials...")
        with open('data/tableau_server_credentials.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                cursor.execute("""
                    INSERT INTO biport_dev.tableau_server_credentials
                    (id, server_id, pat_name, pat_secret, username, password, server_auth_type,
                     created_by, updated_by, created_at, updated_at, is_deleted)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (id) DO NOTHING
                """, (
                    safe_value(row['id']), safe_value(row['server_id']),
                    safe_value(row['pat_name']), safe_value(row['pat_secret']),
                    safe_value(row['username']), safe_value(row['password']),
                    safe_value(row['server_auth_type']), safe_value(row['created_by']),
                    safe_value(row['updated_by']), safe_value(row['created_at']),
                    safe_value(row['updated_at']), safe_value(row['is_deleted'], 'bool')
                ))

        # Import tableau_site_details
        print("Importing tableau_site_details...")
        with open('data/tableau_site_details.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                cursor.execute("""
                    INSERT INTO biport_dev.tableau_site_details
                    (id, credentials_id, site_name, site_id, created_by, updated_by,
                     created_at, updated_at, is_deleted)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (id) DO NOTHING
                """, (
                    safe_value(row['id']), safe_value(row['credentials_id']),
                    safe_value(row['site_name']), safe_value(row['site_id']),
                    safe_value(row['created_by']), safe_value(row['updated_by']),
                    safe_value(row['created_at']), safe_value(row['updated_at']),
                    safe_value(row['is_deleted'], 'bool')
                ))

        # Import final_project_details (project_details table)
        print("Importing project_details...")
        with open('data/final_project_details.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                cursor.execute("""
                    INSERT INTO biport_dev.project_details
                    (id, name, is_upload, site_id, server_id, parent_id, user_id, assigned_to,
                     created_at, updated_at, is_deleted)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (id) DO NOTHING
                """, (
                    safe_value(row['id']), safe_value(row['name']),
                    safe_value(row['is_upload'], 'bool'),
                    safe_value(row['site_id']), safe_value(row['server_id']),
                    safe_value(row['parent_id']), safe_value(row['user_id']),
                    safe_value(row['assigned_to']), safe_value(row['created_at']),
                    safe_value(row['updated_at']), safe_value(row['is_deleted'], 'bool')
                ))

        # Import report_details
        print("Importing report_details...")
        with open('data/report_details.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                cursor.execute("""
                    INSERT INTO biport_dev.report_details
                    (id, name, report_id, project_id, is_analyzed, analyzed_status, is_converted,
                     converted_status, is_migrated, migrated_status, unit_tested, uat_tested,
                     deployed, is_scoped, semantic_type, has_semantic_model, view_count)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (id) DO NOTHING
                """, (
                    safe_value(row['id']), safe_value(row['name']), safe_value(row['report_id']),
                    safe_value(row['project_id']), safe_value(row['is_analyzed'], 'bool'),
                    safe_value(row['analyzed_status']), safe_value(row['is_converted'], 'bool'),
                    safe_value(row['converted_status']), safe_value(row['is_migrated'], 'bool'),
                    safe_value(row['migrated_status']), safe_value(row['unit_tested'], 'bool'),
                    safe_value(row['uat_tested'], 'bool'), safe_value(row['deployed'], 'bool'),
                    safe_value(row['is_scoped'], 'bool'), safe_value(row['semantic_type']),
                    safe_value(row['has_semantic_model'], 'bool'),
                    safe_value(row['view_count'], 'int') or 0
                ))

        conn.commit()
        print("Data import completed successfully.")
        return True

    except Exception as e:
        print(f"Error importing data: {e}")
        conn.rollback()
        return False
    finally:
        cursor.close()
        conn.close()

def main():
    """Main function to create schema, tables and import data"""
    print("Starting database setup...")

    # Create schema
    if not create_schema():
        print("Failed to create schema. Exiting.")
        sys.exit(1)

    # Create tables
    if not create_tables():
        print("Failed to create tables. Exiting.")
        sys.exit(1)

    # Import data
    if not import_csv_data():
        print("Failed to import data. Exiting.")
        sys.exit(1)

    print("Database setup completed successfully!")

if __name__ == "__main__":
    main()
