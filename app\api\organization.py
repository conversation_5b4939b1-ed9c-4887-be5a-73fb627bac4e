from app.models.organization_details import OrganizationDetail, OrganizationDetailManager
from pydantic import BaseModel
from fastapi import <PERSON><PERSON>PEx<PERSON>, Depends
from app.core.dependencies import get_current_user
from app.models.users import User
from fastapi import APIRouter
from app.schemas.organization_details import OrganizationDetailRequest
from fastapi.responses import JSONResponse
from app.services.organization.organization_service import OrganizationDetailsService
from app.services.organization.organization_processor import OrganizationProcessor

organization_router = APIRouter()


@organization_router.post("/add-organization-test")
async def organization_details(organization: OrganizationDetailRequest):
    """Create and return a new organization detail."""
    response = OrganizationProcessor.create_organization_details(organization)
    return JSONResponse(content={"data": response.data, "error": response.error}, status_code = response.status_code)
