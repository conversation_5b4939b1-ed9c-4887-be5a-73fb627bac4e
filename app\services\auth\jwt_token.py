import jwt
import os
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.hashes import SHA256
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives.padding import P<PERSON>CS7
from cryptography.hazmat.backends import default_backend
from base64 import urlsafe_b64encode, urlsafe_b64decode
from datetime import datetime, timedelta, timezone
from dotenv import load_dotenv
import json
from base64 import binascii


from app.core.exceptions import ServerError, AuthenticationError
from app.services.auth.token_blacklist import is_token_blacklisted
# Load environment variables
load_dotenv()

# Constants
SECRET_KEY = os.getenv("JWT_SECRET_KEY")
JWT_ALGORITHM = "HS256"


# User-specific encryption key derivation
def derive_user_key(email: str, salt: str) -> bytes:
    """
    Derive a user-specific key using PBKDF2.
    """
    kdf = PBKDF2HMAC(
        algorithm=SHA256(),
        length=32,
        salt=salt.encode(),
        iterations=100000,
        backend=default_backend()
    )
    return kdf.derive(email.encode())


# Encrypt data
def encrypt(data: str, key: bytes) -> str:
    """
    Encrypt the given data using AES.
    """
    iv = os.urandom(16)  # Generate a random Initialization Vector
    cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=default_backend())
    encryptor = cipher.encryptor()
    padder = PKCS7(128).padder()
    padded_data = padder.update(data.encode()) + padder.finalize()
    ciphertext = encryptor.update(padded_data) + encryptor.finalize()
    # Combine IV and ciphertext
    return urlsafe_b64encode(iv + ciphertext).decode()


def decrypt(encrypted_data: str, key: bytes) -> str:
    try:
        padding = len(encrypted_data) % 4
        if padding:
            encrypted_data += '=' * (4 - padding)

        encrypted_data = urlsafe_b64decode(encrypted_data)
        iv = encrypted_data[:16]
        ciphertext = encrypted_data[16:]
        cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=default_backend())
        decryptor = cipher.decryptor()
        padded_data = decryptor.update(ciphertext) + decryptor.finalize()
        unpadder = PKCS7(128).unpadder()
        return (unpadder.update(padded_data) + unpadder.finalize()).decode()
    except Exception as e:
        raise AuthenticationError("Invalid or expired token")




# Generate a secure JWT
def create_secure_jwt(data: dict, expires_in: int) -> str:
    """
    Create a secure JWT that can only be read by the specific user.
    """
    payload = {
        "sub": json.dumps(data),
        "iat": datetime.now(timezone.utc),
        "exp": datetime.now(timezone.utc) + timedelta(minutes=expires_in)
    }
    # Generate user-specific encryption key
    user_key = derive_user_key(data["email"], salt=SECRET_KEY)
    jwt_token = jwt.encode(payload, SECRET_KEY, algorithm=JWT_ALGORITHM)
    encrypted_payload = encrypt(jwt_token, user_key)
    return encrypted_payload


def decode_secure_jwt(encrypted_jwt: str, user_email: str) -> dict:
    """Decode a secure JWT that is encrypted for the specific user."""
    try:
        if is_token_blacklisted(encrypted_jwt):
            raise AuthenticationError("Token has been blacklisted")

        user_key = derive_user_key(user_email, salt=SECRET_KEY)
        # encrypted_jwt += '=' * (-len(encrypted_jwt) % 4)
        decrypted_payload = decrypt(encrypted_jwt, user_key)

        decoded_payload = jwt.decode(
            decrypted_payload,
            SECRET_KEY,
            algorithms=[JWT_ALGORITHM]
        )

        if decoded_payload:
            return decoded_payload
        else:
            raise AuthenticationError("Invalid token payload")

    except jwt.ExpiredSignatureError:
        raise AuthenticationError("Token has expired")
    except jwt.InvalidTokenError:
        raise AuthenticationError("Invalid token")



def encode_base(email: str):
    try:
        encoded_email = urlsafe_b64encode(email.encode()).decode()
        return encoded_email
    except Exception as e:
        raise ServerError(f"Error encoding email: {str(e)}")


def decode_base(email: str):
    try:
        decoded_email = urlsafe_b64decode(email).decode()
        return decoded_email
    except (binascii.Error, UnicodeDecodeError):
        raise AuthenticationError("Invalid email")