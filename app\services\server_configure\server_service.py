import json
import uuid
import aiofiles
import requests
import xml.etree.ElementTree as ET
from fastapi import BackgroundTasks
import os

from app.core.exceptions import NotFoundError, ServerError, ConflictError, AuthenticationError
from app.core.response import ServiceResponse
from app.core.config import logger, S3Config
from app.core.constants import BA<PERSON><PERSON><PERSON>OUND_TASK, TABLEAU_VERSION, XMLNS, CLOUD_SITE_DISCOVERY_DIR, FILE_TYPE, LOCAL_DIR
from app.core.base_service import BaseService
from app.core.task import SiteDiscoveryService
from app.models_old.cloud_server import CloudServerDetailsManager
from app.models_old.on_premise_server import ONServerDetailsManager
from app.models_old.server import ServerDetailsManager, ServerDetails
from app.models_old.site_discovery import SiteDiscovery, SiteDiscoveryManager
from app.core.enums import DiscoverSiteStatus
from app.models_old.reports import ReportDetailsManager
from app.models_old.user import UserOld
from app.schemas.server import AddServerRequest, UpdateServerRequest, UpdateServerStatusRequest, ServerType
from app.models.tableau_server import TableauServerDetailManager, TableauServerDetail
from app.schemas.server import DeleteServerRequest

class ServerService(BaseService):
    """Handles all server-related operations."""
    
    def __init__(self):
        super().__init__()
        self.s3 = S3Config()
        self.background_task = os.getenv(BACKGROUND_TASK, False)
        self.site_discovery_manager = SiteDiscoveryManager()

    def _encode_for_display(self, text: str) -> str:
        """Encodes text for safe display."""
        
        logger.debug("Encoding text for safe display.")
        return text.encode('ascii', errors="backslashreplace").decode('utf-8')

    def create_xml_request(self, username: str, password: str, pat_name: str, pat_secret: str, site: str = "") -> bytes:
        """Creates an XML request for server authentication."""
        
        logger.debug(f"Creating XML request for username: {username}, site: {site}")
        xml_request = ET.Element('tsRequest')
        if username and password:
            credentials_element = ET.SubElement(xml_request, 'credentials', name=username, password=password)
        else:
            credentials_element = ET.SubElement(
                xml_request, 'credentials', personalAccessTokenName=pat_name, personalAccessTokenSecret=pat_secret
            )
        ET.SubElement(credentials_element, 'site', contentUrl=site)
        return ET.tostring(xml_request)

    def server_sign_in(self, request: AddServerRequest, site: str = ""):
        """Signs in to the server and retrieves authentication details."""
        
        logger.info(f"Signing in to server: {request.server_url}")
        url = f"{request.server_url}/api/{TABLEAU_VERSION}/auth/signin"
        if request.server_type.value == ServerType.ONPREMISE.value:
            return self._sign_in(
                url,
                request.username,
                request.password,
                request.pat_name,
                request.pat_secret,
                site
            )
            
        elif request.server_type.value == ServerType.CLOUD.value:
            # Validate all credentials; fail fast if any fail
            for credential in request.server_credentials:
                self._sign_in(
                    url,
                    None,
                    None,
                    credential.pat_name,
                    credential.pat_secret,
                    site
                )
            # Return the last successful one (or modify as needed)
            last_credential = request.server_credentials[-1]
            return self._sign_in(
                url,
                None,
                None,
                last_credential.pat_name,
                last_credential.pat_secret,
                site
            )


    def _sign_in(
        self, url: str, username: str, password: str, pat_name: str, pat_secret: str, site: str = ""
    ) -> tuple[str, str, str, int]:

        xml_request = self.create_xml_request(username, password, pat_name, pat_secret, site)
        headers = {'Content-Type': 'application/xml'}
        try:
            server_response = requests.post(url, data=xml_request, headers=headers)
            status_code = server_response.status_code
            server_response_text = self._encode_for_display(server_response.text)

            if status_code != 200:
                logger.error(f"Failed to sign in to the server. Status code: {status_code}")
                raise ServerError(f"Failed to sign in to the server. Status code: {status_code}")

            parsed_response = ET.fromstring(server_response_text)
            token = parsed_response.find('t:credentials', namespaces=XMLNS).get('token')
            site_id = parsed_response.find('.//t:site', namespaces=XMLNS).get('id')
            user_id = parsed_response.find('.//t:user', namespaces=XMLNS).get('id')
            logger.info("Successfully signed in to the server.")
            return token, site_id, user_id, status_code
        except ET.ParseError as e:
            logger.error(f"Error parsing server response: {e}")
            raise ServerError("Failed to parse server response")
        except requests.RequestException as e:
            logger.error(f"Error during server sign-in: {e}")
            raise AuthenticationError("Server is unreachable")

    def add_server(self, request: AddServerRequest, user: UserOld, bt: BackgroundTasks) -> ServiceResponse:
        """Adds a new server after validation and authentication."""
        logger.info(f"Processing add server request for server: {request.server_name}")
        
        if ServerDetailsManager.get_server_by_name_or_url(request.server_name, request.server_url):
            logger.error(f"Server already exists")
            raise ConflictError(f"Server already exists")

        ServerService().server_sign_in(
            request,
            site=""
        )

        server_id = ServerDetailsManager.add_server(
            user.id,
            request.server_name,
            request.server_url,
            request.server_type,
            user.id,  # created_by
            user.id  # updated_by
        )
        
        if request.server_type == ServerType.ONPREMISE.value:
            ONServerDetailsManager.add_on_premise_server(
                server_id,
                request.server_auth_type.value,
                request.username,
                request.password,
                request.pat_name,
                request.pat_secret,
                user.id,  # created_by
                user.id  # updated_by
            )
            ServerDetailsManager.update_site_count(server_id, 1)
            
        elif request.server_type == ServerType.CLOUD.value:
            not_allowed = []
            for credential in request.server_credentials:
                if CloudServerDetailsManager.get_cloud_server_by_pat_secret(
                    credential.pat_secret
                ) is not None:
                    not_allowed.append(credential.pat_secret)
                else:
                    cloud_server = CloudServerDetailsManager.add_cloud_server(
                        server_id,
                        credential.pat_name,
                        credential.pat_secret,
                        user.id,  # created_by
                        user.id  # updated_by
                    )

                    if self.background_task:
                        # Start the site discovery process in the background
                        site_discovery: SiteDiscovery = self.site_discovery_manager.add(server_id, "CLOUD", DiscoverSiteStatus.INITIATED, cloud_server.id)
                        client = SiteDiscoveryService(
                            server_url=request.server_url,
                            server_id=server_id,
                            server_type_id=cloud_server.id,
                            site_discovery_id=site_discovery.id,
                            pat_name=credential.pat_name,
                            pat_secret=credential.pat_secret
                        )
                        # Add the task to the background task queue
                        bt.add_task(client.run)                            
            ServerDetailsManager.update_site_count(server_id, len(request.server_credentials) - len(not_allowed))
            # Check if any PAT secrets already exist
            if not_allowed:
                logger.error(f"PAT secret(s) already exists: {', '.join(not_allowed)}")
                raise ConflictError(f"PAT secret(s) already exists: {', '.join(not_allowed)}")
            
        else:
            logger.error("Unsupported server type")

        logger.info(f"Server added successfully: {request.server_name}")
        return ServiceResponse.success("Server added successfully", 201)

    async def process_update_site_discovery(self, server_id: uuid.UUID) -> None:
        """Processes the update of site discovery."""
        server_id_str = str(server_id)

        file_name = f"{server_id}.{FILE_TYPE}"
        s3_path = os.path.join(CLOUD_SITE_DISCOVERY_DIR, file_name)
        local_path = os.path.join(LOCAL_DIR, file_name)

        await self._process_and_update_file(
            s3_object_path=s3_path,
            local_file_path=local_path,
            exclude_key="server_id",
            exclude_value=server_id_str,
        )

    async def _process_and_update_file(
        self,
        s3_object_path: str,
        local_file_path: str,
        exclude_key: str,
        exclude_value: str,
    ) -> None:
        """Downloads, filters, uploads, and cleans up a file from S3."""
        if not await self.s3.check_file_exists(s3_object_path):
            return
        try:
            await self.s3.download_file(s3_object_path, local_file_path)

            async with aiofiles.open(local_file_path, "r") as f:
                data = json.loads(await f.read())

            filtered_data = [item for item in data if item.get(exclude_key) != exclude_value]

            async with aiofiles.open(local_file_path, "w") as f:
                await f.write(json.dumps(filtered_data, indent=4))

            await self.s3.upload_to_s3(file_path=local_file_path, object_name=s3_object_path)

        finally:
            if os.path.exists(local_file_path):
                os.remove(local_file_path)

    async def delete_server(self, server_id: uuid.UUID) -> ServiceResponse:
        """Deletes a server if it exists."""
        
        logger.info(f"Processing delete server request for server ID: {server_id}")
        
        server = ServerDetailsManager.get_server_by_id(server_id)
        
        if not server:
            logger.error(f"Server not found: {server_id}")
            raise NotFoundError("Server not found")
        
        if server.server_type == ServerType.ONPREMISE.value:
            ONServerDetailsManager.delete_on_premise_server(server_id)
            
        else:
            CloudServerDetailsManager.delete_cloud_server_id(server_id)
            self.site_discovery_manager.delete(server_id)
            if self.background_task:
                await self.process_update_site_discovery(server_id)

        ReportDetailsManager.delete_reports(server_id)
        ServerDetailsManager.delete_server(server_id)
        
        logger.info(f"Server deleted successfully: {server_id}")
        return ServiceResponse.success("Server deleted successfully")

    def update_server(self, request: UpdateServerRequest, user: UserOld) -> ServiceResponse:
        """Updates an existing server."""
        
        logger.info(f"Processing update server request for server ID: {request.server_id}")
        
        server = ServerDetailsManager.get_server_by_id(request.server_id)
        
        if not server:
            logger.error(f"Server not found: {request.server_id}")
            raise NotFoundError("Server not found")
        
        if server.server_type != request.server_type.value:
            logger.error("Server type cannot be changed")
            raise ConflictError("Server type cannot be changed")
        
        on_premise_server = ONServerDetailsManager.get_on_premise_server_by_id(request.server_id)
        
        if on_premise_server:
            if on_premise_server.server_auth_type != request.server_auth_type.value:
                logger.error("Server authentication type cannot be changed")
                raise ConflictError("Server authentication type cannot be changed")
        
        ServerDetailsManager.update_server(
            request.server_id,
            request.server_name,
            request.server_url,
            user.id
        )
        
        if request.server_type == ServerType.ONPREMISE.value:
            update_data: dict = {
                "username": request.username,
                "password": request.password,
                "pat_name": request.pat_name,
                "pat_secret": request.pat_secret,
                "updated_by": user.id
            }
            
            ONServerDetailsManager.update_on_premise_server(
                request.server_id,
                update_data
            )
            
        elif request.server_type == ServerType.CLOUD.value:
            
            cloud_servers = CloudServerDetailsManager.get_cloud_server_by_id(request.server_id)
            pat_names = [credential.pat_name for credential in cloud_servers]
            remove_pat_names = set(pat_names) - set([credential.pat_name for credential in request.server_credentials])
            
            for credential in request.server_credentials:
                CloudServerDetailsManager.update_cloud_server(
                    request.server_id,
                    credential.pat_name,
                    credential.pat_secret,
                    user.id
                )
                
            for pat_name in remove_pat_names:
                CloudServerDetailsManager.delete_cloud_server(
                    request.server_id,
                    pat_name
                )

            ServerDetailsManager.update_site_count(
                request.server_id,
            len(server.site_count) - len(remove_pat_names)
            )
                
        logger.info(f"Server updated successfully: {request.server_id}")
        return ServiceResponse.success("Server updated successfully")

    def get_server_data(self, server: ServerDetails) -> dict:
        
        on_premise_server = ONServerDetailsManager.get_on_premise_server_by_id(server.id)
        
        cloud_server = CloudServerDetailsManager.get_cloud_server_by_id(server.id)

        logger.info(f"Server details fetched successfully for server ID: {server.id}")
        
        # Constructing response
        response_data = {
            "id": str(server.id),
            "server_name": server.server_name,
            "server_url": server.server_url,
            "status": server.status.value,
            "server_type": server.server_type.value,
            "server_credentials": [],
            "server_auth_type": None,
            "username": None,
            "password": None,
            "pat_name": None,
            "pat_secret": None,
        }

        if server.server_type == ServerType.ONPREMISE.value and on_premise_server:
            response_data.update({
                "server_auth_type": on_premise_server.server_auth_type.value,
                "username": on_premise_server.username,
                "password": on_premise_server.password,
                "pat_name": on_premise_server.pat_name,
                "pat_secret": on_premise_server.pat_secret
            })

        elif server.server_type == ServerType.CLOUD.value and cloud_server:
            response_data["server_credentials"] = [
                {"pat_name": cred.pat_name, "pat_secret": cred.pat_secret} for cred in cloud_server
            ]
        return response_data
    
    def get_server(self, server_id: uuid.UUID) -> ServiceResponse:
        """Fetches details of a server."""
        
        logger.info(f"Fetching server details for server ID: {server_id}")
        server = ServerDetailsManager.get_server_by_id(server_id)
        
        if not server:
            logger.error(f"Server not found: {server_id}")
            raise NotFoundError("Server not found")
        
        data = self.get_server_data(server)

        logger.info(f"Server details fetched successfully for server ID: {server_id}")
        return ServiceResponse.success(data)
    
    def get_servers(self, page: int, page_size: int) -> ServiceResponse:
        """Fetch servers with pagination using Pydantic ORM conversion."""
        
        total_servers = ServerDetailsManager.get_total_servers()
        offset = (page - 1) * page_size
        
        servers = ServerDetailsManager.get_servers(offset=offset, limit=page_size)
        data = []
        
        for server in servers:
            server_data = self.get_server_data(server)
            data.append(server_data)
            
        response_data = {
            "content": data,
            "total": total_servers,
            "page": page,
            "page_size": page_size if total_servers > page_size else total_servers,
            "total_pages": (total_servers + page_size - 1) // page_size,
        }
        
        logger.info(f"Fetched {len(data)} servers successfully.")
        return ServiceResponse.success(response_data)

    def get_org_servers(self, organization_id: uuid.UUID, page: int, page_size: int) -> ServiceResponse:
        """Fetch servers for a specific organisation with pagination using the new TableauServerDetail model."""
        
        total_servers = TableauServerDetailManager.get_total_servers_by_org_id(organization_id)
        offset = (page - 1) * page_size
        
        servers = TableauServerDetailManager.get_servers_by_org_id(organization_id, offset=offset, limit=page_size)
        data = []
        
        for server in servers:
            server_data = {
                "id": str(server.id),
                "name": server.name,
                "server_url": server.server_url,
                "status": server.status.value,
            }
            data.append(server_data)
            
        response_data = {
            "content": data,
            "total": total_servers,
            "page": page,
            "page_size": page_size if total_servers > page_size else total_servers,
            "total_pages": (total_servers + page_size - 1) // page_size,
        }
        
        logger.info(f"Fetched {len(data)} servers for organisation {organization_id} successfully.")
        return ServiceResponse.success(response_data)
    
    def update_server_status(self, request: UpdateServerStatusRequest) -> ServiceResponse:
        """Updates the status of a server."""
        logger.info(f"Processing update server status request for server ID: {request.server_id}")
        server = ServerDetailsManager.get_server_by_id(request.server_id)
        
        if not server:
            logger.error(f"Server not found: {request.server_id}")
            raise NotFoundError("Server not found")

        ServerDetailsManager.update_server_status(request.server_id, request.status)
        
        logger.info(f"Server status updated successfully: {request.server_id}")
        return ServiceResponse.success("Server status updated successfully")

    def update_tableau_server_status(self, request: UpdateServerStatusRequest) -> ServiceResponse:
        """Updates the status of a TableauServerDetail."""
        logger.info(f"Processing update TableauServerDetail status request for server ID: {request.server_id}")
        
        server = TableauServerDetailManager.get_server_by_id(request.server_id)
        
        if not server:
            logger.error(f"TableauServerDetail not found: {request.server_id}")
            raise NotFoundError("Server not found")

        TableauServerDetailManager.update_server_status(request.server_id, request.status)
        
        logger.info(f"TableauServerDetail status updated successfully: {request.server_id}")
        return ServiceResponse.success("Server status updated successfully")

    def soft_delete_tableau_server(self, request: DeleteServerRequest) -> ServiceResponse:
        """Soft deletes a TableauServerDetail by setting is_deleted=True."""
        server = TableauServerDetailManager.get_server_by_id(request.server_id)
        if not server:
            raise NotFoundError("Server not found")
        TableauServerDetailManager.soft_delete_server(request.server_id)
        return ServiceResponse.success("Server deleted successfully")
