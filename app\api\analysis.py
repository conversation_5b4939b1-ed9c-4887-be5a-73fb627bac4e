from fastapi import APIRouter, Form, HTTPException, status, Depends, Query, Body
from fastapi.responses import JSONResponse
from typing import List, Optional

from app.models_old.user import UserOld
from app.services.analysis import AnalyseProcessor
from app.core.dependencies import get_current_user
from app.schemas.migrate import WorkbooksRequest

analysis_router = APIRouter()   

@analysis_router.post("/generate_report", response_model=dict)
async def analyse_report(
    body: WorkbooksRequest,
    is_upload_file: bool = Query(..., description="True if using file_path directly, False for workbook ID"),
    user: UserOld = Depends(get_current_user)
):
   
    response = await AnalyseProcessor.analyse_processor(
        twb_files=body.s3_paths if is_upload_file else body.workbook_ids,
        is_upload_file=is_upload_file,
        user=user
    )

    return JSONResponse(
        content={"data": response.data, "error": None},
        status_code=status.HTTP_200_OK
    )