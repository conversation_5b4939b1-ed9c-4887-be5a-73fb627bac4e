from fastapi import APIRouter, BackgroundTasks, Query, Depends
from fastapi.responses import JSONResponse
import uuid
from app.models_old.user import UserOld
from app.models.users import User
from app.schemas import *
from app.services.server_configure.server_processor import ServerProcessor
from app.core.dependencies import get_current_user, get_current_new_user

server_router = APIRouter()

@server_router.post("/server", response_model=dict)
async def add_server(request: AddServerRequest, background_tasks: BackgroundTasks, user: UserOld = Depends(get_current_user)):
    """API to add a new server."""
    response = ServerProcessor.process_add_server(request, user, background_tasks)
    return JSONResponse(content={"data": response.data, "error": response.error}, status_code=response.status_code)

@server_router.delete("/server", response_model=dict)
async def delete_server(server_id: uuid.UUID = Query(..., description="Server ID"), user: UserOld = Depends(get_current_user)):
    """API to delete a server."""
    response = await ServerProcessor.process_delete_server(server_id)
    return JSONResponse(content={"data": response.data, "error": response.error}, status_code=response.status_code)

@server_router.put("/server", response_model=dict)
async def update_server(request: UpdateServerRequest, user: UserOld = Depends(get_current_user)):
    """API to update a server."""
    response = ServerProcessor.process_update_server(request, user)
    return JSONResponse(content={"data": response.data, "error": response.error}, status_code=response.status_code)

@server_router.get("/server", response_model=GetServerResponse)
async def get_server(server_id: uuid.UUID = Query(..., description="Server ID"), user: UserOld = Depends(get_current_user)):
    """API to get a single server."""
    response = ServerProcessor.process_get_server(server_id)
    return JSONResponse(content={"data": response.data, "error": response.error}, status_code=response.status_code)

@server_router.get("/servers", response_model=ServerListResponse)
async def get_servers(page: int = Query(1, ge=1), page_size: int = Query(10, ge=1, le=100), user: UserOld = Depends(get_current_user)):
    """API to get all servers with pagination."""
    response = ServerProcessor.process_get_servers(page=page, page_size=page_size)
    return JSONResponse(content={"data": response.data, "error": response.error}, status_code=response.status_code)

@server_router.get("/list-servers", response_model=TableauServerResponse)
async def get_org_servers(page: int = Query(1, ge=1), page_size: int = Query(10, ge=1, le=100), user: User = Depends(get_current_new_user)):
    """API to get all servers for the current user's organisation with pagination."""
    # Use the organization_id directly from the User model and cast to UUID
    response = ServerProcessor.process_get_org_servers(uuid.UUID(str(user.organization_id)), page=page, page_size=page_size)
    return JSONResponse(content={"data": response.data, "error": response.error}, status_code=response.status_code)

@server_router.patch("/update_status")
async def update_server_status(request: UpdateServerStatusRequest, user: UserOld = Depends(get_current_user)):
    """API to update the status of a server."""
    response = ServerProcessor.process_update_server_status(request)
    return JSONResponse(content={"data": response.data, "error": response.error}, status_code=response.status_code)

@server_router.patch("/update-status")
async def update_tableau_server_status(
    request: UpdateServerStatusRequest,
    current_user: User = Depends(get_current_new_user)
):
    """Updates the status of a TableauServerDetail."""
    response = ServerProcessor().update_tableau_server_status(request)
    return JSONResponse(content={"data": response.data, "error": response.error}, status_code=response.status_code)

@server_router.patch("/delete-server")
async def soft_delete_tableau_server(
    request: DeleteServerRequest,
    user: User = Depends(get_current_new_user)
):
    """Soft deletes a TableauServerDetail by setting is_deleted=True."""
    response = ServerProcessor.process_soft_delete_tableau_server(request)
    return JSONResponse(content={"data": response.data, "error": response.error}, status_code=response.status_code)
