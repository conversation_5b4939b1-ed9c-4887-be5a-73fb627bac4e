from datetime import datetime
import uuid
from sqlalchemy import Column, <PERSON>, Integer, <PERSON><PERSON><PERSON>, Enum, DateTime
from sqlalchemy.dialects.postgresql import UUID
from app.core import Base
from app.core.enums import ServerAuthType
from app.core import Base, scoped_context


class OnPremiseServer(Base):
    __tablename__ = "on_premise_server"

    id = Column(UUID(as_uuid=True), primary_key=True, nullable=False)
    server_id = Column(UUID, ForeignKey('server_details.id'), nullable=False)
    server_auth_type = Column(Enum(ServerAuthType), nullable=False)
    username = Column(String(100), nullable=True)
    password = Column(String(100), nullable=True)
    pat_name = Column(String(100), nullable=True)
    pat_secret = Column(String(100), nullable=True)
    created_by = Column(Integer, nullable=False)
    updated_by = Column(Integer, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
class ONServerDetailsManager:
    @staticmethod
    def add_on_premise_server(server_id, server_auth_type, username, password, pat_name, pat_secret, created_by, updated_by):
        """Adds a new on-premise server to the database."""
        with scoped_context() as session:
            new_server = OnPremiseServer(
                id=uuid.uuid4(),
                server_id=server_id,
                server_auth_type=server_auth_type,
                username=username,
                password=password,
                pat_name=pat_name,
                pat_secret=pat_secret,
                created_by=created_by,
                updated_by=updated_by
            )
            session.add(new_server)
            session.commit()
    
    @staticmethod
    def get_on_premise_server_by_id(server_id) -> OnPremiseServer:
        """Fetch an on-premise server by its ID."""
        with scoped_context() as session:
            return session.query(OnPremiseServer).filter_by(server_id=server_id).first()
        
    @staticmethod
    def update_on_premise_server(server_id, update_data) -> None:
        """Updates an existing on-premise server."""
        with scoped_context() as session:
            server = session.query(OnPremiseServer).filter_by(server_id=server_id).first()
            if server:
                for key, value in update_data.items():
                    if hasattr(server, key):
                        setattr(server, key, value)
                session.commit()
            else:
                raise ValueError("On Premise Server not found")
            
    @staticmethod
    def delete_on_premise_server(server_id) -> None:
        """Deletes an on-premise server by its ID."""
        with scoped_context() as session:
            server = session.query(OnPremiseServer).filter_by(server_id=server_id).first()
            if server:
                session.delete(server)
                session.commit()
            else:
                raise ValueError("On Premise Server not found")