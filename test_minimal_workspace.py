#!/usr/bin/env python3
"""
Minimal test to check workspace service imports and basic functionality
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("🧪 Testing Minimal Workspace Service")
print("=" * 50)

# Test imports step by step
try:
    print("1. Testing basic imports...")
    from app.core import ServiceResponse
    print("   ✅ ServiceResponse imported")
    
    from app.models.users import User, UserManager
    print("   ✅ User models imported")
    
    from app.services.workspace.workspace_service import WorkspaceService
    print("   ✅ WorkspaceService imported")
    
    from app.services.workspace.workspace_procssor import WorkspaceProcessor
    print("   ✅ WorkspaceProcessor imported")
    
except Exception as e:
    print(f"   ❌ Import error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

# Test creating service instance
try:
    print("\n2. Testing service instantiation...")
    service = WorkspaceService()
    print("   ✅ WorkspaceService instance created")
    
except Exception as e:
    print(f"   ❌ Service instantiation error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

# Test getting a user from database
try:
    print("\n3. Testing user retrieval...")
    user = UserManager.get_user_by_email("<EMAIL>", load_role=True)
    if user:
        print(f"   ✅ User found: {user.name} ({user.email})")
        print(f"   Role: {user.role.name.value if user.role else 'No role'}")
        
        # Test workspace service with this user
        print("\n4. Testing workspace service call...")
        response = service.get_reports_by_user_role(user)
        print(f"   Response success: {response.success}")
        print(f"   Response error: {response.error}")
        if response.success:
            print(f"   Data count: {len(response.data) if response.data else 0}")
            if response.data:
                print(f"   Sample report: {response.data[0]}")
        
    else:
        print("   ❌ User not found")
        
except Exception as e:
    print(f"   ❌ User/service test error: {e}")
    import traceback
    traceback.print_exc()

# Test processor
try:
    print("\n5. Testing workspace processor...")
    if user:
        response = WorkspaceProcessor.process_get_reports_by_user_role(user)
        print(f"   Processor response success: {response.success}")
        print(f"   Processor response error: {response.error}")
        if response.success:
            print(f"   Processor data count: {len(response.data) if response.data else 0}")
    
except Exception as e:
    print(f"   ❌ Processor test error: {e}")
    import traceback
    traceback.print_exc()

print(f"\n✅ Minimal workspace test completed!")
