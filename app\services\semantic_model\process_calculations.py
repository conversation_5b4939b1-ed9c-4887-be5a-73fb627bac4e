import xml.etree.ElementTree as ET
import re, json, xmltodict, os
from openai import OpenAI, APIConnectionError, AuthenticationError
from itertools import islice
from app.core.config import OpenAIConfig, logger

openai_config = OpenAIConfig()
client = openai_config.get_openai_client()

def resolve_dependencies(calculations):
    if not calculations:
        return []

    name_to_caption = {calc['name']: calc.get('caption') for calc in calculations}

    def resolve_formula_with_captions(formula):
        """
        Resolves formulas by replacing dependencies with their captions.
        """
        pattern = r"\[.*?\]"
        matches = re.findall(pattern, formula)

        if not matches:
            return formula

        for match in matches:
            if match in name_to_caption:
                resolved_caption = name_to_caption[match]
                if resolved_caption:  
                    resolved_caption = f"[{resolved_caption}]"
                    formula = formula.replace(match, resolved_caption)
                else:
                    return formula
        return formula

    resolved_calculations = []
    for calc in calculations:
        independent_formula = resolve_formula_with_captions(calc['formula']) if calc['formula'] else "No formula"
        resolved_calculations.append({
            "name": calc['name'],
            "caption": calc.get('caption', calc['name']),
            "formula": independent_formula,
            "data_type": calc.get('data_type', ''),
            "role": calc.get('role', ''),
            "column_type": calc.get('column_type', '')
        })

    return resolved_calculations

def calculate_fields(root):
    logger.info("Started extracting calculated fields from XML root.")
    try:
        calculations_list = []
        datasources_element = root.find('datasources')

        if datasources_element is None:
            logger.warning("No <datasources> tag found in the XML.")
            return []

        datasources = datasources_element.findall('datasource')
        logger.info(f"Found {len(datasources)} datasource(s).")

        for datasource in datasources:
            columns = datasource.findall("column")
            logger.info(f"Datasource has {len(columns)} column(s).")

            for column in columns:
                formula_element = column.find("calculation")

                if formula_element is not None and 'formula' in formula_element.attrib:
                    column_name = column.get('name')
                    formula = formula_element.get('formula')
                    caption = column.get('caption')
                    data_type = column.get('datatype')
                    role = column.get('role')
                    column_type = column.get('type')

                    calculations_list.append({
                        "name": column_name,
                        "formula": formula,
                        "caption": caption,
                        "data_type": data_type,
                        "role": role,
                        "column_type": column_type
                    })
                    logger.info(f"Extracted calculated field: name={column_name}, caption={caption}, type={column_type}")

        logger.info(f"Completed extracting calculated fields. Total calculated fields found: {len(calculations_list)}")
        return resolve_dependencies(calculations_list)

    except ET.ParseError as e:
        logger.error(f"Error parsing XML file: {str(e)}")
        return []
    except Exception as e:
        logger.error(f"Unexpected error during calculation extraction: {str(e)}")
        return []


def fix_xml_line(xml_line):
    return re.sub(r"formula=x'([^']*)'", r'formula="\1"', xml_line)

def extract_json(file_path):
    try:
        with open(file_path, encoding='utf-8') as xml_file:
            content = xml_file.read()
            fixed_content = fix_xml_line(content)
            
            data_dict = xmltodict.parse(fixed_content)
        return data_dict
    except Exception as e:
        raise ValueError(f"Error in converting xml to json - {str(e)}")

def get_tables_data(root):
    logger.info("Started extracting table-column metadata from XML root.")
    try:
        table_column_data = {}
        total_records = 0

        for metadata_record in root.findall(".//metadata-record[@class='column']"):
            table_name = metadata_record.findtext("parent-name")
            column_name = metadata_record.findtext("local-name")

            if not table_name or not column_name:
                logger.warning("Skipping a column due to missing table name or column name.")
                continue

            table_name = table_name.strip('[]')
            column_name = column_name.strip('[]')
            total_records += 1

            if table_name in table_column_data:
                table_column_data[table_name].append(column_name)
                logger.info(f"Appended column '{column_name}' to existing table '{table_name}'.")
            else:
                table_column_data[table_name] = [column_name]
                logger.info(f"Added new table '{table_name}' with column '{column_name}'.")

        logger.info(f"Completed extraction of table-column metadata. Total tables: {len(table_column_data)}, total columns: {total_records}")
        return table_column_data

    except Exception as e:
        logger.error(f"Error while extracting table-column data: {str(e)}")
        raise ValueError(f"Error in getting table column data - {str(e)}")

    
def get_formula_columns(input_string):
    return re.findall(r'\[(.*?)\]', input_string)

def find_tables_with_columns(table_column_data, columns_to_match):
    matching_tables = []
    
    for table, columns in table_column_data.items():
        if any(column in columns for column in columns_to_match):
            matching_tables.append(table)
    
    return matching_tables

def resolve_table_name(calc_column_list, table_column_data, calc_fields):
    logger.info(f"Attempting to resolve table name for columns: {calc_column_list}")
    
    if not calc_column_list:
        logger.info("Cannot resolve table name, table column list is empty. Aborting recursion")
        return "UnresolvedTable"
       
    table_name = find_tables_with_columns(table_column_data, calc_column_list)
    logger.info(f"Directly searching for tables with these columns. Result: {table_name}")

    if table_name:
        logger.info(f"Direct match found. Returning table: {table_name[0]}")
        return table_name[0]
    else:
        logger.info("No direct table match. Attempting to resolve via calculated field lookup.")
        col_value = calc_column_list[0]
        logger.info(f"Using first column '{col_value}' as caption to find its formula.")
        
        calc_formula = ''
        for data in calc_fields:
            if col_value == data.get('caption'):
                calc_formula = data.get('formula')
                logger.info(f"Found matching formula for '{col_value}': {calc_formula}")
                break
        
        if not calc_formula:
            logger.info(f"Could not find a formula for caption '{col_value}'.")

        calc_column_list = get_formula_columns(calc_formula)
        logger.info(f"Extracted new column list from formula: {calc_column_list}. Making a recursive call.")
        return resolve_table_name(calc_column_list, table_column_data, calc_fields)
    
def get_openai_pbi_response(tab_formula, table_info):
    try:
        response = client.chat.completions.create(
        model="gpt-4o",
        messages=[
            {
            "role": "system",
            "content": [
                {
                "type": "text",
                "text": '''You are an expert in both Tableau and Power BI. Your task is to convert a list of Tableau calculated fields into their corresponding Power BI DAX functions. For each Tableau formula, you must accurately translate it into the equivalent DAX expression in Power BI, ensuring that the table and column names are maintained exactly as provided.

                Input Format:
                Tableau Formula: A list of Tableau calculated fields (formulae) that need to be converted.

                Table Information: A dictionary containing the table names and their associated columns.

                Output Instructions:
                DAX Conversion: Provide the exact Power BI DAX equivalent for each Tableau formula.

                Preserve Structure: The structure of the Tableau formula must be maintained without alteration in the DAX conversion.

                No Additional Aggregations: Do not introduce any new aggregations unless explicitly specified in the Tableau formula.

                Exact Field Mapping: Ensure the translated DAX formula references the correct table and field names from the provided table information.

                Formatting: Output the results in the following JSON format, ensuring the Power BI DAX formula is in a single line:

                {
                "json_output": [
                    {
                    "tableau_formula": "<Tableau formula>",
                    "pbi_dax": "<Power BI DAX formula>"
                    }
                ]
                }

                Make sure all formulas are carefully translated to maintain functional equivalence between Tableau and Power BI without skipping any.'''
                }
            ]
            },
            {
            "role": "user",
            "content": [
                {
                "type": "text",
                "text": f"Tableau Formula:{tab_formula}, Table Information:{table_info}"
                }
            ]
            }
        ],
        response_format={
            "type": "json_object"
        }
        )
        res = response.choices[0].message.content
        return res
    except APIConnectionError as e:
        print(f"Issue in connecting to OpenAI API: {str(e)}")
        raise ValueError('Issue in connecting to OpenAI API')
    except AuthenticationError as e:
        print(f"OpenAI key or token was invalid, expired, or revoked.: {str(e)}")
        raise ValueError('OpenAI key or token was invalid, expired, or revoked.')  

def chunk_list(data, chunk_size):
    """Splits a list into chunks of a specified size."""
    it = iter(data)
    return iter(lambda: list(islice(it, chunk_size)), [])

def process_table_column_data(table_column_data, calc_fields):
    logger.info("Starting processing of calculated fields.")
    logger.info(f"Initial tables: {list(table_column_data.keys())}")

    for i, calc in enumerate(calc_fields):
        calc_name = calc.get('name', '')
        calc_caption = calc.get('caption', '')
        calc_formula = calc.get("formula", "")

        logger.info(f"Processing field {i+1}/{len(calc_fields)}: Name='{calc_name}', Caption='{calc_caption}'")

        formula_columns = get_formula_columns(calc_formula)

        if 'Parameter' in calc_name or not formula_columns:
            logger.info(f"Skipping '{calc_name}': It is a parameter or contains no columns in its formula.")
            continue

        logger.info(f"Found formula columns for '{calc_name}': {formula_columns}")
        table_name = resolve_table_name(formula_columns, table_column_data, calc_fields)

        if table_name not in table_column_data:
            logger.info(f"Could not resolve a valid table for '{calc_name}'. Resolved name '{table_name}' is not in the provided table data. Skipping.")
            continue
            
        logger.info(f"Resolved table for '{calc_name}' is '{table_name}'.")
        table_column_data[table_name].append(calc_caption)
        logger.info(f"Successfully appended caption '{calc_caption}' to table '{table_name}'.")

    logger.info("Finished processing all calculated fields.")
    return table_column_data

def get_calculations_data(twb_file_path):
    logger.info(f"Starting calculation data extraction from: {twb_file_path}")

    try:
        tree = ET.parse(twb_file_path)
        root = tree.getroot()
    except Exception as e:
        logger.error(f"Failed to parse XML file: {twb_file_path}. Error: {str(e)}")
        raise

    logger.info("Extracting table-column metadata from XML root.")
    table_column_data = get_tables_data(root)

    logger.info("Extracting calculated fields from XML root.")
    calc_fields = calculate_fields(root)

    logger.info("Merging calculated fields with table-column data.")
    table_column_data = process_table_column_data(table_column_data, calc_fields)

    output_json = []
    tableau_formulae = [calc.get('formula', '') for calc in calc_fields]
    pbi_dax_data = []

    logger.info(f"Total formulas to process: {len(tableau_formulae)}")
    for idx, chunk in enumerate(chunk_list(tableau_formulae, 10)):
        try:
            logger.info(f"Processing chunk {idx + 1} with {len(chunk)} formulas.")
            response = get_openai_pbi_response(chunk, table_column_data)
            pbi_dax_chunk = json.loads(response)
            logger.info(f"OpenAI DAX chunk response: {pbi_dax_chunk}")
            pbi_dax_data.extend(pbi_dax_chunk.get("json_output", []))
        except json.JSONDecodeError as e:
            logger.error(f"JSON decoding failed for chunk {idx + 1}. Error: {str(e)}")

    dax_lookup = {
        dax["tableau_formula"].replace(" ", ""): dax["pbi_dax"]
        for dax in pbi_dax_data
    }

    logger.info("Constructing final output JSON from calculated field mappings.")
    for calc in calc_fields:
        calc_name = calc.get('name', '')
        calc_caption = calc.get('caption', '')
        calc_formula = calc.get('formula', '')
        data_type = calc.get('data_type', '')
        role = calc.get('role', '')
        column_type = calc.get('column_type', '')

        formula_columns = get_formula_columns(calc_formula)
        if 'Parameter' in calc_name or not formula_columns:
            logger.info(f"Skipping calculation '{calc_name}' - parameter or no formula columns.")
            continue

        pbi_dax_formula = dax_lookup.get(calc_formula.replace(" ", ""), 'Failed to convert formula')
        table_name = resolve_table_name(formula_columns, table_column_data, calc_fields)

        if " " in calc_caption:
            calc_caption = f"'{calc_caption}'"

        logger.info(f"Generated DAX for calc '{calc_caption}' in table '{table_name}': {pbi_dax_formula}")
        output_json.append({
            "caption": calc_caption,
            "formula": pbi_dax_formula,
            "table_name": table_name,
            "data_type": data_type,
            "role": role,
            "type": column_type
        })

    logger.info(f"Completed processing. Total calculation fields generated: {len(output_json)}")
    return output_json


