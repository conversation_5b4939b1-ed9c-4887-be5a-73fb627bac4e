#!/usr/bin/env python3
"""
Script to fix remaining data imports by handling foreign key constraints properly
"""
import psycopg2
import csv
import sys

# Database connection parameters
DB_CONFIG = {
    'host': 'localhost',
    'port': '5432',
    'user': 'postgres',
    'password': 'postgres',
    'database': 'test'
}

def get_connection():
    """Get database connection"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except psycopg2.Error as e:
        print(f"Error connecting to database: {e}")
        return None

def safe_value(value, data_type='str'):
    """Convert CSV value to appropriate type, handling NULL strings"""
    if value is None or value == '' or value.upper() == 'NULL':
        return None
    
    if data_type == 'int':
        try:
            return int(value)
        except (ValueError, TypeError):
            return None
    elif data_type == 'bool':
        return value.lower() == 'true'
    else:
        return value

def fix_tableau_data():
    """Fix tableau server data import"""
    conn = get_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        # First, let's import tableau_server_details with existing users only
        print("Importing tableau_server_details (with valid user references)...")
        
        # Get valid user IDs
        cursor.execute("SELECT id FROM biport_dev.users")
        valid_users = {row[0] for row in cursor.fetchall()}
        
        # Get valid organization IDs
        cursor.execute("SELECT id FROM biport_dev.organization_details")
        valid_orgs = {row[0] for row in cursor.fetchall()}
        
        with open('data/tableau_server_details.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            imported_count = 0
            for row in reader:
                user_id = safe_value(row['user_id'])
                org_id = safe_value(row['organization_id'])
                
                # Only import if user and org exist
                if user_id in valid_users and org_id in valid_orgs:
                    try:
                        cursor.execute("""
                            INSERT INTO biport_dev.tableau_server_details 
                            (id, user_id, name, server_url, status, type, report_count, project_count, 
                             site_count, created_by, updated_by, created_at, updated_at, is_deleted, organization_id)
                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """, (
                            safe_value(row['id']), user_id, safe_value(row['name']), 
                            safe_value(row['server_url']), safe_value(row['status']), safe_value(row['type']), 
                            safe_value(row['report_count'], 'int') or 0,
                            safe_value(row['project_count'], 'int') or 0,
                            safe_value(row['site_count'], 'int') or 0,
                            safe_value(row['created_by']), safe_value(row['updated_by']),
                            safe_value(row['created_at']), safe_value(row['updated_at']), 
                            safe_value(row['is_deleted'], 'bool'), org_id
                        ))
                        imported_count += 1
                    except psycopg2.IntegrityError as e:
                        print(f"Skipping server {row['id']}: {e}")
                        conn.rollback()
                        continue
                else:
                    print(f"Skipping server {row['id']} - invalid user or org reference")
        
        print(f"Imported {imported_count} tableau servers")
        
        # Now import credentials for existing servers
        print("Importing tableau_server_credentials...")
        cursor.execute("SELECT id FROM biport_dev.tableau_server_details")
        valid_servers = {row[0] for row in cursor.fetchall()}
        
        with open('data/tableau_server_credentials.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            cred_count = 0
            for row in reader:
                server_id = safe_value(row['server_id'])
                if server_id in valid_servers:
                    try:
                        cursor.execute("""
                            INSERT INTO biport_dev.tableau_server_credentials 
                            (id, server_id, pat_name, pat_secret, username, password, server_auth_type,
                             created_by, updated_by, created_at, updated_at, is_deleted)
                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """, (
                            safe_value(row['id']), server_id, 
                            safe_value(row['pat_name']), safe_value(row['pat_secret']),
                            safe_value(row['username']), safe_value(row['password']),
                            safe_value(row['server_auth_type']), safe_value(row['created_by']), 
                            safe_value(row['updated_by']), safe_value(row['created_at']), 
                            safe_value(row['updated_at']), safe_value(row['is_deleted'], 'bool')
                        ))
                        cred_count += 1
                    except psycopg2.IntegrityError as e:
                        print(f"Skipping credential {row['id']}: {e}")
                        conn.rollback()
                        continue
        
        print(f"Imported {cred_count} server credentials")
        
        # Import site details for existing credentials
        print("Importing tableau_site_details...")
        cursor.execute("SELECT id FROM biport_dev.tableau_server_credentials")
        valid_credentials = {row[0] for row in cursor.fetchall()}
        
        with open('data/tableau_site_details.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            site_count = 0
            for row in reader:
                cred_id = safe_value(row['credentials_id'])
                if cred_id in valid_credentials:
                    try:
                        cursor.execute("""
                            INSERT INTO biport_dev.tableau_site_details 
                            (id, credentials_id, site_name, site_id, created_by, updated_by, 
                             created_at, updated_at, is_deleted)
                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """, (
                            safe_value(row['id']), cred_id, 
                            safe_value(row['site_name']), safe_value(row['site_id']),
                            safe_value(row['created_by']), safe_value(row['updated_by']),
                            safe_value(row['created_at']), safe_value(row['updated_at']), 
                            safe_value(row['is_deleted'], 'bool')
                        ))
                        site_count += 1
                    except psycopg2.IntegrityError as e:
                        print(f"Skipping site {row['id']}: {e}")
                        conn.rollback()
                        continue
        
        print(f"Imported {site_count} site details")
        
        # Now try to import projects with valid references
        print("Importing project_details (with valid references)...")
        cursor.execute("SELECT id FROM biport_dev.tableau_site_details")
        valid_sites = {row[0] for row in cursor.fetchall()}
        
        cursor.execute("SELECT id FROM biport_dev.tableau_server_details")
        valid_servers = {row[0] for row in cursor.fetchall()}
        
        with open('data/final_project_details.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            project_count = 0
            for row in reader:
                site_id = safe_value(row['site_id'])
                server_id = safe_value(row['server_id'])
                user_id = safe_value(row['user_id'])
                assigned_to = safe_value(row['assigned_to'])
                
                # Check if all required references exist
                valid_refs = True
                if site_id and site_id not in valid_sites:
                    valid_refs = False
                if server_id and server_id not in valid_servers:
                    valid_refs = False
                if user_id and user_id not in valid_users:
                    valid_refs = False
                if assigned_to and assigned_to not in valid_users:
                    valid_refs = False
                
                if valid_refs:
                    try:
                        cursor.execute("""
                            INSERT INTO biport_dev.project_details 
                            (id, name, is_upload, site_id, server_id, parent_id, user_id, assigned_to,
                             created_at, updated_at, is_deleted)
                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """, (
                            safe_value(row['id']), safe_value(row['name']), 
                            safe_value(row['is_upload'], 'bool'),
                            site_id, server_id, safe_value(row['parent_id']), 
                            user_id, assigned_to, safe_value(row['created_at']), 
                            safe_value(row['updated_at']), safe_value(row['is_deleted'], 'bool')
                        ))
                        project_count += 1
                    except psycopg2.IntegrityError as e:
                        print(f"Skipping project {row['id']}: {e}")
                        conn.rollback()
                        continue
        
        print(f"Imported {project_count} projects")
        
        conn.commit()
        print("All data import fixes completed successfully.")
        return True
        
    except Exception as e:
        print(f"Error fixing imports: {e}")
        conn.rollback()
        return False
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    if fix_tableau_data():
        print("Data import fixes completed successfully!")
    else:
        print("Data import fixes failed!")
        sys.exit(1)
