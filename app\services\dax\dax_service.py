import os
import xml.etree.ElementTree as ET
import shutil
from typing import List


from app.schemas import DaxConversionRequest
from app.core.config import S3Config
from app.core import ClOUD_WORKBOOKS_PATH, logger
from app.core.exceptions import BadRe<PERSON>Error, ServerError
from app.services import extract_json, get_tables_data
from app.services.analysis import calculate_fields
from app.models_old.reports import ReportDetailsManager
from app.models_old.upload_file_report_details import UploadFilesReportManager
from .convert import get_openai_response, generate_pdf


class DaxService:
    async def dax_formulae_converstion(self, request: DaxConversionRequest) -> List[dict]:
        s3 = S3Config()
        response_data = []

        for workbook in request.twb_files:
            local_base_path = None
            try:
                file_key = workbook.s3_path if request.is_upload_file else workbook.workbook_id
                workbook_name = workbook.workbook_name

                if request.is_upload_file:
                    report_handler = UploadFilesReportManager
                    record = report_handler.get_by_s3_path(file_key)
                    base_name = os.path.splitext(os.path.basename(file_key))[0]
                    pdf_filename = f"{base_name}.pdf"
                    s3_pdf_key = f"{os.path.dirname(file_key)}/converted_files/{pdf_filename}"
                    s3_key = file_key
                    local_base_path = os.path.join("storage", os.path.dirname(file_key))

                else:
                    report_handler = ReportDetailsManager
                    record = report_handler.get_workbook_id(file_key)
                    prefix = file_key.split('-')[0]
                    pdf_filename = f"{workbook_name}_dax_{prefix}.pdf"
                    s3_pdf_key = f"{ClOUD_WORKBOOKS_PATH}converted_files/{pdf_filename}"
                    s3_key = f"{ClOUD_WORKBOOKS_PATH}{file_key}.twb"
                    local_base_path = os.path.join("storage", "converted_files", file_key)


                local_input_path = os.path.join(local_base_path, "input_files")
                local_pdf_path = os.path.join(local_base_path, "pdf")

                os.makedirs(local_input_path, exist_ok=True)
                os.makedirs(local_pdf_path, exist_ok=True)

                if record and record.is_converted:
                    url = await s3.generate_presigned_url(s3_pdf_key)
                    response_data.append({
                        "s3_path" if request.is_upload_file else "workbook_id": file_key,
                        "status": "Success",
                        "data": {"file": pdf_filename, "download_url": url}
                    })
                    continue

                try:
                    local_paths = await s3.download_twb_file_from_s3(s3_key, local_input_path)
                except Exception as e:
                    logger.exception(f"Failed to download S3 file: {file_key}")
                    raise BadRequestError(detail=f"S3 download failed: {str(e)}")

                for twb_file in local_paths:
                    try:
                        logger.info(f"Converting file: {twb_file}")
                        data = extract_json(twb_file)
                        table_data = get_tables_data(data)
                        tree = ET.parse(twb_file)
                        root = tree.getroot()
                        calculated_fields = calculate_fields(root)

                        result_payload = {}
                        field_results = []

                        for field in calculated_fields:
                            if 'Parameter' in field.get("name", ""):
                                continue
                            caption = field.get("caption", "")
                            formula = field.get("formula", "")
                            tableau_formula = f"{caption} = {formula}"
                            dax_formula = get_openai_response(tableau_formula, table_data)
                            field_results.append({
                                'cal_field': tableau_formula,
                                'powerbi_dax': dax_formula
                            })

                        result_payload[workbook_name] = field_results

                        pdf_path = os.path.join(local_pdf_path, pdf_filename)
                        generate_pdf({"dax": result_payload}, pdf_path)

                        await s3.upload_to_s3(pdf_path, s3_pdf_key)
                        report_handler.mark_converted(file_key)

                        presigned_url = await s3.generate_presigned_url(s3_pdf_key)

                        response_data.append({
                            "s3_path" if request.is_upload_file else "workbook_id": file_key,
                            "status": "Success",
                            "data": {"file": pdf_filename, "download_url": presigned_url}
                        })

                    except Exception as file_err:
                        logger.exception(f"File processing error: {twb_file}")
                        raise ServerError(detail=f"Failed processing file: {twb_file}")

            except Exception as e:
                logger.exception(f"Conversion failed for: {file_key}")
                raise ServerError(detail=f"DAX conversion failed for {file_key}: {str(e)}")

            finally:
                if local_base_path and os.path.exists(local_base_path):
                    shutil.rmtree(local_base_path, ignore_errors=True)

        return response_data
