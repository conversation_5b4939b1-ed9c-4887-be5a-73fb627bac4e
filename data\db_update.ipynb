{"cells": [{"cell_type": "code", "execution_count": 4, "id": "0215d326", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from sqlalchemy import create_engine\n", "from datetime import datetime\n", "import uuid\n"]}, {"cell_type": "code", "execution_count": 5, "id": "ec2d7a4d", "metadata": {}, "outputs": [], "source": ["# import pandas as pd\n", "# import uuid\n", "# from datetime import datetime\n", "# from sqlalchemy import create_engine\n", "# from sqlalchemy.dialects.postgresql import insert\n", "\n", "# # Load CSV files\n", "# credentials_df = pd.read_csv(\"tableau_server_credentials.csv\")\n", "# server_df = pd.read_csv(\"tableau_server_details.csv\")\n", "\n", "# # Merge both on server_id\n", "# merged_df = credentials_df.merge(server_df, left_on=\"server_id\", right_on=\"id\", suffixes=('_cred', '_server'))\n", "\n", "# Create SQLAlchemy engine (update with your DB credentials)\n", "engine = create_engine(\"postgresql+psycopg2://dev_tab_to_pbi:<EMAIL>:5432/dev_tab_to_pbi\")\n", "\n", "# # Prepare rows for insert\n", "# site_details_rows = []\n", "# for _, row in merged_df.iterrows():\n", "#     site_details_rows.append({\n", "#         \"id\": str(uuid.uuid4()),\n", "#         \"credentials_id\": row[\"id_cred\"],  # credentials table id\n", "#         \"site_name\": row[\"site_name\"],\n", "#         \"site_id\": row[\"site_id\"],\n", "#         \"created_by\": row[\"created_by\"],\n", "#         \"updated_by\": row[\"updated_by\"],\n", "#         \"created_at\": datetime.now(),\n", "#         \"updated_at\": datetime.now(),\n", "#         \"is_deleted\": False\n", "#     })\n", "\n", "# # Define the table name\n", "# TABLE_NAME = \"biport_dev.tableau_site_details\"\n", "\n", "# # Insert into DB\n", "# with engine.begin() as conn:\n", "#     for row in site_details_rows:\n", "#         stmt = insert(\n", "#             table=TABLE_NAME\n", "#         ).values(**row)\n", "#         conn.execute(stmt)\n", "\n", "# print(f\"{len(site_details_rows)} records inserted into tableau_site_details.\")\n"]}, {"cell_type": "code", "execution_count": 14, "id": "a994bd4e", "metadata": {}, "outputs": [], "source": ["# Load your cleaned project details\n", "project_df = pd.read_csv(\"cleaned_project_details.csv\")\n", "\n", "# Replace all server_id values with your desired one\n", "new_server_id = \"19317b56-971e-4eef-aadc-0b7d9b11f20c\"\n", "project_df[\"server_id\"] = new_server_id\n", "\n", "# Also fill in created_at and updated_at if needed\n", "now = datetime.now()\n", "project_df[\"created_at\"] = now\n", "project_df[\"updated_at\"] = now\n", "\n", "# Optional: Fill other NOT NULL columns if they exist in your model\n", "project_df[\"is_deleted\"] = False  # if applicable"]}, {"cell_type": "code", "execution_count": 18, "id": "e46f26b8", "metadata": {}, "outputs": [], "source": ["project_df[\"site_id\"] = \"c4b2b37c-e59d-49b3-a41d-2fc2cf99cc2f\"  # your only site\n", "project_df[\"server_id\"] = \"19317b56-971e-4eef-aadc-0b7d9b11f20c\"  # your only server\n", "\n", "project_df.to_csv(\"updated_project_details.csv\", index=False)\n"]}, {"cell_type": "code", "execution_count": 37, "id": "82bbad96", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Data inserted successfully into biport_dev.project_details\n"]}], "source": ["\n", "project_df1 = pd.read_csv(\"final_project_details.csv\")\n", "\n", "# Also fill in created_at and updated_at if needed\n", "now = datetime.now()\n", "project_df1[\"created_at\"] = now\n", "project_df1[\"updated_at\"] = now\n", "\n", "# Create SQLAlchemy engine (update with your DB credentials)\n", "engine = create_engine(\"postgresql+psycopg2://dev_tab_to_pbi:<EMAIL>:5432/dev_tab_to_pbi\")\n", "\n", "project_df1.to_sql(\n", "    \"project_details\",\n", "    con=engine,\n", "    schema=\"biport_dev\",\n", "    if_exists=\"append\",\n", "    index=False\n", ")\n", "\n", "print(\"✅ Data inserted successfully into biport_dev.project_details\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "abbfea67", "metadata": {}, "outputs": [], "source": ["\n", "\n", "\n", "# Load the files\n", "project_df = pd.read_csv(\"cleaned_project_details.csv\")\n", "server_df = pd.read_csv(\"tableau_server_details.csv\")\n", "\n", "# Choose a valid server_id (first one)\n", "valid_server_id = server_df.loc[0, \"id\"]\n", "project_df[\"server_id\"] = valid_server_id\n", "\n", "# Create a dummy site_id\n", "dummy_site_id = str(uuid.uuid4())\n", "project_df[\"site_id\"] = dummy_site_id\n", "\n", "# Save updated CSV\n", "project_df.to_csv(\"final_project_details.csv\", index=False)\n", "\n", "# Also optionally create tableau_site_details.csv with this dummy ID\n", "site_df = pd.DataFrame([{\"id\": dummy_site_id, \"name\": \"Default Site\"}])\n", "site_df.to_csv(\"tableau_site_details.csv\", index=False)\n"]}, {"cell_type": "code", "execution_count": null, "id": "2c370b76", "metadata": {}, "outputs": [], "source": ["project_df = pd.read_csv(\"cleaned_project_details.csv\")\n", "server_df = pd.read_csv(\"tableau_server_details.csv\")\n", "site_df = pd.read_csv(\"tableau_site_details.csv\")  # Make sure this file is available\n", "\n", "# Choose a valid server_id (first one)\n", "valid_server_id = server_df.loc[0, \"id\"]\n", "project_df[\"server_id\"] = valid_server_id\n", "\n", "# Choose a valid site_id (first one)\n", "valid_site_id = site_df.loc[0, \"id\"]\n", "project_df[\"site_id\"] = valid_site_id\n", "\n", "# Save updated CSV\n", "project_df.to_csv(\"final_project_details.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "bfe0f598", "metadata": {}, "outputs": [], "source": ["\n", "# 🔐 Replace with your actual PostgreSQL connection URI\n", "# Format: postgresql+psycopg2://username:password@your_domain:port/database_name\n", "\n", "DATABASE_URI = \"postgresql+psycopg2://dev_tab_to_pbi:<EMAIL>:5432/dev_tab_to_pbi\"\n", "\n", "# 🔄 CSV file path (adjust this to your file)\n", "csv_path = r\"D:\\SPARITY_INTERNAL\\final_project_details.csv\"\n", "\n", "# 🔍 Target table and schema\n", "table_name = \"project_details\"\n", "schema_name = \"biport_dev\"\n", "\n", "# 🔁 Load the CSV\n", "df = pd.read_csv(csv_path)\n", "\n", "df[\"is_deleted\"] = False  # This will be `false` when saved as CSV\n", "# Add created_at and updated_at timestamps\n", "now = datetime.now()\n", "df[\"created_at\"] = now\n", "df[\"updated_at\"] = now\n"]}, {"cell_type": "code", "execution_count": null, "id": "9a162d64", "metadata": {}, "outputs": [], "source": ["df.head(1)"]}, {"cell_type": "code", "execution_count": null, "id": "c132b5c9", "metadata": {}, "outputs": [], "source": ["# ✅ Connect and push to DB\n", "engine = create_engine(DATABASE_URI)\n", "df.to_sql(table_name, con=engine, schema=schema_name, if_exists='append', index=False)\n", "\n", "print(f\"✅ Successfully inserted data into {schema_name}.{table_name}\")\n"]}, {"cell_type": "code", "execution_count": 1, "id": "f7182ac6", "metadata": {}, "outputs": [], "source": ["# If not already installed, uncomment the next line:\n", "\n", "import psycopg2\n", "import pandas as pd\n", "from datetime import datetime\n", "import uuid\n"]}, {"cell_type": "code", "execution_count": 2, "id": "aab06d25", "metadata": {}, "outputs": [], "source": ["# Replace these with your actual DB credentials\n", "conn = psycopg2.connect(\n", "    host=\"localhost\",\n", "    port=\"5432\",\n", "    dbname=\"test_api\",\n", "    user=\"postgres\",\n", "    password=\"postgres\"\n", ")\n", "cursor = conn.cursor()\n"]}, {"cell_type": "code", "execution_count": 7, "id": "879f2353", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Roles columns:\n", " id             object\n", "name           object\n", "created_by    float64\n", "updated_by    float64\n", "created_at     object\n", "updated_at     object\n", "is_deleted       bool\n", "dtype: object\n", "\n", "Users columns:\n", " id                  object\n", "name                object\n", "email               object\n", "password_hash       object\n", "phone_number         int64\n", "organization_id     object\n", "role_id             object\n", "manager_id          object\n", "created_by         float64\n", "updated_by         float64\n", "created_at          object\n", "updated_at          object\n", "is_deleted            bool\n", "dtype: object\n", "\n", "Project Details columns:\n", " id             object\n", "name           object\n", "is_upload        bool\n", "site_id        object\n", "server_id      object\n", "parent_id      object\n", "user_id        object\n", "assigned_to    object\n", "dtype: object\n", "\n", "Report Details columns:\n", " id                    object\n", "name                  object\n", "report_id             object\n", "project_id            object\n", "is_analyzed             bool\n", "analyzed_status       object\n", "is_converted            bool\n", "converted_status      object\n", "is_migrated             bool\n", "migrated_status       object\n", "unit_tested             bool\n", "uat_tested              bool\n", "deployed                bool\n", "is_scoped               bool\n", "semantic_type         object\n", "has_semantic_model      bool\n", "view_count             int64\n", "dtype: object\n"]}], "source": ["import pandas as pd\n", "\n", "# Load CSV files\n", "roles_df = pd.read_csv(\"roles.csv\")\n", "users_df = pd.read_csv(\"users.csv\")\n", "projects_df = pd.read_csv(\"project_details.csv\")\n", "reports_df = pd.read_csv(\"report_details.csv\")\n", "\n", "# Print inferred column types\n", "print(\"Roles columns:\\n\", roles_df.dtypes)\n", "print(\"\\nUsers columns:\\n\", users_df.dtypes)\n", "print(\"\\nProject Details columns:\\n\", projects_df.dtypes)\n", "print(\"\\nReport Details columns:\\n\", reports_df.dtypes)\n"]}, {"cell_type": "code", "execution_count": 8, "id": "c980f01c", "metadata": {}, "outputs": [], "source": ["cursor.execute(\"\"\"\n", "CREATE TABLE IF NOT EXISTS roles (\n", "    id UUID PRIMARY KEY,\n", "    name TEXT,\n", "    created_by UUID,\n", "    updated_by UUID,\n", "    created_at TIMESTAMP,\n", "    updated_at TIMESTAMP,\n", "    is_deleted BOOLEAN\n", ");\n", "\"\"\")\n", "conn.commit()\n"]}, {"cell_type": "code", "execution_count": 9, "id": "40e829ba", "metadata": {}, "outputs": [], "source": ["cursor.execute(\"\"\"\n", "CREATE TABLE IF NOT EXISTS users (\n", "    id UUID PRIMARY KEY,\n", "    name TEXT,\n", "    email TEXT,\n", "    password_hash TEXT,\n", "    phone_number BIGINT,\n", "    organization_id UUID,\n", "    role_id UUID REFERENCES roles(id),\n", "    manager_id UUID,\n", "    created_by UUID,\n", "    updated_by UUID,\n", "    created_at TIMESTAMP,\n", "    updated_at TIMESTAMP,\n", "    is_deleted BOOLEAN\n", ");\n", "\"\"\")\n", "conn.commit()\n"]}, {"cell_type": "code", "execution_count": 10, "id": "a71b888e", "metadata": {}, "outputs": [], "source": ["cursor.execute(\"\"\"\n", "CREATE TABLE IF NOT EXISTS project_details (\n", "    id UUID PRIMARY KEY,\n", "    name TEXT,\n", "    is_upload BOOLEAN,\n", "    site_id UUID,\n", "    server_id UUID,\n", "    parent_id UUID,\n", "    user_id UUID REFERENCES users(id),\n", "    assigned_to UUID REFERENCES users(id)\n", ");\n", "\"\"\")\n", "conn.commit()\n"]}, {"cell_type": "code", "execution_count": 11, "id": "36080fd7", "metadata": {}, "outputs": [], "source": ["cursor.execute(\"\"\"\n", "CREATE TABLE IF NOT EXISTS report_details (\n", "    id UUID PRIMARY KEY,\n", "    name TEXT,\n", "    report_id UUID,\n", "    project_id UUID REFERENCES project_details(id),\n", "    is_analyzed BOOLEAN,\n", "    analyzed_status TEXT,\n", "    is_converted BOOLEAN,\n", "    converted_status TEXT,\n", "    is_migrated BOOLEAN,\n", "    migrated_status TEXT,\n", "    unit_tested BOOLEAN,\n", "    uat_tested BOOLEAN,\n", "    deployed BOOLEAN,\n", "    is_scoped BOOLEAN,\n", "    semantic_type TEXT,\n", "    has_semantic_model BOOLEAN,\n", "    view_count INTEGER\n", ");\n", "\"\"\")\n", "conn.commit()\n"]}, {"cell_type": "code", "execution_count": 12, "id": "feadd48d", "metadata": {}, "outputs": [], "source": ["roles_df = pd.read_csv(\"roles.csv\")\n", "users_df = pd.read_csv(\"users.csv\")\n", "projects_df = pd.read_csv(\"project_details.csv\")\n", "reports_df = pd.read_csv(\"report_details.csv\")\n"]}, {"cell_type": "code", "execution_count": 18, "id": "3921ec46", "metadata": {}, "outputs": [], "source": ["conn.rollback()"]}, {"cell_type": "code", "execution_count": null, "id": "c0d3c325", "metadata": {}, "outputs": [], "source": ["\n"]}, {"cell_type": "code", "execution_count": 20, "id": "5f25f523", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Failed to insert role row: {'id': '6320cbdc-e00c-498e-bd6b-fca286bdd9b2', 'name': 'MANAGER', 'created_by': nan, 'updated_by': nan, 'created_at': '2025-07-02 06:10:33.990799', 'updated_at': '2025-07-02 06:10:33.990799', 'is_deleted': False}\n", "Error: current transaction is aborted, commands ignored until end of transaction block\n", "\n"]}, {"ename": "ForeignKeyViolation", "evalue": "insert or update on table \"users\" violates foreign key constraint \"users_role_id_fkey\"\nDETAIL:  Key (role_id)=(6320cbdc-e00c-498e-bd6b-fca286bdd9b2) is not present in table \"roles\".\n", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON>ignKeyViolation\u001b[39m                       <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[20]\u001b[39m\u001b[32m, line 19\u001b[39m\n\u001b[32m     17\u001b[39m \u001b[38;5;66;03m# Insert users\u001b[39;00m\n\u001b[32m     18\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m _, row \u001b[38;5;129;01min\u001b[39;00m users_df.iterrows():\n\u001b[32m---> \u001b[39m\u001b[32m19\u001b[39m     \u001b[43mcursor\u001b[49m\u001b[43m.\u001b[49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\"\"\u001b[39;49m\n\u001b[32m     20\u001b[39m \u001b[33;43m        INSERT INTO users (id, name, email, password_hash, phone_number, organization_id,\u001b[39;49m\n\u001b[32m     21\u001b[39m \u001b[33;43m                           role_id, manager_id, created_by, updated_by, created_at, updated_at, is_deleted)\u001b[39;49m\n\u001b[32m     22\u001b[39m \u001b[33;43m        VALUES (\u001b[39;49m\u001b[38;5;132;43;01m%s\u001b[39;49;00m\u001b[33;43m, \u001b[39;49m\u001b[38;5;132;43;01m%s\u001b[39;49;00m\u001b[33;43m, \u001b[39;49m\u001b[38;5;132;43;01m%s\u001b[39;49;00m\u001b[33;43m, \u001b[39;49m\u001b[38;5;132;43;01m%s\u001b[39;49;00m\u001b[33;43m, \u001b[39;49m\u001b[38;5;132;43;01m%s\u001b[39;49;00m\u001b[33;43m, \u001b[39;49m\u001b[38;5;132;43;01m%s\u001b[39;49;00m\u001b[33;43m, \u001b[39;49m\u001b[38;5;132;43;01m%s\u001b[39;49;00m\u001b[33;43m, \u001b[39;49m\u001b[38;5;132;43;01m%s\u001b[39;49;00m\u001b[33;43m, \u001b[39;49m\u001b[38;5;132;43;01m%s\u001b[39;49;00m\u001b[33;43m, \u001b[39;49m\u001b[38;5;132;43;01m%s\u001b[39;49;00m\u001b[33;43m, \u001b[39;49m\u001b[38;5;132;43;01m%s\u001b[39;49;00m\u001b[33;43m, \u001b[39;49m\u001b[38;5;132;43;01m%s\u001b[39;49;00m\u001b[33;43m, \u001b[39;49m\u001b[38;5;132;43;01m%s\u001b[39;49;00m\u001b[33;43m)\u001b[39;49m\n\u001b[32m     23\u001b[39m \u001b[33;43m    \u001b[39;49m\u001b[33;43m\"\"\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msafe_tuple\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrow\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     25\u001b[39m \u001b[38;5;66;03m# Insert project_details\u001b[39;00m\n\u001b[32m     26\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m _, row \u001b[38;5;129;01min\u001b[39;00m projects_df.iterrows():\n", "\u001b[31mForeignKeyViolation\u001b[39m: insert or update on table \"users\" violates foreign key constraint \"users_role_id_fkey\"\nDETAIL:  Key (role_id)=(6320cbdc-e00c-498e-bd6b-fca286bdd9b2) is not present in table \"roles\".\n"]}], "source": ["def safe_tuple(row):\n", "    return tuple(None if pd.isna(x) else x for x in row)\n", "\n", "for _, row in roles_df.iterrows():\n", "    try:\n", "        cursor.execute(\"\"\"\n", "            INSERT INTO roles (id, name, created_by, updated_by, created_at, updated_at, is_deleted)\n", "            VALUES (%s, %s, %s, %s, %s, %s, %s)\n", "        \"\"\", safe_tuple(row))\n", "    except Exception as e:\n", "        print(\"Failed to insert role row:\", row.to_dict())\n", "        print(\"Error:\", e)\n", "        conn.rollback()\n", "\n", "\n", "\n", "# Insert users\n", "for _, row in users_df.iterrows():\n", "    cursor.execute(\"\"\"\n", "        INSERT INTO users (id, name, email, password_hash, phone_number, organization_id,\n", "                           role_id, manager_id, created_by, updated_by, created_at, updated_at, is_deleted)\n", "        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)\n", "    \"\"\", safe_tuple(row))\n", "\n", "# Insert project_details\n", "for _, row in projects_df.iterrows():\n", "    cursor.execute(\"\"\"\n", "        INSERT INTO project_details (id, name, is_upload, site_id, server_id, parent_id, user_id, assigned_to)\n", "        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)\n", "    \"\"\", safe_tuple(row))\n", "\n", "# Insert report_details\n", "for _, row in reports_df.iterrows():\n", "    cursor.execute(\"\"\"\n", "        INSERT INTO report_details (\n", "            id, name, report_id, project_id, is_analyzed, analyzed_status,\n", "            is_converted, converted_status, is_migrated, migrated_status,\n", "            unit_tested, uat_tested, deployed, is_scoped, semantic_type,\n", "            has_semantic_model, view_count\n", "        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)\n", "    \"\"\", safe_tuple(row))\n", "\n", "conn.commit()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}