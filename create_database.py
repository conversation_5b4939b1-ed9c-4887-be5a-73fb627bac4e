#!/usr/bin/env python3
"""
Scrip<PERSON> to create the test database in local PostgreSQL
"""
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import sys

def create_database():
    try:
        # Connect to PostgreSQL server (default database)
        conn = psycopg2.connect(
            host="localhost",
            port="5432",
            user="postgres",
            password="postgres",
            database="postgres"  # Connect to default postgres database
        )
        
        # Set autocommit mode for database creation
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        
        cursor = conn.cursor()
        
        # Check if database already exists
        cursor.execute("SELECT 1 FROM pg_catalog.pg_database WHERE datname = 'test'")
        exists = cursor.fetchone()
        
        if exists:
            print("Database 'test' already exists.")
        else:
            # Create the database
            cursor.execute("CREATE DATABASE test")
            print("Database 'test' created successfully.")
        
        cursor.close()
        conn.close()
        
        return True
        
    except psycopg2.Error as e:
        print(f"Error creating database: {e}")
        return False
    except Exception as e:
        print(f"Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = create_database()
    sys.exit(0 if success else 1)
