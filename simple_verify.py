#!/usr/bin/env python3
"""
Simple verification of project and report data
"""
import psycopg2

# Database connection parameters
DB_CONFIG = {
    'host': 'localhost',
    'port': '5432',
    'user': 'postgres',
    'password': 'postgres',
    'database': 'test'
}

def verify_data():
    """Simple verification"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # Count records
        cursor.execute("SELECT COUNT(*) FROM biport_dev.project_details")
        project_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM biport_dev.report_details")
        report_count = cursor.fetchone()[0]
        
        print(f"✅ Project Details: {project_count} records")
        print(f"✅ Report Details: {report_count} records")
        
        # Show sample project data
        cursor.execute("SELECT id, name, is_upload FROM biport_dev.project_details LIMIT 3")
        projects = cursor.fetchall()
        print(f"\nSample Projects:")
        for project in projects:
            print(f"  - {project[1]} (Upload: {project[2]})")
        
        # Show sample report data
        cursor.execute("SELECT id, name, semantic_type, view_count FROM biport_dev.report_details LIMIT 3")
        reports = cursor.fetchall()
        print(f"\nSample Reports:")
        for report in reports:
            print(f"  - {report[1]} ({report[2]}) - Views: {report[3]}")
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Data import successful!")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    verify_data()
