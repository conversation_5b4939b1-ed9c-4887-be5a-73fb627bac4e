from .process_tables import process_table_columns
from app.core.exceptions import NotFoundError
from app.core.config import S3Config
import os
import shutil
from app.core.enums import PowerBITemplateKeys
from app.core.constants import LOCAL_DATE_TEMPLATE
import uuid

s3_config = S3Config()

async def get_tables_folder_path(pbi_env_file):
    pbi_dir_list = os.listdir(pbi_env_file)
    powerbi_semantic_folder = [path for path in pbi_dir_list if '.SemanticModel' in path]
    
    if not powerbi_semantic_folder:
        raise NotFoundError("No '.SemanticModel' folder found in the given directory.")
    
    definition_folder = os.path.join(pbi_env_file, powerbi_semantic_folder[0], 'definition')
    tables_folder_path = os.path.join(definition_folder, 'tables')

    # Remove the existing folder if it exists
    if os.path.exists(tables_folder_path):
        shutil.rmtree(tables_folder_path)

    # Create a fresh tables folder
    os.makedirs(tables_folder_path, exist_ok=True)

    return tables_folder_path
        

async def create_tmdl_file(table_column_data, date_columns_data, tables_folder_path):
    for datasource_name, tables_data in table_column_data.items():
        for table_name, table_info in tables_data.items():
            file_path = os.path.join(tables_folder_path, f"{table_name}.tmdl")
            column_data = table_info.get(PowerBITemplateKeys.COLUMN_DATA.value, [])

            with open(file_path, 'w') as tmdl_file:
                tmdl_file.write(f"table {table_name}\n")
                tmdl_file.write(f"\tlineageTag: {table_info.get(PowerBITemplateKeys.LINEAGE_TAG.value)}\n\n")
                for column in column_data:
                    tmdl_file.write(f"\tcolumn {column.get(PowerBITemplateKeys.COLUMN_NAME.value)}\n")
                    tmdl_file.write(f"\t\tdataType: {column.get(PowerBITemplateKeys.DATA_TYPE.value)}\n")
                    tmdl_file.write(f"\t\tformatString: {column.get(PowerBITemplateKeys.FORMAT_STRING.value)}\n")
                    tmdl_file.write(f"\t\tlineageTag: {column.get(PowerBITemplateKeys.LINEAGE_TAG.value)}\n")
                    tmdl_file.write(f"\t\tsummarizeBy: {column.get(PowerBITemplateKeys.SUMMARIZE_BY.value)}\n")
                    tmdl_file.write(f"\t\tsourceColumn: {column.get(PowerBITemplateKeys.SOURCE_COLUMN.value)}\n\n")
                    tmdl_file.write("\t\tannotation SummarizationSetBy = Automatic\n\n")
                tmdl_file.write("\n")
    if date_columns_data:
        for date_column in date_columns_data:
            column_name = date_column.get("column_name")
            local_date_table_ref = date_column.get("local_date_table_ref")
            table_name = date_column.get("table_name")
            date_table_path = os.path.join(tables_folder_path, f"{local_date_table_ref}.tmdl")
            with open(date_table_path, 'w') as date_file:
                date_file.write(LOCAL_DATE_TEMPLATE.format(
                    column_name = column_name,
                    local_date_table_ref = local_date_table_ref,
                    table_name = table_name,
                    table_lineage_tag = str(uuid.uuid4()),
                    date_lineage_tag = str(uuid.uuid4()),
                    year_lineage_tag = str(uuid.uuid4()),
                    quarter_lineage_tag = str(uuid.uuid4()),
                    month_lineage_tag = str(uuid.uuid4()),
                    day_lineage_tag = str(uuid.uuid4()),
                    month_no_lineage_tag = str(uuid.uuid4()),
                    quarter_no_lineage_tag = str(uuid.uuid4()),
                    hierarchy_lineage_tag = str(uuid.uuid4()),
                    hierarchy_year_lineage_tag = str(uuid.uuid4()),
                    hierarchy_quarter_lineage_tag = str(uuid.uuid4()),
                    hierarchy_month_lineage_tag = str(uuid.uuid4()),
                    hierarchy_day_lineage_tag = str(uuid.uuid4()),

                ))

async def upload_pbi_to_s3(pbi_folder_path, unique_id):
    s3_key = f"semantic_model_test/{unique_id}"
    zip_file_path = shutil.make_archive(pbi_folder_path, 'zip', pbi_folder_path)
    zip_s3_key = f"{s3_key}/{os.path.basename(zip_file_path)}"
    await s3_config.upload_to_s3(zip_file_path, zip_s3_key)
    return {
        'file_name': os.path.basename(zip_file_path),
        'download_link': await s3_config.generate_presigned_url(zip_s3_key)
    }

async def create_semantic_model(twb_file_path, pbi_env_file):

    table_column_data, date_columns_data = await process_table_columns(twb_file_path)
    tables_folder_path = await get_tables_folder_path(pbi_env_file)
    await create_tmdl_file(table_column_data, date_columns_data, tables_folder_path)
    