from multiprocessing import process
import xml.etree.ElementTree as ET
from app.core.enums import TableauXMLTags, ChartType, VisualRequest
from app.services.migrate.visuals.areachart import process_area_chart_report
from app.services.migrate.visuals.line_chart import process_line_chart_report
from app.services.migrate.visuals.scatterplot import process_scatter_chart_report
from app.services.migrate.Tableau_Analyzer.report import (
    extract_worksheet_title, extract_style_data, extract_datasource_columns,
    extract_table_columns, get_fields_data
)


def process_worksheets(root, chart_types):
    worksheets = root.findall(TableauXMLTags.WORKSHEET.value)
    datasources = root.findall(TableauXMLTags.DATASOURCE.value)
    table_column_data = extract_table_columns(datasources)
    worksheet_visuals_data = []

    for worksheet in worksheets:
        worksheet_name = worksheet.get(TableauXMLTags.NAME.value)
        rows = worksheet.find(TableauXMLTags.ROWS.value).text
        cols = worksheet.find(TableauXMLTags.COLS.value).text
        filters = worksheet.find(TableauXMLTags.FILTER.value)
        panes = worksheet.findall(TableauXMLTags.PANE.value)
        style_data = worksheet.find(TableauXMLTags.STYLE.value)
        title_run_data = worksheet.findall(TableauXMLTags.TITLE_RUN.value)
        datasource_dependencies = worksheet.findall(TableauXMLTags.DATASOURCE_DEPENDENCIES.value)

        chart_types_data = {chart.get("name"): chart.get("chart_type") for chart in chart_types}
        visual_type = chart_types_data.get(worksheet_name)

        rows_list = get_fields_data(rows)
        cols_list = get_fields_data(cols)

        worksheet_title_data = extract_worksheet_title(title_run_data) if title_run_data else None
        worksheet_style_data = extract_style_data(style_data) if style_data else None
        datasource_columns = extract_datasource_columns(datasource_dependencies) if datasource_dependencies else None
        small_multiples = True if "*" in str(rows)+str(cols) else False

        visual_request = VisualRequest(
            rows = rows_list,
            cols = cols_list,
            panes = panes,
            visual_type=visual_type,
            worksheet_name = worksheet_name,
            table_column_data = table_column_data,
            datasource_columns = datasource_columns,
            worksheet_title_data = worksheet_title_data,
            worksheet_style_data = worksheet_style_data,
            small_multiples = small_multiples,
            filters=filters
        )

        if visual_type == ChartType.LINE.value:
            linechart_response = process_line_chart_report(visual_request)
            if linechart_response: worksheet_visuals_data.append(linechart_response)
        elif visual_type == ChartType.AREA.value:
            areachart_response = process_area_chart_report(visual_request)
            if areachart_response: worksheet_visuals_data.append(areachart_response)
        elif visual_type == ChartType.SCATTER.value:
            scatterchart_response = process_scatter_chart_report(visual_request)
            if scatterchart_response: worksheet_visuals_data.append(scatterchart_response)
    return worksheet_visuals_data