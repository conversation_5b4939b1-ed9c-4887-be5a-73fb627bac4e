#!/usr/bin/env python3
"""
Test the authentication dependency to isolate the issue
"""
import requests
import json

# API base URL
BASE_URL = "http://127.0.0.1:9090/app_api"

def test_auth_dependency():
    """Test if the authentication dependency is working"""
    print("🔐 Testing Authentication Dependency")
    print("=" * 50)
    
    # First login to get token
    login_url = f"{BASE_URL}/users/login"
    login_data = {
        "email": "<EMAIL>",
        "password": "Raghava@123"
    }
    
    response = requests.post(login_url, json=login_data)
    if response.status_code != 200:
        print(f"❌ Login failed: {response.text}")
        return
    
    login_response = response.json()
    access_token = login_response['data']['access_token']
    user_email = login_response['data']['user_email']
    
    print(f"✅ Login successful")
    print(f"Token length: {len(access_token)}")
    print(f"User email: {user_email}")
    
    # Test a simple authenticated endpoint first
    print(f"\n🧪 Testing simple authenticated endpoint...")
    
    # Let's try to create a simple test endpoint to check auth
    # For now, let's test the workspace endpoint with more debugging
    
    workspace_url = f"{BASE_URL}/workspace/reports"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "X-User-Email": user_email,
        "Content-Type": "application/json"
    }
    
    print(f"Making request to: {workspace_url}")
    print(f"Headers: {headers}")
    
    try:
        response = requests.get(workspace_url, headers=headers, timeout=10)
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        print(f"Response text: {response.text}")
        
        if response.status_code == 500:
            print(f"\n❌ Internal Server Error - this suggests the issue is in the server code")
            print(f"The authentication might be working, but there's an error in the workspace logic")
        
    except Exception as e:
        print(f"❌ Request failed: {e}")

if __name__ == "__main__":
    test_auth_dependency()
