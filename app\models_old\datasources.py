from datetime import datetime
from sqlalchemy import <PERSON>umn, DateTime, Integer, ForeignKey, update, String, UUID
from app.core import Base, scoped_context
from uuid import uuid4


class DataSourcesDetails(Base):
    """Database model for storing data source details for users."""

    __tablename__ = "data_sources"

    id = Column(UUID(as_uuid=True), primary_key=True, nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"))
    mssql = Column(String, nullable=True)
    postgresql = Column(String, nullable=True)
    oracle = Column(String, nullable=True)
    mysql = Column(String, nullable=True)
    sqlite = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class DataSourceDetailsManager:
    """Manager class for handling data source details operations."""

    @staticmethod
    def add_new_users_ds(record):
        """Adds new user data source details to the database."""
        record_id = uuid4()
        try:
            with scoped_context() as session:
                new_user_ds = DataSourcesDetails(
                    id=record_id,
                    user_id=record.user_id,
                    mssql=record.mssql,
                    postgresql=record.postgresql,
                    mysql=record.mysql,
                    oracle=record.oracle,
                    sqlite=record.sqlite,
                )
                session.add(new_user_ds)
                session.commit()
        except Exception as e:
            raise ValueError(f"Exception occurred while adding user data source: {e}")

    @staticmethod
    def update_data_source_details(record):
        """Updates the data source details in the database for a user."""
        with scoped_context() as session:
            stmt = (
                update(DataSourcesDetails)
                .where(DataSourcesDetails.user_id == record.user_id)
                .values(
                    mssql=record.mssql,
                    postgresql=record.postgresql,
                    mysql=record.mysql,
                    sqlite=record.sqlite,
                    oracle=record.oracle,
                )
            )
            session.execute(stmt)
            session.commit()

    @staticmethod
    def remove_user(request):
        """Removes all data source details for a given user."""
        record = DataSourceDetailsManager.get_user_data(request)
        if not record:
            raise ValueError("User not found.")
        with scoped_context() as session:
            session.query(DataSourcesDetails).filter(
                DataSourcesDetails.user_id == request.user_id
            ).delete()
            session.commit()

    @staticmethod
    def get_user_data(request):
        """Retrieves data source details for a specific user."""
        if not hasattr(request, "user_id") or request.user_id is None:
            raise ValueError("User ID is missing in the request.")
        try:
            with scoped_context() as session:
                record = session.query(DataSourcesDetails).filter_by(user_id=request.user_id).first()
                return record
        except Exception as e:
            raise ValueError(f"Error retrieving user data: {str(e)}")