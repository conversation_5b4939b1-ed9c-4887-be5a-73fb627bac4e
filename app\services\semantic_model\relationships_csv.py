import uuid
import re
from app.core.config import logger


def format_column_reference(column_full_specifier: str) -> str:
    logger.info(f"Raw column input: {column_full_specifier}")

    table_part, column_part = column_full_specifier.rsplit('.', 1)

    # Conditionally quote the table_part if it contains non-word characters
    if re.search(r"[^\w]", table_part):
        table_part = f"'{table_part}'"
    
    # The column_part is used as is, because extract_relationships_csv
    # has already ensured it's correctly quoted (e.g., "'Order Date'")
    # or unquoted (e.g., "Date") as needed.

    formatted = f"{table_part}.{column_part}"
    logger.info(f"Formatted column reference: {formatted}")

    return formatted


def extract_relationships_csv(root, unique_table_lineage_tags):
    relationships = []
    variant_relationships = {}

    for rel_tag in root.findall(".//relationship"):
        relationship_id = str(uuid.uuid4())
        expressions = rel_tag.findall(".//expression")

        from_column_op = (
            expressions[1].get("op").split("(")[0] if len(expressions) > 1 else None
        )
        to_column_op = (
            expressions[2].get("op").split("(")[0] if len(expressions) > 2 else None
        )

        first_end_point = rel_tag.find(".//first-end-point")
        second_end_point = rel_tag.find(".//second-end-point")

        from_table_obj_id = (
            first_end_point.get("object-id") if first_end_point is not None else None
        )
        to_table_obj_id = (
            second_end_point.get("object-id") if second_end_point is not None else None
        )

        from_table = from_table_obj_id.split("_")[0] if from_table_obj_id else None
        to_table = to_table_obj_id.split("_")[0] if to_table_obj_id else None

        if from_column_op and to_column_op and from_table and to_table:
            from_column_name = from_column_op.strip("[]").strip()
            to_column_name = to_column_op.strip("[]").strip()

            from_column_processed = (
                f"'{from_column_name}'"
                if " " in from_column_name
                else from_column_name
            )
            to_column_processed = (
                f"'{to_column_name}'" if " " in to_column_name else to_column_name
            )

            from_column_full = f"{from_table}.{from_column_processed}"
            to_column_full = f"{to_table}.{to_column_processed}"

            from_unique = first_end_point.get("unique-key") == "true"
            to_unique = second_end_point.get("unique-key") == "true"

            if from_unique and to_unique:
                from_col_final, to_col_final = from_column_full, to_column_full
                from_cardinality, to_cardinality = "one", "one"
            elif not from_unique and not to_unique:
                from_col_final, to_col_final = from_column_full, to_column_full
                from_cardinality, to_cardinality = "many", "many"
            elif from_unique and not to_unique:
                from_col_final, to_col_final = to_column_full, from_column_full
                from_cardinality, to_cardinality = "many", "one"
            elif not from_unique and to_unique:
                from_col_final, to_col_final = from_column_full, to_column_full
                from_cardinality, to_cardinality = "many", "one"
            else:
                from_col_final, to_col_final = from_column_full, to_column_full
                from_cardinality, to_cardinality = "unknown", "unknown"


            unique_table_lineage_tags[from_table] = unique_table_lineage_tags.get(
                from_table, str(uuid.uuid4())
            )
            unique_table_lineage_tags[to_table] = unique_table_lineage_tags.get(
                to_table, str(uuid.uuid4())
            )

            relationship_data = {
                "id": relationship_id,
                "crossFilteringBehavior": "bothDirections",
                "fromCardinality": from_cardinality,
                "toCardinality": to_cardinality,
                "fromColumn": from_col_final,
                "toColumn": to_col_final,
            }
            relationships.append(relationship_data)
    return relationships, variant_relationships


def write_relationships_file_csv(relationships, relationships_file_path):
    logger.info(f"Writing relationships file to: {relationships_file_path}")

    with open(relationships_file_path, "w", encoding="utf-8") as rel_file:
        for rel in relationships:
            logger.info(f"Processing relationship: {rel['id']}")

            rel_file.write(f"relationship {rel['id']}\n")

            if "annotation" in rel:
                rel_file.write(f"\t{rel['annotation']}\n")
            if "joinOnDateBehavior" in rel:
                 rel_file.write(f"\tjoinOnDateBehavior: {rel['joinOnDateBehavior']}\n")


            if "crossFilteringBehavior" in rel:
                rel_file.write(
                    f"\tcrossFilteringBehavior: {rel['crossFilteringBehavior']}\n"
                )
            if "fromCardinality" in rel:
                rel_file.write(f"\tfromCardinality: {rel['fromCardinality']}\n")
            if "toCardinality" in rel:
                rel_file.write(f"\ttoCardinality: {rel['toCardinality']}\n")
            
            if rel.get("isActive") is False:
                rel_file.write(f"\tisActive: false\n")

            from_col_formatted = format_column_reference(rel["fromColumn"])
            to_col_formatted = format_column_reference(rel["toColumn"])

            rel_file.write(f"\tfromColumn: {from_col_formatted}\n")
            rel_file.write(f"\ttoColumn: {to_col_formatted}\n")
            rel_file.write("\n")

        logger.info("Completed writing all relationship definitions")


def write_relationships_tmdl_generic(
    relationships_data_list: list,
    output_relationships_file_path: str,
    model_type_description: str,
) -> None:
    log_prefix = (
        f" - {model_type_description} Relationships TMDL - "
    )
    logger.info(f"{log_prefix}Writing relationships to: {output_relationships_file_path}")

    try:
        with open(output_relationships_file_path, "w", encoding="utf-8") as rel_tmdl_file:
            if not relationships_data_list:
                logger.info(
                    f"{log_prefix}No relationships provided. Writing empty relationships file."
                )
                rel_tmdl_file.write(
                    f"// No relationships defined for this {model_type_description} model.\n"
                )
                return

            for rel_definition in relationships_data_list:
                rel_id = rel_definition.get("id")
                if not rel_id:
                    logger.warning(
                        f"{log_prefix}Skipping relationship due to missing 'id'. Data: {rel_definition}"
                    )
                    continue

                logger.debug(f"{log_prefix}Processing relationship: {rel_id}")
                rel_tmdl_file.write(f"relationship {rel_id}\n")

                if "joinOnDateBehavior" in rel_definition:
                    rel_tmdl_file.write(
                        f"\tjoinOnDateBehavior: {rel_definition['joinOnDateBehavior']}\n"
                    )
                elif "annotation" in rel_definition:
                    logger.warning(
                        f"{log_prefix}Relationship '{rel_id}' has generic 'annotation': {rel_definition['annotation']}. "
                        f"Consider specific TMDL properties."
                    )
                    rel_tmdl_file.write(f"\t{rel_definition['annotation']}\n")

                if "crossFilteringBehavior" in rel_definition:
                    rel_tmdl_file.write(
                        f"\tcrossFilteringBehavior: {rel_definition['crossFilteringBehavior']}\n"
                    )
                if "fromCardinality" in rel_definition:
                    rel_tmdl_file.write(
                        f"\tfromCardinality: {rel_definition['fromCardinality']}\n"
                    )
                if "toCardinality" in rel_definition:
                    rel_tmdl_file.write(
                        f"\ttoCardinality: {rel_definition['toCardinality']}\n"
                    )

                if rel_definition.get("isActive") is False:
                    rel_tmdl_file.write(f"\tisActive: false\n")

                from_col_raw = rel_definition.get("fromColumn")
                to_col_raw = rel_definition.get("toColumn")

                if not from_col_raw or not to_col_raw:
                    logger.warning(
                        f"{log_prefix}Skipping relationship '{rel_id}' due to missing 'fromColumn' or 'toColumn'."
                    )
                    continue

                formatted_from_column = format_column_reference(from_col_raw)
                formatted_to_column = format_column_reference(to_col_raw)

                rel_tmdl_file.write(f"\tfromColumn: {formatted_from_column}\n")
                rel_tmdl_file.write(f"\ttoColumn: {formatted_to_column}\n")
                rel_tmdl_file.write("\n")

            logger.info(
                f"{log_prefix}Successfully completed writing {len(relationships_data_list)} relationship definition(s)."
            )

    except IOError as e:
        logger.error(
            f"{log_prefix}IOError writing relationships file to {output_relationships_file_path}: {e}",
            exc_info=True,
        )
        raise
    except Exception as e:
        logger.error(
            f"{log_prefix}Unexpected error writing relationships file: {e}",
            exc_info=True,
        )
        raise