import uuid
from typing import List, Optional

from fastapi import HTT<PERSON>Exception
from requests import Session
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, Integer, String, DateTime, ForeignKey, select
from sqlalchemy.orm import relationship,joinedload
from sqlalchemy.sql import func
from starlette.concurrency import run_in_threadpool

from app.core import Base, scoped_context, logger
from app.models_old.file import FileModel
from app.schemas.folders import FolderCreate
from app.models_old.upload_file_report_details import UploadFilesReportDetails



class Folder(Base):
    __tablename__ = "folders"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), index=True)
    name = Column(String, nullable=False)
    parent_id = Column(UUID(as_uuid=True), ForeignKey('folders.id'), nullable=True)
    is_file = Column(Integer, default=0)
    is_deleted = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    parent = relationship("Folder", remote_side=[id], backref="children")
    files = relationship("FileModel", back_populates="folder", cascade="all, delete")

class FolderManager:
    @staticmethod
    def get_all_active_folders():
        with scoped_context() as session:
            return session.query(Folder).filter(Folder.is_deleted == 0).all()
        
    @staticmethod
    def get_folder_by_id(folder_id: int) -> Folder:
        """Fetches a folder by its ID."""
        with scoped_context() as db:
            return db.query(Folder).filter(Folder.id == folder_id).first()

    @staticmethod
    def get_file_for_folder(folder_id: int):
        """Fetches file related to a specific folder."""
        with scoped_context() as db:
            return db.query(FileModel).filter(FileModel.folder_id == folder_id).first()
        
    @staticmethod
    def get_folders_by_parent(parent_id: Optional[UUID] = None):
        """Fetches folders by parent_id."""
        with scoped_context() as db:
            if parent_id in (None, "null", "None"):
                return db.query(Folder).filter(Folder.parent_id.is_(None), Folder.is_deleted == 0).all()
            else:
                return db.query(Folder).filter(Folder.parent_id == parent_id, Folder.is_deleted == 0).all()
            
    @staticmethod
    async def main_create_folder(folder: FolderCreate) -> tuple[Folder, str]:
        """Creates a folder and returns the folder and parent S3 path."""
        def _create_folder_sync() -> tuple[Folder, str]:
            from app.services.folder.folder_service import FolderService
            with scoped_context() as db:
                parent_s3_path = ""
                if folder.parent_id:
                    parent = db.query(Folder).filter(
                        Folder.id == folder.parent_id,
                        Folder.is_deleted == 0
                    ).first()
                    if not parent:
                        raise HTTPException(status_code=404, detail="Parent folder not found")
                    parent_s3_path = FolderService.get_folder_path(folder.parent_id)

                new_folder = Folder(
                    name=folder.name,
                    parent_id=folder.parent_id,
                    is_file=folder.is_file
                )
                db.add(new_folder)
                db.commit()
                db.refresh(new_folder)

                return new_folder, parent_s3_path

        return await run_in_threadpool(_create_folder_sync)
    
    @staticmethod
    def get_root_folders() -> List[Folder]:
        with scoped_context() as db:
            return db.query(Folder).filter(Folder.parent_id.is_(None), Folder.is_deleted == 0).all()

    @staticmethod
    def soft_delete_folder_from_db(folder_id: str, db: Session) -> str:
        from app.services.folder.folder_service import FolderService
        from app.services.folder.folder_helper import FolderHelper

        folder = db.query(Folder).filter(Folder.id == folder_id, Folder.is_deleted == 0).first()
        if not folder:
            raise HTTPException(status_code=404, detail="Folder not found")

        s3_path_to_delete = FolderService.get_folder_path(folder_id)

        FolderHelper.recursively_soft_delete(folder, db)
        db.commit()

        return s3_path_to_delete
    
    @staticmethod
    def update_folder_in_db(folder_id: UUID, folder_update, db: Session) -> Folder:
        folder = db.query(Folder).filter(Folder.id == folder_id, Folder.is_deleted == 0).first()
        if not folder:
            raise HTTPException(status_code=404, detail="Folder not found")

        if folder_update.name is not None:
            folder.name = folder_update.name

        if folder_update.parent_id is not None:
            if folder_update.parent_id:
                parent = db.query(Folder).filter(
                    Folder.id == folder_update.parent_id, Folder.is_deleted == 0
                ).first()
                if not parent:
                    raise HTTPException(status_code=404, detail="Parent folder not found")
            folder.parent_id = folder_update.parent_id

        folder.updated_at = func.now()
        db.commit()
        db.refresh(folder)

        return folder
    
    @staticmethod
    def create_folder(folder_name: str, parent_id: Optional[UUID], is_file: int) -> Folder:
        """Creates a new folder in the database."""
        with scoped_context() as db:
            new_folder = Folder(name=folder_name, parent_id=parent_id, is_file=is_file)
            db.add(new_folder)
            db.commit()
            db.refresh(new_folder)
            return new_folder

    @staticmethod
    def get_existing_folder(folder_name: str, parent_id: Optional[UUID]) -> Optional[Folder]:
        """Fetches an existing folder if already present."""
        with scoped_context() as db:
            return db.query(Folder).filter(Folder.name == folder_name, Folder.parent_id == parent_id).first()

    def add_file_record(db_session, file_name, s3_key, folder_id):
        file = FileModel(
            filename=file_name, 
            filepath=s3_key,  
            folder_id=folder_id
        )
        db_session.add(file)
        db_session.commit()
        db_session.refresh(file)
        return file
    
    def add_path_upload_file_reportdetails(db_session, s3_key, folder_id):
        upload_file = UploadFilesReportDetails(
            upload_file_path=s3_key,
            is_analyzed=False,
            is_converted=False,
            is_migrated=False,
            folder_id=folder_id
        )
        db_session.add(upload_file)
        db_session.commit()
        db_session.refresh(upload_file)
        return upload_file
        

    # @staticmethod
    # def get_all_active_folders_with_files():
    #     with scoped_context() as session:
    #         return session.query(Folder).filter(
    #             Folder.is_deleted == 0
    #         ).options(
    #             joinedload(Folder.files)
    #         ).all()


    @staticmethod
    def fetch_active_folders_with_files():
        try:
            with scoped_context() as session:
                return session.query(Folder).filter(
                    Folder.is_deleted == 0
                ).options(
                    joinedload(Folder.files)
                ).all()
        except Exception as e:
            logger.error(f"Error fetching active folders: {e}")
            raise

    @staticmethod
    def get_folder_with_files_by_id(project_id: UUID):
        try:
            with scoped_context() as session:
                return session.query(Folder).filter(
                    Folder.parent_id == project_id,
                    Folder.is_deleted == 0
                ).options(
                    joinedload(Folder.files)
                ).first()
        except Exception as e:
            logger.error(f"Error fetching folder by ID {project_id}: {e}")
            raise

    @staticmethod
    def fetch_folder_by_id_with_files(folder_id: str):
        try:
            with scoped_context() as session:
                return session.query(Folder).filter(
                    Folder.id == folder_id,
                    Folder.is_deleted == 0
                ).options(
                    joinedload(Folder.files)
                ).first()
        except Exception as e:
            logger.error(f"Error fetching folder with ID {folder_id}: {e}")
            raise

    @staticmethod
    def get_parent_id_from_folder(folder_id: UUID) -> Optional[UUID]:
        with scoped_context() as session:
            folder = session.query(Folder).filter(
                Folder.id == folder_id
            ).first()
            return folder.parent_id
