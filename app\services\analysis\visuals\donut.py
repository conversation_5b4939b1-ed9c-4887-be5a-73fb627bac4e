from app.core import logger
from app.core.enums import ChartType, GeneralKeys as GS
from app.core.enums import WorkSheet as WS

class DonutChart:
    @staticmethod
    def check_donut_chart(worksheet):
        try:
            marks = worksheet.findall(WS.MARK.value)

            if not marks or len(marks) != 3:
                return {
                    GS.STATUS.value: False,
                    GS.CHART_TYPE.value: None
                }

            if all(mark.get(GS.CLASS.value, "").strip().lower() == ChartType.PIE.value.lower() for mark in marks):
                return {
                    GS.STATUS.value: True,
                    GS.CHART_TYPE.value: ChartType.DONUT_CHART.value
                }

            return {
                GS.STATUS.value: False,
                GS.CHART_TYPE.value: None
            }

        except Exception as e:
            logger.error(f"Error in check_donut_chart: {e}")
            return {
                GS.STATUS.value: False,
                GS.CHART_TYPE.value: None,
                GS.ERROR.value: str(e)
            }
