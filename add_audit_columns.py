#!/usr/bin/env python3
"""
Add audit columns to report_details table with default values
"""
import psycopg2
from datetime import datetime
import uuid

# Database connection parameters
DB_CONFIG = {
    'host': 'localhost',
    'port': '5432',
    'user': 'postgres',
    'password': 'postgres',
    'database': 'test'
}

def add_audit_columns():
    """Add audit columns to report_details table"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        print("🔧 Adding audit columns to report_details table")
        print("=" * 50)
        
        # Check if columns already exist
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'report_details' 
            AND table_schema = 'biport_dev'
            AND column_name IN ('created_by', 'updated_by', 'created_at', 'updated_at', 'is_deleted')
        """)
        existing_columns = [row[0] for row in cursor.fetchall()]
        
        print(f"Existing audit columns: {existing_columns}")
        
        # Get a sample user ID for default values
        cursor.execute("SELECT id FROM biport_dev.users LIMIT 1")
        sample_user_result = cursor.fetchone()
        sample_user_id = sample_user_result[0] if sample_user_result else str(uuid.uuid4())
        
        print(f"Using sample user ID for defaults: {sample_user_id}")
        
        # Add missing columns
        columns_to_add = [
            ('created_by', f'UUID DEFAULT \'{sample_user_id}\''),
            ('updated_by', f'UUID DEFAULT \'{sample_user_id}\''),
            ('created_at', 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'),
            ('updated_at', 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'),
            ('is_deleted', 'BOOLEAN DEFAULT FALSE')
        ]
        
        for column_name, column_def in columns_to_add:
            if column_name not in existing_columns:
                try:
                    sql = f"ALTER TABLE biport_dev.report_details ADD COLUMN {column_name} {column_def}"
                    print(f"   Adding column: {column_name}")
                    print(f"   SQL: {sql}")
                    cursor.execute(sql)
                    print(f"   ✅ Added {column_name}")
                except Exception as e:
                    print(f"   ❌ Error adding {column_name}: {e}")
            else:
                print(f"   ⏭️  Column {column_name} already exists")
        
        # Commit changes
        conn.commit()
        
        # Verify the columns were added
        print(f"\n🔍 Verifying audit columns...")
        cursor.execute("""
            SELECT column_name, data_type, column_default
            FROM information_schema.columns 
            WHERE table_name = 'report_details' 
            AND table_schema = 'biport_dev'
            AND column_name IN ('created_by', 'updated_by', 'created_at', 'updated_at', 'is_deleted')
            ORDER BY column_name
        """)
        
        audit_columns = cursor.fetchall()
        for col in audit_columns:
            print(f"   ✅ {col[0]} ({col[1]}) - Default: {col[2]}")
        
        print(f"\n✅ Audit columns setup completed!")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error adding audit columns: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_after_changes():
    """Test that the ReportDetail model works after adding audit columns"""
    print(f"\n🧪 Testing ReportDetail model after changes")
    print("=" * 50)
    
    try:
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from app.models.report_details import ReportDetail
        from app.core.session import scoped_context
        
        with scoped_context() as session:
            # Try to query reports
            reports = session.query(ReportDetail).limit(3).all()
            print(f"✅ Successfully queried {len(reports)} reports")
            
            if reports:
                sample_report = reports[0]
                print(f"   Sample report: {sample_report.name}")
                print(f"   ID: {sample_report.id}")
                print(f"   Created by: {sample_report.created_by}")
                print(f"   Created at: {sample_report.created_at}")
                print(f"   Unit tested: {sample_report.unit_tested}")
                print(f"   UAT tested: {sample_report.uat_tested}")
                print(f"   Deployed: {sample_report.deployed}")
        
        print(f"✅ ReportDetail model is working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing model: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("🔧 Setting up Audit Columns for ReportDetail")
    print("=" * 60)
    print("This will add the missing audit columns to make AuditMixin work")
    
    # Step 1: Add audit columns
    if add_audit_columns():
        # Step 2: Test the model
        test_model_after_changes()
        
        print(f"\n🎉 Setup completed successfully!")
        print(f"\n📋 What was done:")
        print(f"   ✅ Added created_by column with default user ID")
        print(f"   ✅ Added updated_by column with default user ID") 
        print(f"   ✅ Added created_at column with current timestamp")
        print(f"   ✅ Added updated_at column with current timestamp")
        print(f"   ✅ Added is_deleted column with default FALSE")
        print(f"   ✅ Verified ReportDetail model works correctly")
        print(f"\n🚀 Now the report status update API should work!")
    else:
        print(f"\n❌ Setup failed. Check the errors above.")

if __name__ == "__main__":
    main()
