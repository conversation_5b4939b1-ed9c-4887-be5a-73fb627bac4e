from app.services import (add_or_update_userReports,
                          process_files_for_migration)
from ..common import *
from app.schemas.migrate import MigarationInput
from app.core import ServiceResponse,BaseService

class MigrationService(BaseService):
    """ Sending Files For Migration """

    @staticmethod
    async def tableau_to_powerbi(request: MigarationInput, custom_zip_name=None) -> ServiceResponse:
        download_links = await process_files_for_migration(
            request.twb_files,
            request.powerbi_structure,
            request.process_id,
            request.s3_input_path,
            custom_zip_name=custom_zip_name
        )
        return ServiceResponse.success(data=download_links, status_code=200)