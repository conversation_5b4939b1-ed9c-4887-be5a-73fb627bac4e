"""renaming report_details files to server_report_details

Revision ID: 1d8c3c7120a4
Revises: a7532629829a
Create Date: 2025-06-11 12:02:20.054911

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1d8c3c7120a4'
down_revision: Union[str, None] = 'a7532629829a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.rename_table('report_details', 'server_report_details')


def downgrade() -> None:
    op.rename_table('server_report_details', 'report_details')
